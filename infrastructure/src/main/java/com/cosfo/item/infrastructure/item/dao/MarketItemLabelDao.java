package com.cosfo.item.infrastructure.item.dao;

import com.cosfo.item.infrastructure.item.dto.MarketItemLabelQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import com.cosfo.item.infrastructure.item.model.MarketItemLabel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品标签表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
public interface MarketItemLabelDao extends IService<MarketItemLabel> {

    List<MarketItemLabel>  selectByNameAndTenantId(String labelName, Long tenantId);

    List<MarketItemLabel> queryMarketItemLabel(MarketItemLabelQueryParam param);

    List<MarketItemLabel> getByIds(List<Long> itemLabelIds);
}
