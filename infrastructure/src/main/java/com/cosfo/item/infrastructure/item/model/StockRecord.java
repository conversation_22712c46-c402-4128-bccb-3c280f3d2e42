package com.cosfo.item.infrastructure.item.model;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 库存变动记录(StockRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-05-05 14:25:49
 */
@Data
@TableName("stock_record")
public class StockRecord implements Serializable {
    private static final long serialVersionUID = 406190882025724226L;
    /**
     * 主键、自增
     */     
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;
    /**
     * stock sku id
     */     
    private Long stockSkuId;
    /**
     * 记录类型：0、下单 1、取消订单 2、售后 3、手动调整 4、自营更换三方 5、三方更换自营 6、新建商品
     */     
    private Integer type;
    /**
     * 变更前数量
     */     
    private Integer beforeAmount;
    /**
     * 变更数量
     */     
    private Integer changeAmount;
    /**
     * 变更后数量
     */     
    private Integer afterAmount;
         
    private LocalDateTime updateTime;
         
    private LocalDateTime createTime;
    /**
     * 记录编号
     */     
    private String recordNo;

}

