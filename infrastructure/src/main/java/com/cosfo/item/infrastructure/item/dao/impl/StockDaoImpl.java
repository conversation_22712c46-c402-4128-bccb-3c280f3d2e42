package com.cosfo.item.infrastructure.item.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.item.dao.StockDao;
import com.cosfo.item.infrastructure.item.dto.StockQueryParam;
import com.cosfo.item.infrastructure.item.mapper.StockMapper;
import com.cosfo.item.infrastructure.item.model.Stock;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 库存表(Stock)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-05 14:19:51
 */
@Service
public class StockDaoImpl extends ServiceImpl<StockMapper, Stock> implements StockDao {

    @Override
    public List<Stock> listByParam(StockQueryParam param) {
        return list(buildQueryWrapper(param));
    }

    private LambdaQueryWrapper<Stock> buildQueryWrapper(StockQueryParam param) {
        LambdaQueryWrapper<Stock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getItemId()), Stock::getItemId, param.getItemId());
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), Stock::getTenantId, param.getTenantId());
        queryWrapper.in(CollectionUtil.isNotEmpty(param.getItemIds()),Stock::getItemId,param.getItemIds());

        return queryWrapper;
    }

    @Override
    public Stock preUpdateQuery(Long tenantId, Long itemId) {
        return getBaseMapper().preUpdateQuery(tenantId, itemId);
    }

    @Override
    public Integer increaseStock(Long id, Integer addAmount) {
        return getBaseMapper().increaseStock(id, addAmount);
    }
}
