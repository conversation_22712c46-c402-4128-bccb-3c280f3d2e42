package com.cosfo.item.infrastructure.item.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.item.model.MarketAreaItem;
import com.cosfo.item.infrastructure.item.mapper.MarketAreaItemMapper;
import com.cosfo.item.infrastructure.item.dao.MarketAreaItemDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.item.model.MarketCombineItemMapping;
import com.cosfo.item.infrastructure.price.model.MarketAreaItemMapping;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 城市商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Service
public class MarketAreaItemDaoImpl extends ServiceImpl<MarketAreaItemMapper, MarketAreaItem> implements MarketAreaItemDao {

    @Override
    public Set<Long> listIdsByItemIds(List<Long> itemIds) {
        LambdaQueryWrapper<MarketAreaItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MarketAreaItem::getItemId,itemIds);
        wrapper.select(MarketAreaItem::getId);
        return baseMapper.selectObjs(wrapper).stream().map(o -> (Long) o).collect(Collectors.toSet());
    }
}
