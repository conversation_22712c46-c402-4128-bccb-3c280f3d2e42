package com.cosfo.item.starter.plugin;


import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;

import java.util.Collections;


/**
 * 代码生成器
 *
 * @author: Cathy
 */
public class CodeGenerator4Offline {
    public static void main(String[] args) {
        //*** 修改 ***
        String moduleName = "offline";
        String tablename = "self_goods_cost_price";//data_synchronization_information
        String author = "Cathy";
        //*** 修改 ***

        String projectPath = System.getProperty("user.dir");
        FastAutoGenerator.create("*****************************************************************************************************************************************", "test", "xianmu619")
                .globalConfig(builder -> {
        builder.author(author) // 设置作者
                .fileOverride() // 覆盖已生成文件
                .outputDir(projectPath + "/infrastructure" + "/src/main/java"); // 指定输出目录
        }).packageConfig(builder -> {
                builder.parent("com.cosfo.item.infrastructure") // 设置父包名
                .moduleName(moduleName) // 设置父包模块名
                .entity("model")
                .service("dao")
                .serviceImpl("dao.impl")
                .pathInfo(Collections.singletonMap(OutputFile.mapperXml, projectPath + "/infrastructure" + "/src/main/resources/mapper/" + moduleName)); // 设置mapperXml生成路径
        }).strategyConfig(builder -> {
        builder.addInclude(tablename) // 设置需要生成的表名
                .entityBuilder()
                .enableLombok()
                .serviceBuilder()
                .formatServiceFileName("%sDao")
                .formatServiceImplFileName("%sDaoImpl")
                .mapperBuilder ().enableMapperAnnotation ();
        // 设置过滤表前缀
        }).templateConfig(t -> t.controller(null))
    //                .templateEngine(new FreemarkerTemplateEngine()) // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .execute();
    }
}
