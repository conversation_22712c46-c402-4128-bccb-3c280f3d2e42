package com.cosfo.item.infrastructure.skuPreferential.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceOccupy;
import com.cosfo.item.infrastructure.skuPreferential.mapper.ProductSkuPreferentialCostPriceOccupyMapper;
import com.cosfo.item.infrastructure.skuPreferential.dao.ProductSkuPreferentialCostPriceOccupyDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 省心定库存占用表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Service
public class ProductSkuPreferentialCostPriceOccupyDaoImpl extends ServiceImpl<ProductSkuPreferentialCostPriceOccupyMapper, ProductSkuPreferentialCostPriceOccupy> implements ProductSkuPreferentialCostPriceOccupyDao {

    @Override
    public List<ProductSkuPreferentialCostPriceOccupy> listByOrderIdAndSkuIds(Long tenantId,Long orderId, List<Long> skuIds) {
        LambdaQueryWrapper<ProductSkuPreferentialCostPriceOccupy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPreferentialCostPriceOccupy::getTenantId, tenantId);
        queryWrapper.in(ProductSkuPreferentialCostPriceOccupy::getSkuId, skuIds);
        queryWrapper.in(ProductSkuPreferentialCostPriceOccupy::getOrderId, orderId);
        return list (queryWrapper);
    }

    @Override
    public Integer optOccupyQuantity(Long id, int i) {
        return getBaseMapper ().optOccupyQuantity(id,i);
    }
}
