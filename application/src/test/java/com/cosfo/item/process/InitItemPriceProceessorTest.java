package com.cosfo.item.process;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemParam;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.web.scheduler.InitItemPriceProceessor;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/7/4 14:52
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class InitItemPriceProceessorTest {
    @Autowired
    private InitItemPriceProceessor priceProceessor;

    @Autowired
    private MarketItemDao itemDao;

    @Test
    public void processResult(){
        String msg = "{\"items\":\"1234567\"}";
        Map<String, String> map = JSON.parseObject(msg, Map.class);
        List<Long> itemIds = Arrays.stream(map.get("items").split(",")).map(Long::valueOf).collect(Collectors.toList());
        MarketItemParam params = new MarketItemParam();
        params.setItemIds(itemIds);
        List<MarketItem> byIds = itemDao.listByParam(params);
        log.info("结果：{}", JSON.toJSONString(byIds));
    }
}
