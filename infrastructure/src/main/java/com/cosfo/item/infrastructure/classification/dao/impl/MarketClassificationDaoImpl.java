package com.cosfo.item.infrastructure.classification.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.classification.dao.MarketClassificationDao;
import com.cosfo.item.infrastructure.classification.mapper.MarketClassificationMapper;
import com.cosfo.item.infrastructure.classification.model.MarketClassification;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationQueryParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 商品分类表(MarketClassification)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-04 16:38:00
 */
@Service
public class MarketClassificationDaoImpl extends ServiceImpl<MarketClassificationMapper, MarketClassification> implements MarketClassificationDao {

    @Override
    public List<MarketClassification> listByParam(MarketClassificationQueryParam param) {
        return list(buildQueryWrapper(param));
    }

    private LambdaQueryWrapper<MarketClassification> buildQueryWrapper(MarketClassificationQueryParam param) {
        LambdaQueryWrapper<MarketClassification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MarketClassification::getTenantId, param.getTenantId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getIds()), MarketClassification::getId, param.getIds());
        queryWrapper.eq(Objects.nonNull(param.getParentId()), MarketClassification::getParentId, param.getParentId());
        queryWrapper.eq(StringUtils.isNotEmpty(param.getName()), MarketClassification::getName, param.getName());
        queryWrapper.eq(Objects.nonNull(param.getId()), MarketClassification::getId, param.getId());
        return queryWrapper;
    }

    @Override
    public Integer selectMaxSort(Long parentId, Long tenantId) {
        return getBaseMapper().selectMaxSort(parentId, tenantId);
    }
}
