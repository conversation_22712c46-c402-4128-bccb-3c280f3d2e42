package com.cosfo.item.infrastructure.item.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品item扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketItemDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * marketitemid
     */
    private Long marketItemId;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 起售规格
     */
    private Integer baseSaleUnit;

    /**
     * 0 不展示平均价  1 展示平均价
     */
    private Integer averagePriceFlag;

    /**
     *  是否加入样本池 0 不加入 1加入
     */
    private Integer samplePool;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 外部id
     */
    private Long outId;

    /**
     * 商品标签
     */
    private String itemLabel;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;

    /**
     * 自动补差售后量阈值
     */
    private Integer minAutoAfterSaleThreshold;
    /**
     * 销售属性说明
     */
    private String salePropertyDesc;
}
