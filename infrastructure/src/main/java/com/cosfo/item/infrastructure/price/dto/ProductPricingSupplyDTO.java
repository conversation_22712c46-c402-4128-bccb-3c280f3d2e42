package com.cosfo.item.infrastructure.price.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ProductPricingSupplyDTO {

    /**
     * 城市报价单主键Id
     */
    private Integer productPricingSupplyCityMappingId;

    /**
     * 城市Id
     */
    private Integer cityId;

    /**
     * 报价方式 0、指定价  1,随鲜沐商城价
     */
    private Integer type;

    /**
     * 0、成本供价 1、报价单供价
     */
    private Integer supplyType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 浮动值
     */
    private BigDecimal strategyValue;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 供应SKU id
     */
    private Long skuId;

    /**
     * 供应商tenantId
     */
    private Long supplyTenantId;
}
