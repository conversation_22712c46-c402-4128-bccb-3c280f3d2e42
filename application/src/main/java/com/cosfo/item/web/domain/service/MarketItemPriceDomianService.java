package com.cosfo.item.web.domain.service;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.PriceStrategyTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cosfo.item.common.dto.LadderPriceDTO;
import com.cosfo.item.common.dto.PriceRangeDTO;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.common.utils.PriceUtil;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceDao;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyDao;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import com.cosfo.item.infrastructure.price.model.MarketItemPrice;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.web.domain.converter.PriceConvert;
import com.cosfo.item.web.domain.vo.MarketItemPriceVO;
import com.cosfo.item.web.domain.vo.PriceDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.annotation.Target;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * item价格
 */
@Component
@Slf4j
public class MarketItemPriceDomianService {

    @Autowired
    private MarketItemPriceDao priceDao;
    @Autowired
    private MarketItemPriceStrategyDao priceStrategyDao;
    @Autowired
    private MarketItemPriceStrategyDomainService priceStrategyDomainService;


    /**
     * 批量查询商品价格区间
     *
     * @return
     */
    public Map<Long, PriceRangeDTO> listRangePriceByItemIds(Long tenantId, List<Long> itemIds) {
        List<PriceRangeDTO> marketItemPrices = priceDao.queryPriceRangeGroupByItemId(tenantId, itemIds);
        if(CollectionUtils.isEmpty (marketItemPrices)) {
            return Collections.emptyMap ();
        }
        Map<Long, PriceRangeDTO> map = marketItemPrices.stream().collect(Collectors.toMap(PriceRangeDTO::getMarketItemId, Function.identity()));

        List<MarketItemPriceStrategy> marketItemPriceStrategies = priceStrategyDao.listByItemIdsAndStrategyType (tenantId, itemIds, PriceStrategyTypeEnum.ASSIGN.getCode ());
        Map<Long, List<MarketItemPriceStrategy>> strategyMap;
        if(CollectionUtils.isNotEmpty (marketItemPriceStrategies)) {
            strategyMap = marketItemPriceStrategies.stream ().collect (Collectors.groupingBy (MarketItemPriceStrategy::getItemId));
        } else {
            strategyMap = Collections.emptyMap ();
        }
        map.forEach((itemId, price) -> {
            Set<BigDecimal> ladderPriceDTOS = new HashSet<> ();
            ladderPriceDTOS.add (price.getMinPrice ());
            ladderPriceDTOS.add (price.getMaxPrice ());
            List<MarketItemPriceStrategy> priceStrategies = strategyMap.get (itemId);
            if(CollectionUtils.isNotEmpty (priceStrategies)){
                priceStrategies.stream().filter (e-> StringUtils.isNotBlank (e.getPriceStrategyValue ())).forEach (p-> ladderPriceDTOS.addAll (JSON.parseArray (p.getPriceStrategyValue (), LadderPriceDTO.class).stream().map (LadderPriceDTO::getPrice).collect(Collectors.toSet ())));
            }
            BigDecimal min = ladderPriceDTOS.stream ().distinct ().min (BigDecimal::compareTo).get ();
            BigDecimal max = ladderPriceDTOS.stream ().distinct ().max (BigDecimal::compareTo).get ();
            price.setMinPrice (min);
            price.setMaxPrice (max);
            price.setPriceStr (PriceUtil.calPriceRange(min,max));
        });
        return map;
    }

    /**
     * 查询item不同报价类型的价格
     */
    public List<MarketItemPriceVO> listAllPriceByItemIds(Long tenantId, List<Long> itemIds) {
        List<MarketItemPriceVO> result = new ArrayList<>();
        List<MarketItemPrice> marketItemPrices = priceDao.listByItemIds(tenantId, itemIds);
        Map<Long, List<MarketItemPrice>> marketItemIdMap = marketItemPrices.stream().collect(Collectors.groupingBy(MarketItemPrice::getMarketItemId));
        marketItemIdMap.forEach((marketItemId, marketItemList) -> {
            MarketItemPriceVO vo = new MarketItemPriceVO();
            vo.setMarketItemId(marketItemId);
            Map<Integer, List<MarketItemPrice>> targetMap = marketItemList.stream().collect(Collectors.groupingBy(MarketItemPrice::getTargetType));
            if (Objects.nonNull(targetMap.get(PriceTargetTypeEnum.TENANT.getCode()))) {
                vo.setTenantPrice(PriceConvert.marketItemPrice2PriceDetailVO(targetMap.get(PriceTargetTypeEnum.TENANT.getCode()).stream().findFirst().orElse(new MarketItemPrice())));
            }
            vo.setStorePriceList(getMap(targetMap.get(PriceTargetTypeEnum.STORE.getCode())));
            vo.setAreaNoPriceList(getMap(targetMap.get(PriceTargetTypeEnum.AREA_NO.getCode())));
            result.add(vo);
        });
        return result;
    }

    /**
     * 查询item+target的价格
     * PriceDetailVO 可能为 门店价格/租户价格/区域价格
     * 如果对应target没有价格，向下取(门店价格 -> 租户价格 ; 区域价格 -> 区域价格)
     */
    public Map<Long, PriceDetailVO> listAllPriceByItemIds(Long tenantId, List<Long> itemIds, Long targetId, Integer targetType) {
        Map<Long, List<LadderPriceDTO>> ladderPriceMap = priceStrategyDomainService.getLadderPriceMap (tenantId, itemIds, targetId);
        List<MarketItemPrice> marketItemPrices = priceDao.ListByItemIdAndTarget(tenantId, itemIds, targetId, targetType);
        Map<Long, PriceDetailVO> targetPrice = marketItemPrices.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(MarketItemPrice::getMarketItemId, PriceConvert::marketItemPrice2PriceDetailVO, (k1, k2) -> k1));
        if (PriceTargetTypeEnum.STORE.getCode().equals(targetType)) {
            Set<Long> targetItemIds = targetPrice.keySet();
            List<Long> queryTenantItemIds = itemIds.stream()
                .filter(itemId -> !targetItemIds.contains(itemId))
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(queryTenantItemIds)) {
                List<MarketItemPrice> tenantPriceList = priceDao.ListByItemIdAndTarget(tenantId, queryTenantItemIds, tenantId, PriceTargetTypeEnum.TENANT.getCode());
                targetPrice.putAll(tenantPriceList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(MarketItemPrice::getMarketItemId, PriceConvert::marketItemPrice2PriceDetailVO, (k1, k2) -> k1)));
            }
        }
        if(MapUtil.isNotEmpty (ladderPriceMap)){
            targetPrice.forEach ((itemId, detailVO) -> {
                List<LadderPriceDTO> ladderPriceDTOS = ladderPriceMap.get (itemId);
                if (CollectionUtils.isNotEmpty (ladderPriceDTOS)) {
                    detailVO.setLadderPrice (JSON.toJSONString (ladderPriceDTOS));
                    detailVO.setLadderPriceDTOS (ladderPriceDTOS);
                }
            });
        }
        return targetPrice;
    }

    private Map<Long, PriceDetailVO> getMap(List<MarketItemPrice> priceList) {
        if (CollectionUtils.isEmpty(priceList)) {
            return Collections.emptyMap();
        }
        Map<Long, MarketItemPrice> collect = priceList.stream().collect(Collectors.toMap(MarketItemPrice::getTargetId, Function.identity()));
        Map<Long, PriceDetailVO> map = new HashMap<>();
        collect.forEach((key, value) -> map.put(key, PriceConvert.marketItemPrice2PriceDetailVO(value)));
        return map;
    }


}
