package com.cosfo.item.common.constants;

/**
 * <AUTHOR>
 */
public class RocketMqConstant {

    /**
     * Topic
     */
    public interface Topic {
        String MYSQL_BINLOG_SAAS = "cosfo-mysql-binlog";
        String MYSQL_BINLOG_SUMMERFARM = "mysql-binlog";
        String SUPPLY_PRICE = "topic_item_supply_price";
        String ITEM_PRICE = "topic_item_price";
        String ITEM_ONSALE = "topic_item_onsale";
        String TOPIC_SELF_GOODS_COST_PRICE = "topic_item_self_goods_cost_price";
    }

    /**
     * 消费组
     */
    public interface ConsumeGroup {
        String MYSQL_BINLOG_SUMMERFARM_ITEM_CENTER = "GID_item_summerfarm_binlog";
        String MYSQL_BINLOG_COSFO_ITEM_CENTER = "GID_item_cosfo_binlog";
        String ITEM_SUPPLY_PRICE = "GID_item_supply_price";
        String ITEM_PRICE = "GID_item_price";
        String ITEM_ONSALE = "GID_item_onsale";
        String SELF_GOODS_COST_PRICE = "GID_item_self_goods_cost_price";
    }

    /**
     * tag
     */
    public interface Tag {
        String XIANMU_TABLENAMES_TAG_4_COST_PRICE = "area_sku" + "||" + "major_price" + "||" + "fence";
//                + "||" + "ad_code_msg";
        String COSFO_TABLENAMES_TAG_4_COST_PRICE = "market_item" + "||" + "merchant_address" + "||" + "market_item_price_strategy"+ "||" + "market_combine_item_mapping"+ "||" + "cost_price" + "||" + "product_pricing_supply_city_mapping" + "||"+ "product_pricing_supply"+ "||" + "market_item_unfair_price_strategy"+ "||" + "merchant_store_group_mapping"+ "||" + "product_sku_preferential_cost_price"+ "||" + "tenant_common_config";
        String SUPPLY_PRICE = "item_supply_price";
        String ITEM_PRICE = "item_price";
        String ITEM_ONSALE = "item_onsale";
        String SELF_GOODS_COST_PRICE = "self_goods_cost_price";
    }
}
