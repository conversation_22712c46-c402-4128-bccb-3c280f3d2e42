package com.cosfo.item.web.skuPreferential.provider;

import com.cofso.page.PageResp;
import com.cofso.preferential.client.provider.ProductSkuPreferentialCostPriceProvider;
import com.cofso.preferential.client.req.OptAvailableQuantityReq;
import com.cofso.preferential.client.req.ProductPreferentialCostPriceCommandReq;
import com.cofso.preferential.client.req.ProductSkuPreferentialDeleteReq;
import com.cofso.preferential.client.req.ProductSkuPreferentialQueryReq;
import com.cofso.preferential.client.resp.ProductSkuCityPreferentialCostPriceResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialBasicResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialPageResp;
import com.cosfo.item.web.skuPreferential.domain.SkuPreferentialDomainService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2024-02-21
 * @Description:
 */
@DubboService
@Slf4j
public class ProductSkuPreferentialCostPriceProviderImpl implements ProductSkuPreferentialCostPriceProvider {

    @Resource
    private SkuPreferentialDomainService skuPreferentialDomainService;

    @Override
    public DubboResponse<PageResp<ProductSkuPreferentialPageResp>> pagePreferentialCostPrice(@Valid ProductSkuPreferentialQueryReq productSkuPreferentialQueryReq) {
        return DubboResponse.getOK(skuPreferentialDomainService.pagePreferentialCostPrice(productSkuPreferentialQueryReq));
    }

    @Override
    public DubboResponse<ProductSkuPreferentialBasicResp> queryBasicData(ProductSkuPreferentialQueryReq productSkuPreferentialQueryReq) {
        return DubboResponse.getOK(skuPreferentialDomainService.queryBasicData(productSkuPreferentialQueryReq));
    }

    @Override
    public DubboResponse<Boolean> deleteSkuPreferentialCostPrice(@Valid ProductSkuPreferentialDeleteReq deleteReq) {
        return DubboResponse.getOK(skuPreferentialDomainService.deleteSkuPreferentialCostPrice(deleteReq));
    }

    @Override
    public DubboResponse<Boolean> upsertSkuPreferentialCostPrice(@Valid ProductPreferentialCostPriceCommandReq commandReq) {
        return DubboResponse.getOK(skuPreferentialDomainService.lockUpsertSkuPreferentialCostPrice(commandReq));
    }

    @Override
    public DubboResponse<List<ProductSkuCityPreferentialCostPriceResp>> queryCityPreferentialCostPrice(Long tenantId, Long skuId, List<Long> cityIds) {
        List<ProductSkuCityPreferentialCostPriceResp> list = skuPreferentialDomainService.queryCityPreferentialCostPrice(tenantId, skuId, cityIds);
        return DubboResponse.getOK(list);
    }

    @Override
    public DubboResponse<List<ProductSkuCityPreferentialCostPriceResp>> queryPreferentialCostPriceBySkuIds4City(Long tenantId, Long cityId, Set<Long> skuIds) {
        List<ProductSkuCityPreferentialCostPriceResp> list = skuPreferentialDomainService.queryPreferentialCostPriceBySkuIds4City(tenantId, cityId,skuIds);
        return DubboResponse.getOK(list);
    }

    @Override
    public DubboResponse<List<ProductSkuPreferentialCostPriceRangeResp>> queryPreferentialCostPriceRange(Long tenantId, Map<Long,List<Long>> skuIdCityIdMap) {
        List<ProductSkuPreferentialCostPriceRangeResp> list = skuPreferentialDomainService.queryPreferentialCostPriceRange(tenantId, skuIdCityIdMap);
        return DubboResponse.getOK(list);
    }

    @Override
    public DubboResponse occupyAvailableQuantity(Long tenantId, Long cityId, Long orderId, List<OptAvailableQuantityReq> req) {
        skuPreferentialDomainService.occupyAvailableQuantity(tenantId, cityId,orderId,req);
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse releaseAvailableQuantity(Long tenantId, Long orderId,List<OptAvailableQuantityReq> req ) {
        skuPreferentialDomainService.releaseAvailableQuantity(tenantId,orderId,req);
        return DubboResponse.getOK();
    }
}
