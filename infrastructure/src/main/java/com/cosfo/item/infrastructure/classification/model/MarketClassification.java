package com.cosfo.item.infrastructure.classification.model;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 商品分类表(MarketClassification)实体类
 *
 * <AUTHOR>
 * @since 2023-05-04 16:37:54
 */
@Data
@TableName("market_classification")
public class MarketClassification implements Serializable {
    private static final long serialVersionUID = -56290747954179742L;
         
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;
    /**
     * 分类名称
     */     
    private String name;
    /**
     * 分类图标
     */     
    private String icon;
    /**
     * 上级分类id
     */     
    private Long parentId;
    /**
     * 排序值
     */     
    private Integer sort;
    /**
     * 创建时间
     */     
    private LocalDateTime createTime;
    /**
     * 更新时间
     */     
    private LocalDateTime updateTime;

}

