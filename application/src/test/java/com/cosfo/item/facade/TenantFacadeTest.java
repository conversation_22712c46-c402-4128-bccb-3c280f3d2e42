package com.cosfo.item.facade;

import com.alibaba.fastjson.JSON;
import com.cosfo.item.web.domain.vo.TenantVO;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.facade.TenantOldFacade;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-08-03
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class TenantFacadeTest {

    @Autowired
    private TenantOldFacade tenantFacade;

    @Autowired
    private TenantFacade tenantFacadeNew;

    @Test
    public void getTenantByAdminId() {
        Long adminId = 4599L;
        TenantVO data = tenantFacade.getTenantByAdminId(adminId);
        TenantVO newData = tenantFacadeNew.getTenantByAdminId(adminId);
        System.err.println(JSON.toJSONString(data).equals(JSON.toJSONString(newData)));
    }

    @Test
    public void listAllTenant() {
        List<TenantVO> data = tenantFacade.listAllTenant();
        List<TenantVO> newData = tenantFacadeNew.listAllTenant();
        System.err.println(JSON.toJSONString(data).equals(JSON.toJSONString(newData)));
    }

    @Test
    public void listAddress() {
        Long tenantId = 2L;
        List<String> data = tenantFacade.listAddress(tenantId);
        List<String> newData = tenantFacadeNew.listAddress(tenantId);
        System.err.println(JSON.toJSONString(data).equals(JSON.toJSONString(newData)));

        tenantId = 1024L;
        data = tenantFacade.listAddress(tenantId);
        newData = tenantFacadeNew.listAddress(tenantId);
        System.err.println(JSON.toJSONString(data).equals(JSON.toJSONString(newData)));
    }

    @Test
    public void getTenantByTenantId() {
        Long tenantId = 2L;
        TenantVO data = tenantFacade.getTenantByTenantId(tenantId);
        TenantVO newData = tenantFacadeNew.getTenantByTenantId(tenantId);
        System.err.println(JSON.toJSONString(data).equals(JSON.toJSONString(newData)));
    }

}
