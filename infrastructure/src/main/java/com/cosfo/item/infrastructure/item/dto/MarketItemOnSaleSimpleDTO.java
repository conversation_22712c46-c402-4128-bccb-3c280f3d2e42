package com.cosfo.item.infrastructure.item.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketItemOnSaleSimpleDTO {

    /**
     * sku_id
     */
    private Long skuId;

    /**
     * 0 下架 1 上架
     */
    private Integer onSale;

    /**
     * 商品id
     */
    private Long id;
}
