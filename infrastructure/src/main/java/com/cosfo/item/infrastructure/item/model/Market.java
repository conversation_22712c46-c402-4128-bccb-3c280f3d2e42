package com.cosfo.item.infrastructure.item.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 销售商品(Market)实体类
 *
 * <AUTHOR>
 * @since 2023-04-27 14:03:14
 */
@Data
@TableName("market")
public class Market implements Serializable {
    private static final long serialVersionUID = -71520281877228448L;
    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户Id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;
    /**
     * 0、已删除 1、正在使用
     */
    private Integer deleteFlag;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 最后编辑人
     */
    private Long editUserId;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 商品描述 文字
     */
    private String descriptionString;
}

