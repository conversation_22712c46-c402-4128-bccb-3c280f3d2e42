package com.cosfo.item.infrastructure.item.model;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 城市商品表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketAreaItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * item表id
     */
    private Long itemId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 0 下架 1 上架
     */
    private Integer onSale;

    /**
     * 已废弃
     */
    private Integer warehouseType;

    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;

    /**
     * 已废弃
     */
    private Integer deliveryType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     */
    private Integer priceType;


}
