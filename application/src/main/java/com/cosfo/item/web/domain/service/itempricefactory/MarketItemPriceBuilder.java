package com.cosfo.item.web.domain.service.itempricefactory;

import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceLogDTO;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyVO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.UnfairPriceStrategyVO;

import java.util.List;
import java.util.Map;

public interface MarketItemPriceBuilder {
    MarketItemPriceStrategyEnum.StrategyTypeEnum getSupportType();

    /**
     * 获取价格
     * @return
     */
    List<MarketItemPriceLogDTO> buildMarketItemPriceLogDTOS(List<MarketItemPriceStrategyVO> strategyVOS, MarketItem marketItem, List<MerchantStoreAddressVO> merchantStoreAddressVOS, UnfairPriceStrategyVO unfairPriceStrategyVO);
}
