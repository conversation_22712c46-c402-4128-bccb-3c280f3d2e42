package com.cosfo.item.web.provider;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.enums.MTypeEnum;
import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.req.ItemNoGoodsSupplyPriceReq;
import com.cofso.item.client.req.MarketItemCommonQueryReq;
import com.cofso.item.client.req.MarketItemWithClassificationQueryReq;
import com.cofso.item.client.req.PageMarketItemQueryReq;
import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.*;
import com.cofso.item.client.resp.BatchChangNoGoodsSupplyPriceVO;
import com.cofso.page.PageResp;
import com.cosfo.item.web.domain.dto.MarketItemCommonQueryDTO;
import com.cosfo.item.web.domain.dto.MarketItemOnSaleInputDTO;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.provider.converter.MarketConvert;
import com.cosfo.item.web.provider.converter.MarketItemConverter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@DubboService
@Slf4j
public class MarketItemProviderImpl implements MarketItemProvider {

    @Resource
    private ItemDomainService itemDomainService;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Override
    public DubboResponse<PageResp<MarketItemWithClassificationResp>> queryItemWithClassification(MarketItemWithClassificationQueryReq marketItemWithClassificationQueryReq) {
        return DubboResponse.getOK(itemDomainService.queryItemWithClassification(marketItemWithClassificationQueryReq));
    }

    @Override
    public DubboResponse<PageResp<PageMarketItemResp>> pageMarketItem(@Valid PageMarketItemQueryReq req) {
        return DubboResponse.getOK(itemDomainService.pageMarketItem(req));
    }

    /**
     * item page
     * @param req
     * @return
     */
    @Override
    public DubboResponse<PageResp<MarketItemInfoResp>> queryMarketItemList(MarketItemCommonQueryReq req) {
        MarketItemCommonQueryDTO queryDTO = MarketConvert.INSTANCE.convert2QueryDto(req);
        Page<MarketItemVO> pageInfo = itemDomainService.queryMarketItemList(queryDTO);
        return DubboResponse.getOK(PageResp.toPageList(MarketConvert.INSTANCE.convert2Items(pageInfo.getRecords()),
                Math.toIntExact(pageInfo.getTotal()), queryDTO.getPageNum(), queryDTO.getPageSize()));
    }


    @Override
    public DubboResponse<Map<Long, List<MarketItemInfoResp>>> queryAssociatedItemBySkuIds(Set<Long> skuIds, Long tenantId){
        List<MarketItemVO> marketItems = itemDomainService.queryAssociatedItemBySkuIds (skuIds, tenantId);
        if(CollectionUtils.isEmpty (marketItems)){
            return DubboResponse.getOK(Collections.emptyMap ());
        }else {
            Map<Long, List<MarketItemInfoResp>> result =  new HashMap<> ();
            Map<Long, List<MarketItemVO>> map = marketItems.stream ().collect (Collectors.groupingBy (MarketItemVO::getSkuId));
            map.forEach ((skuid,list)-> result.put (skuid, MarketConvert.INSTANCE.convert2Items(list)));
            return DubboResponse.getOK (result);
        }
    }

    @Override
    public DubboResponse saleMarketItemInfoBySkuIds(Long tenantId, Set<Long> skuIds, OnSaleTypeEnum onSaleTypeEnum) {
        List<MarketItemVO> marketItems = itemDomainService.queryAssociatedItemBySkuIds (skuIds, tenantId);
        if(CollectionUtils.isNotEmpty (marketItems)){
            MarketItemOnSaleInputDTO dto = new MarketItemOnSaleInputDTO ();
            dto.setItemIds(marketItems.stream ().map (MarketItemVO::getId).collect(Collectors.toList()));
            dto.setOnSale(onSaleTypeEnum.getCode ());
            dto.setTenantId(tenantId);
            itemDomainService.batchChangOnSale(dto);
        }
        return DubboResponse.getOK();
    }

    /**
     * @param tenantId
     * @param itemCode
     * @return
     */
    @Override
    public DubboResponse<List<MarketItemOnsaleStrategyMappingResp>> queryOnsaleStrategyByItemCode(Long tenantId,String itemCode) {
        List<MarketItemOnsaleStrategyMappingVO> marketItemOnsaleStrategyVOS = itemDomainService.queryOnsaleStrategyByItemCode (tenantId, itemCode);
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2OnSaleRespList(marketItemOnsaleStrategyVOS));
    }
    /**
     * @param tenantId
     * @param itemCodes
     * @return
     */
    @Override
    public DubboResponse<Map<String,List<MarketItemOnsaleStrategyMappingResp>>> queryOnsaleStrategyByItemCodeBatch(Long tenantId,List<String> itemCodes) {
        Map<String,List<MarketItemOnsaleStrategyMappingVO>> map = itemDomainService.queryOnsaleStrategyByItemCodeBatch (tenantId, itemCodes);
        if(MapUtils.isEmpty (map)){
            return DubboResponse.getOK(Collections.emptyMap ());
        }
        Map<String,List<MarketItemOnsaleStrategyMappingResp>> result = new HashMap<> ();
        map.forEach ((k,v)-> result.put (k,MarketConvert.INSTANCE.convert2OnSaleRespList(v)));
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<Map<String, List<MarketItemOnsaleStrategyMappingResp>>> queryOnsaleStrategyByCommonQuery(OnsaleStrategyCommonQueryReq onsaleStrategyCommonQueryReq) {
        Map<String,List<MarketItemOnsaleStrategyMappingVO>> map = itemDomainService.queryOnsaleStrategyByCommonQuery (onsaleStrategyCommonQueryReq);
        if(MapUtils.isEmpty (map)){
            return DubboResponse.getOK(Collections.emptyMap ());
        }
        Map<String,List<MarketItemOnsaleStrategyMappingResp>> result = new HashMap<> ();
        map.forEach ((k,v)-> result.put (k,MarketConvert.INSTANCE.convert2OnSaleRespList(v)));
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse updateItemCode(String itemCode, Long marketItemId, Long tenantId) {
        itemDomainService.updateItemCode (itemCode,marketItemId, tenantId);
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<BatchChangNoGoodsSupplyPriceResp> batchChangNoGoodsSupplyPrice(Long tenantId , List<ItemNoGoodsSupplyPriceReq> list) {
        BatchChangNoGoodsSupplyPriceVO resultVO = itemDomainService.batchChangNoGoodsSupplyPrice (tenantId, list);
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Resp(resultVO));
    }

    @Override
    public DubboResponse<Integer> countOnsaleItem(Long tenantId) {
        return DubboResponse.getOK(itemDomainService.countOnsaleItem (tenantId));
    }

    @Override
    public DubboResponse<Map<Long, Long>> querySkuIdByItemIds(Long tenantId, List<Long> itemIds) {
        return DubboResponse.getOK(itemDomainService.querySkuIdByItemIds (tenantId,itemIds));
    }

    @Override
    public DubboResponse<Map<Long, OnSaleTypeEnum>> queryItemOnsaleFlagByItemIds(Long tenantId, List<Long> itemIds) {
        return DubboResponse.getOK(itemDomainService.queryItemOnsaleFlagByItemIds (tenantId,itemIds,Collections.emptyList ()));
    }

    @Override
    public DubboResponse<Map<Long, OnSaleTypeEnum>> queryItemOnsaleFlagByItemIdLimitRange(Long tenantId, List<Long> itemIds, List<Long> targetIds) {
        return DubboResponse.getOK(itemDomainService.queryItemOnsaleFlagByItemIds (tenantId,itemIds,targetIds));
    }

    @Override
    public DubboResponse<Map<Long, OnSaleTypeEnum>> queryItemOnsaleFlagBySkuIds(Long tenantId, List<Long> skuIds) {
        return DubboResponse.getOK(itemDomainService.queryItemOnsaleFlagBySkuIds (tenantId,skuIds,Collections.emptyList ()));
    }

    @Override
    public DubboResponse<Map<Long, OnSaleTypeEnum>> queryItemOnsaleFlagBySkuIdsLimitRange(Long tenantId, List<Long> skuIds, List<Long> targetIds) {
        return DubboResponse.getOK(itemDomainService.queryItemOnsaleFlagBySkuIds (tenantId,skuIds,targetIds));
    }

    @Override
    public DubboResponse<Map<Long, MTypeEnum>> queryItemMTypeFlagBySkuIds(Long tenantId, List<Long> skuIds) {
        return DubboResponse.getOK(itemDomainService.queryItemMTypeFlagBySkuIds (tenantId,skuIds,Collections.emptyList ()));
    }

    @Override
    public DubboResponse<Map<Long, MTypeEnum>> queryItemMTypeFlagBySkuIdsLimitRange(Long tenantId, List<Long> skuIds, List<Long> targetIds) {
        return DubboResponse.getOK(itemDomainService.queryItemMTypeFlagBySkuIds (tenantId,skuIds,targetIds));
    }

    @Override
    public DubboResponse<List<MarketItemDetailResp>> getItemDetailByOutId(MarketItemDetailQueryReq marketItemDetailQueryReq) {
        List<MarketItemDetailVO> itemDetailByOutId = itemDomainService.getItemDetailByOutId(marketItemDetailQueryReq.getOutIds());
        return DubboResponse.getOK(MarketItemConverter.marketItemDtailVOToResp(itemDetailByOutId));
    }

    @Override
    public DubboResponse<Boolean> saveItemLabel(MarketItemInputReq marketItemInputReq) {
        itemDomainService.saveItemLabel(MarketItemConverter.marketItemInputReqToDTO(marketItemInputReq));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<List<MarketItemDetailResp>> queryMarketItemLabelByItemCodes(Long tenantId, Set<String> itemCodes) {
        List<MarketItemDetailVO> itemDetailByOutId = itemDomainService.queryMarketItemLabelByItemCodes (tenantId, itemCodes);
        return DubboResponse.getOK(MarketItemConverter.marketItemDtailVOToResp(itemDetailByOutId));

    }
}
