package com.cosfo.item.web.domain.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cofso.item.client.req.MarketItemOnSaleReq;
import com.cofso.item.client.req.MarketItemWithClassificationQueryReq;
import com.cofso.item.client.resp.MarketItemWithClassificationResp;
import com.cofso.page.PageResp;
import com.cosfo.item.common.dto.LadderPriceDTO;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemWithClassificationDTO;
import com.cosfo.item.infrastructure.item.dto.MarketItemWithClassificationQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.web.domain.converter.MarketDomainConvert;
import com.cosfo.item.web.domain.dto.MarketDetailQueryDTO;
import com.cosfo.item.web.domain.dto.MarketItemDetailQueryDTO;
import com.cosfo.item.web.domain.vo.MarketItemDetail4StoreVO;
import com.cosfo.item.web.domain.vo.MarketItemVO;
import com.cosfo.item.web.provider.converter.MarketConvert;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SpringBootTest
class ItemDomainServiceTest {

    @Resource
    private ItemDomainService itemDomainService;
    @Resource
    private MarketItemDao marketItemDao;

    @Test
    void getMarketItemDetail() {
        MarketItem marketItem = marketItemDao.getById(22095L);
        System.out.println(marketItem);
        MarketItemVO itemVO = MarketDomainConvert.INSTANCE.convert2ItemVO(marketItem);
        System.out.println(itemVO);
        MarketDetailQueryDTO queryDTO = new MarketDetailQueryDTO();
        queryDTO.setMarketItemId(22095L);
        MarketItemVO marketItemDetail = itemDomainService.getMarketItemDetailById (2L,22095L);
        System.out.println(marketItemDetail);
    }

    @Test
    void cleanNoGoodsSupplyPrice() {
        MarketItemDetailQueryDTO marketItemDetailQueryDTO = new MarketItemDetailQueryDTO ();
        marketItemDetailQueryDTO.setTenantId(2L);
        marketItemDetailQueryDTO.setStoreId(4290L);
        marketItemDetailQueryDTO.setItemId(51770L);


        MarketItemDetail4StoreVO marketItemDetail4Store = itemDomainService.getMarketItemDetail4Store(marketItemDetailQueryDTO);
        System.out.println (marketItemDetail4Store);
    }


    @Test
    void query4SelectPage() {
        MarketItemWithClassificationQueryParam param = new MarketItemWithClassificationQueryParam();
        param.setPageIndex(1);
        param.setPageSize(10);
        param.setTenantId(2L);
        IPage<MarketItemWithClassificationDTO> page = marketItemDao.queryMarketItemWithClassification(param);
        System.out.println(JSON.toJSONString(page));
    }

    @Test
    void query4SelectPageDomain() {
        MarketItemWithClassificationQueryReq queryReq = new MarketItemWithClassificationQueryReq();
        queryReq.setPageIndex(1);
        queryReq.setPageSize(10);
        queryReq.setTenantId(2L);
        PageResp<MarketItemWithClassificationResp> marketItem4SelectRespPageResp = itemDomainService.queryItemWithClassification(queryReq);
        System.out.println(JSON.toJSONString(marketItem4SelectRespPageResp));
    }

    @Test
    void cleanNoGoodsInfoTest() {
        marketItemDao.cleanNoGoodsInfo(23774L);
    }

    @Test
    void queryMarketInfoTest() {
        MarketItemOnSaleReq req = new MarketItemOnSaleReq();
        req.setTenantId(2L);
        req.setSkuIds(Lists.newArrayList(10233L, 16972L, 16965L, 12103L, 16975L, 16974L, 16971L, 16969L, 16968L, 16966L));
        itemDomainService.queryMarketItemOnSaleInfo(req);
    }


    @Test
     void ladderPriceTest() {
        // 创建示例数据
        List<MarketItemPriceStrategy> strategies = new ArrayList<>();

        MarketItemPriceStrategy marketItemPriceStrategy = new MarketItemPriceStrategy ();
        marketItemPriceStrategy.setItemId (1L);
        marketItemPriceStrategy.setPriceStrategyValue ("[{\"price\": 50, \"unit\": 4}, {\"price\": 60, \"unit\": 3}, {\"price\": 100, \"unit\": 1}]");
        strategies.add(marketItemPriceStrategy);
        MarketItemPriceStrategy marketItemPriceStrategy2 = new MarketItemPriceStrategy ();
        marketItemPriceStrategy2.setItemId (1L);
        marketItemPriceStrategy2.setPriceStrategyValue ("[{\"price\": 40, \"unit\": 5}, {\"price\": 80, \"unit\": 3}, {\"price\": 99, \"unit\": 1}]");
        strategies.add(marketItemPriceStrategy2);
        MarketItemPriceStrategy marketItemPriceStrategy3 = new MarketItemPriceStrategy ();
        marketItemPriceStrategy3.setItemId (2L);
        marketItemPriceStrategy3.setPriceStrategyValue ("");
        strategies.add(marketItemPriceStrategy3);


        Map<Long, List<LadderPriceDTO>> result = strategies.stream ().filter (e -> StringUtils.isNotBlank (e.getPriceStrategyValue ()))
                .collect (Collectors.toMap (
                        MarketItemPriceStrategy::getItemId,
                        e -> JSON.parseArray (e.getPriceStrategyValue (), LadderPriceDTO.class),
                        (existingList, newList) -> {
                            List<LadderPriceDTO> mergedList = new ArrayList<> (existingList);
                            for (LadderPriceDTO newItem : newList) {
                                boolean found = false;
                                for (LadderPriceDTO existingItem : mergedList) {
                                    if (existingItem.getUnit () == newItem.getUnit ()) {
                                        if (existingItem.getPrice ().compareTo (newItem.getPrice ()) > 0) {
                                            mergedList.remove (existingItem);
                                            mergedList.add (newItem);
                                        }
                                        found = true;
                                        break;
                                    }
                                }
                                if (!found) {
                                    mergedList.add (newItem);
                                }
                            }
                            mergedList.sort (Comparator.comparingInt (LadderPriceDTO::getUnit));
                            return mergedList;
                        }
                ));


        // 打印结果
        result.forEach((key, value) -> {
            System.out.println("Item ID: " + key);
            value.forEach(dto -> System.out.println(" - Price: " + dto.getPrice() + ", Unit: " + dto.getUnit()));
        });
    }
}