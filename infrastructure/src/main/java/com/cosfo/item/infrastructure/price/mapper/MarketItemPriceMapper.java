package com.cosfo.item.infrastructure.price.mapper;

import com.cosfo.item.common.dto.PriceRangeDTO;
import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;
import com.cosfo.item.infrastructure.price.model.MarketItemPrice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品价格表(MarketItemPrice)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-28 15:45:04
 */
@Mapper
public interface MarketItemPriceMapper extends BaseMapper<MarketItemPrice>{
    void saveOrUpdateByItemIdAndTargetIdAndTargetType(@Param("list") List<MarketItemPrice> price);

    List<PriceRangeDTO> queryPriceRangeGroupByItemId(@Param ("tenantId") Long tenantId,@Param ("list") List<Long> itemIds);
}

