package com.cosfo.item.infrastructure.skuPreferential.mapper;

import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 省心定报价城市关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Mapper
public interface ProductSkuPreferentialCostPriceMappingMapper extends BaseMapper<ProductSkuPreferentialCostPriceMapping> {

    int batchSave(List<ProductSkuPreferentialCostPriceMapping> mappingList);
}
