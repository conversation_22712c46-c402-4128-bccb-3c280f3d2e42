package com.cosfo.item.web.domain.service;

import com.cofso.item.client.enums.StockRecordType;
import com.cosfo.item.infrastructure.item.dao.StockDao;
import com.cosfo.item.infrastructure.item.dao.StockRecordDao;
import com.cosfo.item.infrastructure.item.dto.StockQueryParam;
import com.cosfo.item.infrastructure.item.model.Stock;
import com.cosfo.item.infrastructure.item.model.StockRecord;
import com.cosfo.item.web.domain.converter.StockConvert;
import com.cosfo.item.web.domain.dto.StockDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/16
 */
@Component
@Slf4j
public class StockDomainService {

    @Autowired
    private StockDao stockDao;
    @Autowired
    private StockRecordDao stockRecordDao;

    /**
     * 处理本地库存
     *
     * @param tenantId
     * @param recordType
     * @param itemId
     * @param addAmount
     * @param recordNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void increaseSelfStock(Long tenantId, StockRecordType recordType, Long itemId, Integer addAmount, String recordNo){
        Stock stock = stockDao.preUpdateQuery(tenantId, itemId);
        if (stock == null) {
            throw new BizException("库存信息错误");
        }

        stockDao.increaseStock(stock.getId(), addAmount);

        StockRecord record = new StockRecord();
        record.setTenantId(tenantId);
        record.setStockSkuId(itemId);
        record.setType(recordType.getType());
        record.setBeforeAmount(stock.getAmount());
        record.setChangeAmount(Math.abs(addAmount));
        record.setAfterAmount(stock.getAmount() + addAmount);
        record.setRecordNo(recordNo);
        stockRecordDao.save(record);
    }

    /**
     * 批量查询
     *
     * @param tenantId
     * @param itemIds
     * @return
     */
    public List<StockDTO>  batchQuery(Long tenantId, List<Long> itemIds){
        StockQueryParam param = StockQueryParam.builder().build();
        param.setTenantId(tenantId);
        param.setItemIds(itemIds);
        List<Stock> stocks = stockDao.listByParam(param);
        List<StockDTO> stockDTOS = StockConvert.convertToStockList(stocks);
        return stockDTOS;
    }
}
