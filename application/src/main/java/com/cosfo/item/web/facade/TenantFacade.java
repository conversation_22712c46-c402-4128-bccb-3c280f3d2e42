package com.cosfo.item.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.TenantVO;
import com.cosfo.item.web.facade.converter.TenantConverter;
import com.cosfo.manage.client.enums.ItemPriceRuleEnum;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TenantFacade {
    @DubboReference
    private TenantProvider tenantProvider;
    @Resource
    private UserCenterTenantFacade userCenterTenantFacade;

    //缓存 超时时间 1小时、- 租户信息相关 变动不频繁
    long tenantAvailableCachePeriod = 10 * 60 * 6;

    //缓存 超时时间 10分钟、- 地址信息相关 变动相对频繁
    long addressAvailableCachePeriod = 10 * 60;

    public boolean getSaveWorrySwitchByTenantId(Long tenantId) {
        if(ObjectUtil.isNull(tenantId)){
            throw new ParamsException("tenantId 不能为空");
        }
        DubboResponse<Boolean> response = tenantProvider.getSaveWorrySwitch (tenantId);
        if (!response.isSuccess()) {
            throw new BizException("获取租户信息失败");
        }
        return response.getData();
    }


    public TenantVO getTenantByAdminIdInternal(Long adminId) {
        if(ObjectUtil.isNull(adminId)){
            throw new ParamsException("adminId 不能为空");
        }

        TenantQueryReq req = new TenantQueryReq();
        req.setAdminId(adminId);
        List<TenantResp> list = userCenterTenantFacade.list(req);
        if(!CollectionUtil.isEmpty(list)){
            return TenantConverter.tenantResp2VO(list.stream().findFirst().get());
        }
        log.info ("getTenantByAdminIdInternal查询结果为空， adminId={}",adminId);
        return null;
    }

    public TenantVO getTenantByAdminId(Long adminId) {
        try {
            log.info("从缓存中获取adminId TenantVO:{}", adminId);
            return ADMIN_CACHE.get(adminId);
        } catch (Exception e) {
            log.warn ("从缓存中获取adminId TenantVO失败:{}", adminId,e);
            return getTenantByAdminIdInternal(adminId);
        }
    }

    private final LoadingCache<Long, TenantVO> ADMIN_CACHE = CacheBuilder.newBuilder()
            .maximumSize(200)
            .recordStats()
            .expireAfterWrite(Duration.ofSeconds(tenantAvailableCachePeriod))//10 * 60 seconds
            .build(new CacheLoader<Long, TenantVO> () {
                @Override
                public TenantVO load(Long adminId) {
                    return getTenantByAdminIdInternal(adminId);
                }
            });

    public List<TenantVO> listAllTenant() {
        TenantQueryReq req = new TenantQueryReq();
        List<TenantResp> list = userCenterTenantFacade.list(req);

        if(!CollectionUtil.isEmpty(list)){
            return list.stream().map(e-> TenantConverter.tenantResp2VO(e)).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    public List<String> listAddressInternal(Long tenantId) {
        if(ObjectUtil.isEmpty (tenantId)){
            return Collections.emptyList ();
        }
        List<String> list = userCenterTenantFacade.listAddress(tenantId);
        return list.stream().filter (ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    public List<String> listAddress(Long tenantId) {
        try {
            log.info("从缓存中获取租户listAddress:{}", tenantId);
            return ADDRESS_CACHE.get(tenantId);
        } catch (Exception e) {
            log.warn ("从缓存中获取租户listAddress失败:{}", tenantId, e);
            return listAddressInternal(tenantId);
        }
    }

    private final LoadingCache<Long, List<String>> ADDRESS_CACHE = CacheBuilder.newBuilder()
            .maximumSize(200)
            .recordStats()
            .expireAfterWrite(Duration.ofSeconds(addressAvailableCachePeriod))//10 * 60 seconds
            .build(new CacheLoader<Long, List<String>> () {
                @Override
                public List<String> load(Long tenantId) {
                    return listAddressInternal(tenantId);
                }
            });





    public TenantVO getTenantByTenantIdInternal(Long tenantId) {
        if(ObjectUtil.isNull(tenantId)){
            throw new ParamsException("tenantId 不能为空");
        }

        TenantQueryReq req = new TenantQueryReq();
        req.setTenantId(tenantId);
        List<TenantResp> list = userCenterTenantFacade.list(req);
        if(!CollectionUtil.isEmpty(list)){
            return TenantConverter.tenantResp2VO(list.stream().findFirst().get());
        }
        log.info ("getTenantByTenantIdInternal查询结果为空， tenantId={}",tenantId);
        return null;
    }
    public TenantVO getTenantByTenantId(Long tenantId) {
        try {
            log.info("从缓存中获取租户TenantVO:{}", tenantId);
            return TENANT_CACHE.get(tenantId);
        } catch (Exception e) {
            log.warn ("从缓存中获取租户TenantVO失败:{}", tenantId, e);
            return getTenantByTenantIdInternal(tenantId);
        }
    }

    private final LoadingCache<Long, TenantVO> TENANT_CACHE = CacheBuilder.newBuilder()
            .maximumSize(200)
            .recordStats()
            .expireAfterWrite(Duration.ofSeconds(tenantAvailableCachePeriod))//10 * 60 seconds
            .build(new CacheLoader<Long, TenantVO> () {
                @Override
                public TenantVO load(Long tenantId) {
                    return getTenantByTenantIdInternal(tenantId);
                }
            });
}
