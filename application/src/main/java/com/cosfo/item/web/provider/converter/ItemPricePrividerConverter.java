package com.cosfo.item.web.provider.converter;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.req.LadderPrice;
import com.cofso.item.client.req.MaxCostPriceQueryReq;
import com.cofso.item.client.req.MerchantAddressReq;
import com.cofso.item.client.req.PriceStrategyFloatingRangeReq;
import com.cofso.item.client.resp.ItemPriceDetailResp;
import com.cofso.item.client.resp.PriceDetailResp;
import com.cofso.item.client.resp.PriceStrategyRangeResp;
import com.cosfo.item.web.domain.dto.MaxCostPriceQueryDTO;
import com.cosfo.item.web.domain.dto.MerchantAddressDTO;
import com.cosfo.item.web.domain.dto.PriceStrategyFloatingRangeDTO;
import com.cosfo.item.web.domain.vo.ItemPriceDetailVO;
import com.cosfo.item.web.domain.vo.PriceDetailVO;
import com.cosfo.item.web.domain.vo.PriceStrategyRangeVO;

import java.util.List;
import java.util.stream.Collectors;

public class ItemPricePrividerConverter {

    public static PriceDetailResp priceDetailVO2Resp(PriceDetailVO vo) {
        if(ObjectUtil.isNull (vo)){
            return null;
        }
        PriceDetailResp resp = new PriceDetailResp ();
        resp.setPrice(vo.getPrice ());
        resp.setCostPrice(vo.getCostPrice ());
        resp.setMarketItemPrice(vo.getMarketItemPrice ());
        resp.setLadderPrice(vo.getLadderPrice ());
        if(CollectionUtil.isNotEmpty (vo.getLadderPriceDTOS ())) {
            resp.setLadderPrices (vo.getLadderPriceDTOS ().stream().map (e->{
                LadderPrice ladderPrice = new LadderPrice ();
                ladderPrice.setPrice(e.getPrice ());
                ladderPrice.setUnit(e.getUnit ());
                return ladderPrice;
            }).collect(Collectors.toList()));
        }
        return resp;
    }

    public static ItemPriceDetailResp itemPriceDetailVO2Resp(ItemPriceDetailVO vo) {
        if(ObjectUtil.isNull (vo)){
            return null;
        }
        ItemPriceDetailResp resp = new ItemPriceDetailResp ();
        resp.setItemId(vo.getItemId ());
        if(CollectionUtil.isNotEmpty (vo.getSubItemPriceDetails ())) {
            resp.setSubItemPriceDetails (vo.getSubItemPriceDetails ().stream ().map (ItemPricePrividerConverter::itemPriceDetailVO2Resp).collect (Collectors.toList ()));
        }
        resp.setPrice(vo.getPrice ());
        resp.setCostPrice(vo.getCostPrice ());
        resp.setMarketItemPrice(vo.getMarketItemPrice ());
        return resp;
    }

    public static MaxCostPriceQueryDTO maxCostPriceQueryReq2DTO(MaxCostPriceQueryReq req) {
        MaxCostPriceQueryDTO dto = new MaxCostPriceQueryDTO ();
        dto.setTenantId(req.getTenantId ());
        dto.setSkuId(req.getSkuId ());
        dto.setAddressDTOS(getAddressDTOS(req.getAddressReqs ()));
        return dto;
    }

    private static List<MerchantAddressDTO> getAddressDTOS(List<MerchantAddressReq> addressReqs) {
      return addressReqs.stream ().map (e->{
            MerchantAddressDTO merchantAddressDTO = new MerchantAddressDTO ();
            merchantAddressDTO.setProvince(e.getProvince ());
            merchantAddressDTO.setCity(e.getCity ());
            merchantAddressDTO.setArea(e.getArea ());
            merchantAddressDTO.setCityId(e.getCityId ());
            return merchantAddressDTO;
        }).collect(Collectors.toList());
    }

    public static PriceStrategyRangeResp priceStrategyRangeVO2Resp(PriceStrategyRangeVO vo) {
        PriceStrategyRangeResp resp = new PriceStrategyRangeResp ();
        resp.setCostPriceAddPercentageMin(vo.getCostPriceAddPercentageMin ());
        resp.setCostPriceAddPercentageMax(vo.getCostPriceAddPercentageMax ());
        resp.setCostPriceAddFixedMin(vo.getCostPriceAddFixedMin ());
        resp.setCostPriceAddFixedMax(vo.getCostPriceAddFixedMax ());
        resp.setAssignMin(vo.getAssignMin ());
        resp.setAssignMax(vo.getAssignMax ());
        return resp;
    }

    public static PriceStrategyFloatingRangeDTO priceStrategyFloatingRangeReq2DTO(PriceStrategyFloatingRangeReq req) {
        PriceStrategyFloatingRangeDTO dto = new PriceStrategyFloatingRangeDTO ();
        dto.setTenantId(req.getTenantId ());
        dto.setMarketItemIds(req.getMarketItemIds ());
        dto.setCostPriceAddPercentageRange(req.getCostPriceAddPercentageRange ());
        dto.setCostPriceAddPercentageOptype(req.getCostPriceAddPercentageOptype ());
        dto.setCostPriceAddFixedRange(req.getCostPriceAddFixedRange ());
        dto.setCostPriceAddFixedOptype(req.getCostPriceAddFixedOptype ());
        dto.setAssignRange(req.getAssignRange ());
        dto.setAssignOptype(req.getAssignOptype ());
        return dto;
    }
}
