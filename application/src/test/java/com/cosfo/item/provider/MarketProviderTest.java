package com.cosfo.item.provider;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.provider.MarketItemProvider;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.MarketItemInfoResp;
import com.cofso.item.client.resp.MarketResp;
import com.cofso.page.PageResp;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/25 15:49
 * @Description:
 */
@SpringBootTest
public class MarketProviderTest {
    @Resource
    private MarketProvider marketProvider;
    @Resource
    private MarketItemProvider marketItemProvider;

    @Test
    public void updateMarketItem(){
        String json = "{\"afterSaleUnit\":\"盒\",\"averagePriceFlag\":0,\"baseSaleUnit\":1,\"createTime\":\"2023-05-25T13:53:26\",\"deleteFlag\":1,\"extType\":0,\"goodsType\":2,\"itemCode\":\"971033765661\",\"itemTitle\":\"zyt测试茶2号\",\"itemType\":0,\"maxAfterSaleAmount\":1,\"miniOrderQuantity\":1,\"origin\":\"\",\"outId\":18408,\"priceType\":1,\"samplePool\":0,\"specification\":\"1盒*1斤/15cm/清香\",\"specificationUnit\":\"盒\",\"tenantId\":1,\"updateTime\":\"2023-05-25T13:55:04\"}";
        DubboResponse<Boolean> dubboResponse = marketProvider.updateMarketItem(JSON.parseObject(json, MarketItemInputReq.class));
        Assert.isTrue(dubboResponse.getData());
    }

    @Test
    public void queryMarketListPage(){
        // {"deleteFlag":1,"pageNum":1,"pageSize":10,"tenantId":2,"unfairPriceStrategyDefaultFlag": 0}
        // 1 1053 0 3 2 4 "unfairPriceStrategyType": 2 1058
        // unfairPriceStrategyDefaultFlag 0 7 1 1055
//        String json = "{\"deleteFlag\":1,\"marketItemInfoQueryFlagReq\":{\"categoryIdFlag\":true,\"classificationIdFlag\":true,\"priceRangeFlag\":false,\"stockFlag\":true},\"onSale\":1,\"pageNum\":1,\"pageSize\":10,\"tenantId\":1024}";
//        System.out.println("======== queryMarketListPage result ========");
//        DubboResponse<PageResp<MarketResp>> dubboResponse = marketProvider.queryMarketListPage(JSON.parseObject(json, MarketQueryReq.class));
//        System.out.println(JSON.toJSONString(dubboResponse.getData()));
        String json = "{\"marketItemInfoQueryFlagReq\":{\"categoryIdFlag\":false,\"classificationIdFlag\":true,\"priceRangeFlag\":false,\"stockFlag\":false},\"pageSize\":10,\"tenantId\":1024}";
        System.out.println("======== queryMarketListPage result ========");
        DubboResponse<PageResp<MarketItemInfoResp>> dubboResponse = marketItemProvider.queryMarketItemList (JSON.parseObject (json, MarketItemCommonQueryReq.class));
        System.out.println(JSON.toJSONString(dubboResponse.getData()));
    }

    @Test
    public void queryByMarketIds(){
        List<Long> marketIds = Lists.newArrayList(2014036L);
        MarketQueryReq marketQueryReq = new MarketQueryReq();
        marketQueryReq.setTenantId(2L);
        marketQueryReq.setMarketIds(marketIds);
        marketQueryReq.setPageNum(1);
        marketQueryReq.setPageSize(marketIds.size());

        DubboResponse<PageResp<MarketResp>> dubboResponse = marketProvider.queryMarketListPage(marketQueryReq);
        System.out.println(JSON.toJSONString(dubboResponse.getData()));
    }

    @Test
    public void initMarketItem(){
        MarketItemInputReq req = new MarketItemInputReq();
        marketProvider.initMarketItem(req);
    }

    @Test
    public void queryItemPriceStrategy(){
        MarketDetailQueryReq req = MarketDetailQueryReq.builder().build();
        req.setMarketItemId(23991L);
        req.setTenantId(24514L);
        marketProvider.queryItemPriceStrategy(req);
    }

    @Test
    public void addMarket(){
        String json = "{\"afterSaleReason\":\"商品数量不符;包装问题;商品品质问题;保质期不符;平台发错货;其他\",\"afterSaleTime\":48,\"categoryId\":1022,\"classificationId\":1159,\"createTime\":\"2022-11-21T11:49:21\",\"deleteFlag\":1,\"detailPicture\":\"\",\"mainPicture\":\"\",\"marketItemInputReq\":{\"afterSaleUnit\":\"1\",\"averagePriceFlag\":0,\"baseSaleUnit\":1,\"createTime\":\"2022-11-21T11:49:21\",\"customerId\":1037,\"deleteFlag\":1,\"extType\":0,\"goodsType\":2,\"itemCode\":\"1022305023524\",\"itemTitle\":\"乔治代仓商品\",\"itemType\":0,\"mainPicture\":\"test/8iuh5jbdn55rog9wd.jpg\",\"maxAfterSaleAmount\":1,\"miniOrderQuantity\":1,\"onSale\":0,\"origin\":\"\",\"outId\":8906,\"priceType\":1,\"samplePool\":0,\"skuId\":10547,\"specification\":\"1个*1KG/1/1\",\"specificationUnit\":\"包\",\"tenantId\":1,\"updateTime\":\"2023-04-19T10:50:39\"},\"outId\":8414,\"refundReason\":\"拍多/拍错/不想要;缺货;其他\",\"slogan\":\"\",\"subTitle\":\"111\",\"tenantId\":1,\"title\":\"乔治代仓商品1\",\"updateTime\":\"2023-01-04T14:21:28\"}"
            +"";
        marketProvider.addMarket(JSON.parseObject(json,MarketInputReq.class));
    }

    @Test
    public void getMarketItemInfoByItemCode(){
        marketProvider.getMarketItemDetailByItemCode(1L,"************");
    }

    @Test
    public void getMarketItemDetail(){
        MarketDetailQueryReq marketDetailQueryReq = MarketDetailQueryReq.builder().build();
        marketDetailQueryReq.setMarketItemId(23949L);
        DubboResponse<MarketItemInfoResp> marketItemDetail = marketProvider.getMarketItemDetail(marketDetailQueryReq);
        System.out.println(marketItemDetail);
    }

    @Test
    public void queryMarketItemInfoBySkuIds(){
        DubboResponse<List<MarketItemInfoResp>> list = marketProvider.queryMarketItemInfoBySkuIds(1L, Lists.newArrayList(26576L));
        System.out.println(JSON.toJSONString(list.getData()));
    }

    @Test
    public void listMarketItemInfoByItemCodes(){
        DubboResponse<List<MarketItemInfoResp>> list = marketProvider.listMarketItemInfoByItemCodes(1L, Lists.newArrayList("2188452678684", "2191561045253"));
        System.out.println(JSON.toJSONString(list.getData()));
    }


}
