package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.lang.Pair;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Component
public class CosfoMerchantStoreGroupMappingHandler implements DbTableDmlStrategy {

    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.MERCHANT_STORE_GROUP_MAPPING;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();

                Long tenantId = Long.valueOf (dataMap.get ("tenant_id"));
                Long storeId = Long.valueOf (dataMap.get ("store_id"));
                handleItemPrice (tenantId,storeId);
            }
        }
    }

    private void handleItemPrice(Long tenantId,Long storeId) {
        List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemByTenantIdAndTypes(tenantId, MarketItemEnum.GoodsType.ALL_ITEM_TYPE);
        if (CollectionUtils.isEmpty(marketItemVOS)) {
            log.warn("没有找到marketItemVOS：{}", tenantId);
            return;
        }
        log.info("店铺:{} 地址省市区变化，更新店铺所有的item价格", storeId);
        for (MarketItemVO marketItemVO : marketItemVOS) {
            ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
            dto.setStoreId (storeId);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }
}
