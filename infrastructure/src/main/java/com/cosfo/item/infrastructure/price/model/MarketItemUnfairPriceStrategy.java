package com.cosfo.item.infrastructure.price.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商品倒挂策略表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Getter
@Setter
@TableName("market_item_unfair_price_strategy")
public class MarketItemUnfairPriceStrategy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * item id, 如果是-1，则说明是全局默认规则
     */
    private Long itemId;

    /**
     * 是否默认, 1=是,0=否
     */
    private Integer defaultFlag;

    /**
     * 策略值 0-以供应价售卖 1-已自定义价售卖 2-自动下架
     */
    private Integer strategyValue;

    /**
     * 策略生效目标 0-品牌方
     */
    private Integer targetType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
