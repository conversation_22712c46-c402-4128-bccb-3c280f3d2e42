package com.cosfo.item.infrastructure.price.dao.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.price.model.FutureValidCostPrice;
import com.cosfo.item.infrastructure.price.mapper.FutureValidCostPriceMapper;
import com.cosfo.item.infrastructure.price.dao.FutureValidCostPriceDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 未来生效价格表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Service
public class FutureValidCostPriceDaoImpl extends ServiceImpl<FutureValidCostPriceMapper, FutureValidCostPrice> implements FutureValidCostPriceDao {

    @Override
    public List<FutureValidCostPrice> listFutureValidCostPrice() {
        LambdaQueryWrapper<FutureValidCostPrice> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        wrapper.le(FutureValidCostPrice::getValidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
        return list(wrapper);
    }
}
