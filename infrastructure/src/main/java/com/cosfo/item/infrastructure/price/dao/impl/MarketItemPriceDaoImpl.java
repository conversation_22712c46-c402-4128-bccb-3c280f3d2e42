package com.cosfo.item.infrastructure.price.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.common.dto.PriceRangeDTO;
import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceParam;
import com.cosfo.item.infrastructure.price.model.MarketItemPrice;
import com.cosfo.item.infrastructure.price.mapper.MarketItemPriceMapper;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceDao;
import java.util.Collection;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 商品价格表(MarketItemPrice)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-28 15:45:07
 */
@Service
public class MarketItemPriceDaoImpl extends ServiceImpl<MarketItemPriceMapper, MarketItemPrice> implements MarketItemPriceDao {
    @Override
    public void saveOrUpdateByItemIdAndTargetIdAndTargetType(List<MarketItemPrice> price) {
        getBaseMapper().saveOrUpdateByItemIdAndTargetIdAndTargetType(price);
    }
    @Override
    public List<MarketItemPrice> listByItemIds(Long tenantId, List<Long> itemIds) {
        LambdaQueryWrapper<MarketItemPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MarketItemPrice::getMarketItemId,itemIds);
        queryWrapper.eq(MarketItemPrice::getTenantId,tenantId);
        return list(queryWrapper);
    }

    @Override
    public List<MarketItemPrice> ListByItemIdAndTargetIdsAndTargetType(Long tenantId, Long marketItemId, Collection<Long> targetIds, Integer targetType) {
        LambdaQueryWrapper<MarketItemPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketItemPrice::getTenantId,tenantId);
        queryWrapper.eq(MarketItemPrice::getMarketItemId,marketItemId);
        queryWrapper.in(MarketItemPrice::getTargetId,targetIds);
        queryWrapper.eq(MarketItemPrice::getTargetType,targetType);
        return list (queryWrapper);
    }

    @Override
    public List<PriceRangeDTO> queryPriceRangeGroupByItemId(Long tenantId, List<Long> itemIds) {
        return getBaseMapper ().queryPriceRangeGroupByItemId(tenantId,itemIds);
    }

    @Override
    public List<MarketItemPrice> ListByItemIdAndTarget(Long tenantId, Collection<Long> marketItemIds, Long targetId, Integer targetType) {
        LambdaQueryWrapper<MarketItemPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketItemPrice::getTenantId,tenantId);
        queryWrapper.in(MarketItemPrice::getMarketItemId,marketItemIds);
        queryWrapper.eq(MarketItemPrice::getTargetId,targetId);
        queryWrapper.eq(MarketItemPrice::getTargetType,targetType);
        return list (queryWrapper);
    }
}
