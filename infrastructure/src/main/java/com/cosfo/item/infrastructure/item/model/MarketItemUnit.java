package com.cosfo.item.infrastructure.item.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商品单位表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Getter
@Setter
@TableName("market_item_unit")
public class MarketItemUnit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 商品sku主键
     */
    private Long itemId;

    /**
     * 单位
     */
    private String unitDesc;

    /**
     * 单位类型 1=门店订货单位(基本单位),2=门店库存单位,3=门店成本单位
     */
    private Integer unitType;

    /**
     * 与门店订货单位换算倍数
     */
    private BigDecimal storeOrderingUnitMultiple;


}
