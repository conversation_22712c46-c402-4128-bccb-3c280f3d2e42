package com.cosfo.item.infrastructure.item.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 商品上下架表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public interface MarketItemOnsaleStrategyMappingDao extends IService<MarketItemOnsaleStrategyMapping> {

    List<MarketItemOnsaleStrategyMapping> listMarketItemOnsaleStrategyByItemIds(Long tenantId, List<Long> itemIds);

    void saveOrUpdateByItemIdAndTargetIdAndStrategyType(List<MarketItemOnsaleStrategyMapping> list);

    List<MarketItemOnsaleStrategyMapping> listByParam(MarketItemOnsaleStrategyQueryParam param);

    void soldOutByMarketItemId(Long tenantId,Long marketItemId);

    List<MarketItemOnsaleStrategyMapping> listMarketItemOnsaleStrategyByItemIdAndTargetId(Long tenantId, Long itemId, Long targetId);

    /**
     * 查询上架中的 itemId
     * @param tenantId
     * @param itemIds
     * @return
     */
    Set<Long> listOnSaleMarketItem(Long tenantId, List<Long> itemIds,List<Long> targetIds);

    /**
     * 查询大客户专享中的 itemId
     * @param tenantId
     * @param itemIds
     * @return
     */
    Set<Long> listMTypeMarketItem(Long tenantId, List<Long> itemIds, List<Long> targetIds);
}
