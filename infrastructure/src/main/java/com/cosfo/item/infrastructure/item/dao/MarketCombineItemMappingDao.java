package com.cosfo.item.infrastructure.item.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketCombineItemMapping;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 组合品映射表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface MarketCombineItemMappingDao extends IService<MarketCombineItemMapping> {

    /**
     * 根据条件查询
     *
     * @param param
     * @return
     */
    List<MarketCombineItemMapping> listByParam(MarketCombineQueryParam param);
    /**
     * 根据子item查询组合品的id
     *
     * @param combineItemId
     * @return
     */
    Set<Long> listItemIdsByCombineItemId(Long tenantId , Long combineItemId);
}
