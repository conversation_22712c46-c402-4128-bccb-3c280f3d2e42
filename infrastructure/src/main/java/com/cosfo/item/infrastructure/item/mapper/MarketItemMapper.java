package com.cosfo.item.infrastructure.item.mapper;

import com.cosfo.item.infrastructure.item.dto.MarketItemOnSaleSimpleDTO;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Mapper
public interface MarketItemMapper extends MPJBaseMapper<MarketItem> {

    List<MarketItemOnSaleSimpleDTO> queryMarketItemOnSaleInfo(@Param("tenantId") Long tenantId,@Param("onSale") Integer onSale,@Param("skuIds") List<Long> skuIds);


    /**
     * 查询上下架的商品数
     * @param tenantId
     * @param onSale
     * @return
     */
    List<MarketItemOnSaleSimpleDTO> queryOnSaleMarketItems(@Param("tenantId") Long tenantId, @Param("onSale") Integer onSale);
}
