package com.cosfo.item.infrastructure.price.dao;

import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品item价格策略表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface MarketItemPriceStrategyDao extends IService<MarketItemPriceStrategy> {

    List<MarketItemPriceStrategy> listByItemIds(Long tenantId, List<Long> itemIds);

    List<MarketItemPriceStrategy> listByItemIdsAndStrategyType(Long tenantId, List<Long> itemIds, Integer strategyType);

    List<MarketItemPriceStrategy> getByItemIdAndTargetType(Long tenantId, Long marketItemId, Integer targetType);

    /**
     * 连表查询，根据报价目标跟报价目标类型， 还有报价类型，批量查询价格策略
     * @param tenantId
     * @param itemIds
     * @param strategyType
     * @param targetId
     * @param targetType
     * @return
     */
    List<MarketItemPriceStrategy> listByItemIdsAndStrategyTypeAndTarget(Long tenantId, List<Long> itemIds, Integer strategyType, Long targetId, Integer targetType);
}
