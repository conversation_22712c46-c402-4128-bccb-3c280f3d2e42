<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.price.mapper.MarketItemPriceMapper">
    <insert id="saveOrUpdateByItemIdAndTargetIdAndTargetType">
        INSERT INTO `market_item_price`( `tenant_id`, `market_item_id`, `target_type`, `target_id`, `price`, `base_price`, `price_strategy`)
                VALUES
        <foreach collection="list" item="item" separator=",">
            ( #{item.tenantId}, #{item.marketItemId}, #{item.targetType}, #{item.targetId}, #{item.price}, #{item.basePrice},#{item.priceStrategy} )
        </foreach>
        ON DUPLICATE KEY UPDATE
             price = VALUES(price),
             base_price = VALUES(base_price),
             price_strategy = VALUES(price_strategy)
    </insert>
    <select id="queryPriceRangeGroupByItemId" resultType="com.cosfo.item.common.dto.PriceRangeDTO">
        select min(price) as minPrice ,max(price) as maxPrice,market_item_id as marketItemId from market_item_price where tenant_id = #{tenantId} and market_item_id in
        <foreach collection="list" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
       GROUP BY market_item_id
    </select>
</mapper>
