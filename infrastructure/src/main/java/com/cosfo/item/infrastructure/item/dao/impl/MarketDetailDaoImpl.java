package com.cosfo.item.infrastructure.item.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.item.dao.MarketDetailDao;
import com.cosfo.item.infrastructure.item.dto.MarketDetailParam;
import com.cosfo.item.infrastructure.item.mapper.MarketDetailMapper;
import com.cosfo.item.infrastructure.item.model.MarketDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 商品扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class MarketDetailDaoImpl extends ServiceImpl<MarketDetailMapper, MarketDetail> implements MarketDetailDao {

    @Override
    public List<MarketDetail> listByParam(MarketDetailParam param) {
        return list(buildQueryWrapper(param));
    }

    @Override
    public MarketDetail getByMarketId(Long marketId) {
        LambdaQueryWrapper<MarketDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(marketId), MarketDetail::getMarketId, marketId);
        return getOne(queryWrapper);
    }


    private LambdaQueryWrapper<MarketDetail> buildQueryWrapper(MarketDetailParam param) {
        LambdaQueryWrapper<MarketDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getOutId()), MarketDetail::getOutId, param.getOutId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getOutIds()), MarketDetail::getOutId, param.getOutIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getMarketIds()), MarketDetail::getMarketId, param.getMarketIds());

        return queryWrapper;
    }
}
