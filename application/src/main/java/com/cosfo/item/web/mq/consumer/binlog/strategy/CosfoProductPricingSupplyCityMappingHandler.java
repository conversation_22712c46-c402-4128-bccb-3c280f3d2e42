package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.price.dao.CostPriceDao;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.mq.consumer.binlog.BinLogTimeUtil;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Component
public class CosfoProductPricingSupplyCityMappingHandler implements DbTableDmlStrategy {

    @Autowired
    private CostPriceDao costPriceDao;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Autowired
    private MqProducer mqProducer;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.PRODUCT_PRICING_SUPPLY_CITY_MAPPING;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        LocalDateTime now = LocalDateTime.now ();
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                Long productPricingSupplyId = Long.valueOf (map.get ("product_pricing_supply_id"));
                ProductPricingMessageDTO productPricingMessageDTO = costPriceDao.selectSkuIdsByProductPricingSupplyId (productPricingSupplyId);
                if(ObjectUtil.isNotNull (productPricingMessageDTO)){
                    handleItemPrice (productPricingMessageDTO);
                    sendMsg(BinLogTimeUtil.formartterYMDHHMS (map.get ("start_time")),BinLogTimeUtil.formartterYMDHHMS (map.get ("end_time")),productPricingMessageDTO);
                }
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                if(!dataMap.containsKey ("product_pricing_supply_id")){
                    return;
                }
                Long productPricingSupplyId = Long.valueOf (dataMap.get ("product_pricing_supply_id"));
                ProductPricingMessageDTO productPricingMessageDTO = costPriceDao.selectSkuIdsByProductPricingSupplyId (productPricingSupplyId);
                if(ObjectUtil.isNotNull (productPricingMessageDTO)) {
                    handleItemPrice (productPricingMessageDTO);
                    sendMsg(BinLogTimeUtil.formartterYMDHHMS (dataMap.get ("start_time")),BinLogTimeUtil.formartterYMDHHMS (dataMap.get ("end_time")),productPricingMessageDTO);
                }
            }
        }else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.DELETE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue();
                if(!oldMap.containsKey ("product_pricing_supply_id")){
                    return;
                }
                Long productPricingSupplyId = Long.valueOf (oldMap.get ("product_pricing_supply_id"));
                LocalDateTime startTime = BinLogTimeUtil.formartterYMDHHMS (oldMap.get ("start_time"));
                LocalDateTime endTime = BinLogTimeUtil.formartterYMDHHMS (oldMap.get ("end_time"));
                //删除了生效中的才处理
                if(now.isAfter (startTime) && now.isBefore (endTime)){
                    ProductPricingMessageDTO productPricingMessageDTO = costPriceDao.selectSkuIdsByProductPricingSupplyId (productPricingSupplyId);
                    if(ObjectUtil.isNotNull (productPricingMessageDTO)) {
                        handleItemPrice (productPricingMessageDTO);
                    }
                }
            }
        }
    }
    private void sendMsg(LocalDateTime startTime, LocalDateTime endTime, ProductPricingMessageDTO productPricingMessageDTO) {
        LocalDate currentDate = LocalDate.now();
        if(startTime.toLocalDate().isEqual(currentDate)){
            productPricingMessageDTO.setDealTime (startTime);
            mqProducer.sendStartDeliver (RocketMqConstant.Topic.SUPPLY_PRICE,RocketMqConstant.Tag.SUPPLY_PRICE,JSON.toJSON (productPricingMessageDTO),startTime.plusSeconds (1));
        }
        if(endTime.toLocalDate().isEqual(currentDate)){
            productPricingMessageDTO.setDealTime (endTime);
            mqProducer.sendStartDeliver (RocketMqConstant.Topic.SUPPLY_PRICE,RocketMqConstant.Tag.SUPPLY_PRICE,JSON.toJSON (productPricingMessageDTO),endTime.plusSeconds (1));
        }
    }

    private void handleItemPrice(ProductPricingMessageDTO productPricingMessageDTO) {
        List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemBySkuIdAndTenantIdAndGoodsTypes (productPricingMessageDTO.getSkuId (),productPricingMessageDTO.getTenantId (),Collections.singletonList (MarketItemEnum.GoodsType.QUOTATION.getCode ()));
        log.info ("handleItemPrice,处理商品售价marketItemVOS={}", JSON.toJSONString (marketItemVOS));
        for (MarketItemVO marketItemVO: marketItemVOS) {
            ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }
}
