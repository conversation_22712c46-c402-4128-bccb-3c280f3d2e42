package com.cosfo.item.infrastructure.item.dao;

import com.cosfo.item.infrastructure.item.model.MarketItemUnit;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品单位表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface MarketItemUnitDao extends IService<MarketItemUnit> {

    List<MarketItemUnit> listByItemIds(List<Long> itemIds, Long tenantId);

    void removeByItemId(Long tenantId, Long itemId);
}
