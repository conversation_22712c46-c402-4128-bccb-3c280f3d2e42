<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.price.mapper.CostPriceMapper">
    <select id="selectProductPricingSupplyDTOBySkuId" resultType="com.cosfo.item.infrastructure.price.dto.ProductPricingSupplyDTO">
        select
            s.id, s.tenant_id tenantId, s.supply_sku_id skuId, s.supply_tenant_id supplyTenantId,
            m.id productPricingSupplyCityMappingId, m.city_id cityId , m.`type` type, m.supply_type supplyType, m.price price, m.start_time startTime, m.end_time endTime
           ,m.strategy_value strategyValue
        from
            product_pricing_supply s
                left join product_pricing_supply_city_mapping m on s.id = m.product_pricing_supply_id
        where s.tenant_id = #{tenantId}
        <if test="cityIds != null and cityIds.size() > 0">
            and m.city_id in
            <foreach collection="cityIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        AND s.supply_sku_id = #{skuId}
        and m.start_time &lt;= now() and m.end_time &gt;= now()
    </select>
    <select id="selectFutureEndTimeProductPricingSupply" resultType="com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO">
        SELECT
            s.tenant_id AS tenantId,
            s.supply_sku_id AS skuId,
            m.end_time AS dealTime
        FROM
            product_pricing_supply s INNER JOIN product_pricing_supply_city_mapping m ON s.id = m.product_pricing_supply_id
        WHERE
            m.end_time between date_format(DATE_ADD(now(), INTERVAL 1 DAY), '%Y-%m-%d 00:00:00' ) and date_format(DATE_ADD(now(), INTERVAL 1 DAY),'%Y-%m-%d 23:59:59' )
        GROUP BY
            s.tenant_id,
            s.supply_sku_id,
            m.end_time;
    </select>
    <select id="selectFutureStartTimeProductPricingSupply" resultType="com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO">
        SELECT
            s.tenant_id AS tenantId,
            s.supply_sku_id AS skuId,
            m.start_time AS dealTime
        FROM
            product_pricing_supply s INNER JOIN product_pricing_supply_city_mapping m ON s.id = m.product_pricing_supply_id
        WHERE
            m.start_time between date_format(DATE_ADD(now(), INTERVAL 1 DAY), '%Y-%m-%d 00:00:00' ) and date_format(DATE_ADD(now(), INTERVAL 1 DAY),'%Y-%m-%d 23:59:59' )
        GROUP BY
            s.tenant_id,
            s.supply_sku_id,
            m.start_time;
    </select>
    <select id="selectSkuIdsByProductPricingSupplyId"
            resultType="com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO">
        select supply_sku_id skuId, tenant_id tenantId from product_pricing_supply where id =#{productPricingSupplyId}
    </select>

    <insert id="saveOrUpdateByDuplicateKey">
        INSERT INTO `cost_price`( `tenant_id`, `sku_id`, `sku_code`, `area`, `province`, `city`, `price`, `valid_time`, `invalid_time`, `valid_type`, `sys_type`)
        VALUES
            (#{tenantId}, #{skuId},#{skuCode},#{area},#{province},#{city},#{price},#{validTime}, #{invalidTime},#{validType},#{sysType})
        ON DUPLICATE KEY UPDATE
        sku_code = #{skuCode},
        province = #{province},
        valid_time = #{validTime},
        invalid_time = #{invalidTime},
        sys_type = #{sysType},
        price = #{price}
    </insert>
</mapper>
