package com.cosfo.item.infrastructure.price.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 未来生效价格表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FutureValidCostPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 租户id
     */
    private Long adminId;

    /**
     * sku编码
     */
    private String skuCode;

    /**
     * 鲜沐运营区域编码
     */
    private Integer areaNo;

    /**
     * 生效时间
     */
    private LocalDateTime validTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
