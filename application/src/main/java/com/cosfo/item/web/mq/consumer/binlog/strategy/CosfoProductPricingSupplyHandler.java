package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Component
public class CosfoProductPricingSupplyHandler implements DbTableDmlStrategy {

    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.PRODUCT_PRICING_SUPPLY;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        LocalDateTime now = LocalDateTime.now ();
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.DELETE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue();
                Long skuId = Long.valueOf (oldMap.get ("supply_sku_id"));
                Long tenantId = Long.valueOf (oldMap.get ("tenant_id"));
                handleItemPrice (skuId,tenantId);
            }
        }
    }
    private void handleItemPrice(Long skuId, Long tenantId) {
        List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemBySkuIdAndTenantIdAndGoodsTypes (skuId,tenantId,Collections.singletonList (MarketItemEnum.GoodsType.QUOTATION.getCode ()));
        log.info ("handleItemPrice,处理商品售价marketItemVOS={}", JSON.toJSONString (marketItemVOS));
        for (MarketItemVO marketItemVO: marketItemVOS) {
            ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }
}
