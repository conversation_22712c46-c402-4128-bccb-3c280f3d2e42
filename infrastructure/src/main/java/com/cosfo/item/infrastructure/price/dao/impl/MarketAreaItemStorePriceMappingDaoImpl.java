package com.cosfo.item.infrastructure.price.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.price.model.MarketAreaItemStorePriceMapping;
import com.cosfo.item.infrastructure.price.mapper.MarketAreaItemStorePriceMappingMapper;
import com.cosfo.item.infrastructure.price.dao.MarketAreaItemStorePriceMappingDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 门店商品定价策略映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Service
public class MarketAreaItemStorePriceMappingDaoImpl extends ServiceImpl<MarketAreaItemStorePriceMappingMapper, MarketAreaItemStorePriceMapping> implements MarketAreaItemStorePriceMappingDao {

    @Override
    public List<MarketAreaItemStorePriceMapping> getByPId(Long pid) {
        LambdaQueryWrapper<MarketAreaItemStorePriceMapping> wrapper = new LambdaQueryWrapper ();
        wrapper.eq (MarketAreaItemStorePriceMapping::getAreaItemMappingId,pid);

        return list (wrapper);
    }
}
