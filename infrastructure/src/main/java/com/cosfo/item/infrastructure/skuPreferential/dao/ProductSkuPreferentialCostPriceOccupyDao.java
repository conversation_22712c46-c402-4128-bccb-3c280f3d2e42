package com.cosfo.item.infrastructure.skuPreferential.dao;

import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceOccupy;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 省心定库存占用表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
public interface ProductSkuPreferentialCostPriceOccupyDao extends IService<ProductSkuPreferentialCostPriceOccupy> {

    List<ProductSkuPreferentialCostPriceOccupy> listByOrderIdAndSkuIds(Long tenantId,Long orderId, List<Long> skuIds);

    Integer optOccupyQuantity(Long id, int i);
}
