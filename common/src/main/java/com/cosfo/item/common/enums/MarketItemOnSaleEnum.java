package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/5/5 10:21
 * @Description:
 */
public interface MarketItemOnSaleEnum {
    @Getter
    @AllArgsConstructor
    public enum OnSaleTypeEnum {
        /**
         * 下架
         */
        SOLD_OUT(0, "下架"),
        /**
         * 上架
         */
        ON_SALE(1, "上架");

        /**
         * 售后订单状态编码
         */
        private Integer code;
        /**
         * 售后订单状态描述
         */
        private String desc;

        /**
         * getDesc
         *
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
                if (Objects.equals(code, value.getCode())) {
                    return value.getDesc();
                }
            }
            return null;
        }

        /**
         * get desc
         *
         * @param desc
         * @return
         */
        public static Integer getType(String desc) {
            for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
                if (Objects.equals(desc, value.getDesc())) {
                    return value.getCode();
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    public enum MTypeEnum {
        /**
         * 不是大客户专享
         */
        NOT_BIG_CUSTOMER_EXCLUSIVITY(0,"不是大客户专享"),
        /**
         * 是大客户专享
         */
        BIG_CUSTOMER_EXCLUSIVITY(1,"是大客户专享");

        /**
         * 售后订单状态编码
         */
        private Integer code;
        /**
         * 售后订单状态描述
         */
        private String desc;

        /**
         * getDesc
         *
         * @param code
         * @return
         */
        public static String getDesc(Integer code) {
            for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
                if (Objects.equals(code, value.getCode())) {
                    return value.getDesc();
                }
            }
            return null;
        }

        /**
         * get desc
         *
         * @param desc
         * @return
         */
        public static Integer getType(String desc) {
            for (OnSaleTypeEnum value : OnSaleTypeEnum.values()) {
                if (Objects.equals(desc, value.getDesc())) {
                    return value.getCode();
                }
            }
            return null;
        }
    }
}
