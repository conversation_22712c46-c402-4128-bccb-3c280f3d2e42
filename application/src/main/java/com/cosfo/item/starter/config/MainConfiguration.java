
package com.cosfo.item.starter.config;

import com.alibaba.schedulerx.SchedulerxAutoConfigure;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

import javax.annotation.PostConstruct;

@Slf4j
@EnableAsync
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class})
@DubboComponentScan("com.cosfo.item.**.provider")
@ComponentScan(value = {"com.cosfo.item.*"})
public class MainConfiguration {

    @PostConstruct
    public void init() {
        log.info("MainConfiguration init...");
    }
}
