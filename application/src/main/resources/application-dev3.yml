server:
  port: 80
spring:
  application:
    name: item-center
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 3
      maximum-pool-size: 10
      max-lifetime: 30000   #不能小于30秒，否则默认回到1800秒
      connection-test-query: SELECT 1
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          url: ************************************************************************************************************
          username: dev3
          password: xianmu619
          driver-class-name: com.mysql.cj.jdbc.Driver
        offline:
          url: *****************************************************************************************************************************************
          username: dev3
          password: xianmu619
          driver-class-name: com.mysql.cj.jdbc.Driver
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 43622500-784e-4b79-a13e-8954271dee95
    groupId: item-center
    appKey: GJOpI9R5KEZFL1S8rL+j6g==
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 5 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    address: nacos://test-nacos.summerfarm.net:11000
    parameters:
      namespace: c26bc4c2-bd51-4aae-a170-1f04b9c52987
  protocol:
    id: dubbo
    name: dubbo
    port: 20882
  provider:
    version: 1.0.0
    group: monna
    timeout: 6000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false

rocketmq:
  consumer:
    access-key: Rocketmq
    secret-key: Rocketmq
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: Rocketmq
    group: GID_cosfo_item
    secret-key: Rocketmq
    sendMsgTimeout: 10000
tenant:
  ftTenantId: 0
  xmTenantId: 1