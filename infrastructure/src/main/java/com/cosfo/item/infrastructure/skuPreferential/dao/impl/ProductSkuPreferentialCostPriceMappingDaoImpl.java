package com.cosfo.item.infrastructure.skuPreferential.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.common.skuPreferential.enums.DeletedEnum;
import com.cosfo.item.infrastructure.skuPreferential.dao.ProductSkuPreferentialCostPriceMappingDao;
import com.cosfo.item.infrastructure.skuPreferential.mapper.ProductSkuPreferentialCostPriceMappingMapper;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceMapping;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <p>
 * 省心定报价城市关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Service
public class ProductSkuPreferentialCostPriceMappingDaoImpl extends ServiceImpl<ProductSkuPreferentialCostPriceMappingMapper, ProductSkuPreferentialCostPriceMapping> implements ProductSkuPreferentialCostPriceMappingDao {

    @Override
    public List<ProductSkuPreferentialCostPriceMapping> listByCostPriceIds(Long tenantId, List<Long> skuPreferentialIds) {
        if (Objects.isNull(tenantId) || CollectionUtils.isEmpty(skuPreferentialIds)) {
            return Collections.EMPTY_LIST;
        }
        MPJLambdaWrapper<ProductSkuPreferentialCostPriceMapping> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(ProductSkuPreferentialCostPriceMapping.class);

        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getTenantId, tenantId);
        queryWrapper.in(ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId, skuPreferentialIds);
        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getDeleted, DeletedEnum.NORMAL.getCode());

        return list(queryWrapper);
    }

    @Override
    public List<ProductSkuPreferentialCostPriceMapping> queryBySkuIdAndCityIds(Long tenantId, Long skuId, List<Long> cityIds) {
        if (Objects.isNull(tenantId) || Objects.isNull(skuId) || CollectionUtils.isEmpty(cityIds)) {
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<ProductSkuPreferentialCostPriceMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getTenantId, tenantId);
        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getSkuId, skuId);
        queryWrapper.in(ProductSkuPreferentialCostPriceMapping::getCityId, cityIds);
        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getDeleted, DeletedEnum.NORMAL.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<ProductSkuPreferentialCostPriceMapping> queryBySkuIdsAndCityId(Long tenantId, Long cityId, Set<Long> skuIds) {
        if (Objects.isNull(tenantId) || Objects.isNull(cityId) || CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<ProductSkuPreferentialCostPriceMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getTenantId, tenantId);
        queryWrapper.in(ProductSkuPreferentialCostPriceMapping::getSkuId, skuIds);
        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getCityId, cityId);
        queryWrapper.eq(ProductSkuPreferentialCostPriceMapping::getDeleted, DeletedEnum.NORMAL.getCode());
        return list(queryWrapper);
    }
    @Override
    public Boolean updateDeletedStatus(List<Long> productSkuPreferentialIds, Long tenantId, Long skuId) {
        if (CollectionUtils.isEmpty(productSkuPreferentialIds)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<ProductSkuPreferentialCostPriceMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProductSkuPreferentialCostPriceMapping::getDeleted, DeletedEnum.DELETED.getCode());
        updateWrapper.in(ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId, productSkuPreferentialIds);
        updateWrapper.in(ProductSkuPreferentialCostPriceMapping::getTenantId, tenantId);
        updateWrapper.in(ProductSkuPreferentialCostPriceMapping::getSkuId, skuId);
        return update(updateWrapper);
    }

    @Override
    public Boolean updateDeletedStatusByCityIds(List<Long> cityIds, Long tenantId, Long skuId) {
        if (CollectionUtils.isEmpty(cityIds)) {
            return Boolean.FALSE;
        }
        LambdaUpdateWrapper<ProductSkuPreferentialCostPriceMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProductSkuPreferentialCostPriceMapping::getDeleted, DeletedEnum.DELETED.getCode());
        updateWrapper.in(ProductSkuPreferentialCostPriceMapping::getCityId, cityIds);
        updateWrapper.in(ProductSkuPreferentialCostPriceMapping::getTenantId, tenantId);
        updateWrapper.in(ProductSkuPreferentialCostPriceMapping::getSkuId, skuId);
        return update(updateWrapper);
    }

    @Override
    public boolean batchSave(List<ProductSkuPreferentialCostPriceMapping> mappingList) {
        return baseMapper.batchSave(mappingList) > 0;
    }
}
