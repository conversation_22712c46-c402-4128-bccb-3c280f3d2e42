package com.cosfo.item.infrastructure.price.model;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 成本价格表log
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CostPriceLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 货品主键
     */
    private Long skuId;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 区域
     */
    private String area;

    /**
     * 城市
     */
    private String city;
    /**
     * 省
     */
    private String province;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 生效时间
     */
    private LocalDateTime validTime;

    /**
     * 失效时间
     */
    private LocalDateTime invalidTime;

    /**
     * 生效类型,0-永久,1-周期
     */
    private Integer validType;

    /**
     * 操作类型,0-新增,1-修改,2-删除
     */
    private Integer optType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
