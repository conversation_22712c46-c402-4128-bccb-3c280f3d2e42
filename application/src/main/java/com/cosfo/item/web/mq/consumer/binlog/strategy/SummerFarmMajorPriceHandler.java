package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketAreaItemEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.web.domain.service.AddressService;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import com.google.common.base.Splitter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.cosfo.item.web.mq.consumer.binlog.BinLogTimeUtil;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SummerFarmMajorPriceHandler implements DbTableDmlStrategy {

    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private AddressService addressService;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private TenantFacade tenantFacade;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Override
    public String getTableDmlName() {
        return DBTableName.SummerfarmTable.MAJOR_PRICE;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                Integer areaNo = Integer.parseInt(map.get("area_no"));
                String sku = map.get("sku");
                Long adminId = Long.valueOf(map.get("admin_id"));
                handle(areaNo,sku,adminId,map.get ("valid_time"));
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                String sku = dataMap.get ("sku");
                Long adminId = Long.valueOf(dataMap.get("admin_id"));
                Integer areaNo = Integer.parseInt(dataMap.get("area_no"));
                Map<String, String> oldMap = pair.getValue ();
                if(oldMap.containsKey ("status") ||oldMap.containsKey ("price_adjustment_value") || oldMap.containsKey ("price_type") || oldMap.containsKey ("price") || oldMap.containsKey ("valid_time") || oldMap.containsKey ("invalid_time")){
                    handle(areaNo,sku,adminId,dataMap.get ("valid_time"));
                }
            }
        }else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.DELETE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue ();
                String sku = oldMap.get ("sku");
                String adminId = oldMap.get("admin_id");
                String areaNo = oldMap.get ("area_no");
                if(ObjectUtil.isNotNull (areaNo) &&  ObjectUtil.isNotNull (adminId)) {
                    handle (Integer.parseInt (areaNo), sku, Long.valueOf(adminId),null);
                }
            }
        }
    }
    public void handle(Integer areaNo,String sku,Long adminId,String validTimeString){
        //查询用过这个sku的品牌方
        List<ProductAgentSkuMappingVO> productAgentSkuMappingVOS = productFacade.listProductMappingByAgentSkuCode (sku, xmTenantId, xmTenantId);
        if(CollectionUtil.isEmpty(productAgentSkuMappingVOS)){
            return;
        }
        Set<Long> skuIds = productAgentSkuMappingVOS.stream().filter (e-> ObjectUtil.isNotNull(e.getSkuId())).map(ProductAgentSkuMappingVO::getSkuId).collect(Collectors.toSet ());
        List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemBySkuIdsAndTypes(null, skuIds, Collections.singletonList (MarketItemEnum.GoodsType.QUOTATION.getCode()));
        if(CollectionUtil.isEmpty(marketItemVOS)){
            return;
        }
        List<Long> tenantIds = marketItemVOS.stream().map(MarketItemVO::getTenantId).collect(Collectors.toList());
        TenantVO tenantVO = tenantFacade.getTenantByAdminId(adminId);
        if(ObjectUtil.isEmpty(tenantVO)){
            return;
        }
        Long tenantId = tenantVO.getId();
        if(!tenantIds.contains(tenantId)){
            return;
        }

        if(ObjectUtil.isNotNull (validTimeString)){
            //如果生效时间在现在之后，则保存下来 走后续3分钟定时任务更新价格。
            LocalDateTime validTime = BinLogTimeUtil.formartterYMDHHMS (validTimeString);
            if(validTime.isAfter (LocalDateTime.now())){
                costPriceDomianService.saveFutureValidCostPrice (areaNo,sku,tenantId,adminId,validTime);
                return;
            }
        }

        //查询运营区域覆盖的省市区
        List<ProvinceCityAreaVO> citys = summerfarmMallFacade.getAddressInfoByAreaNo (areaNo);
        if(CollectionUtil.isEmpty(citys)){
            return;
        }

        //查询品牌方覆盖的省市区
        List<String> addresses = tenantFacade.listAddress(tenantId);
        if(CollectionUtil.isEmpty(addresses)){
            return;
        }

        Set<String> areasXMc = citys.stream ().filter (e->StringUtil.isEmpty (e.getArea ())).map (ProvinceCityAreaVO::getCity).collect (Collectors.toSet ());
        Set<String> areasXMca = citys.stream ().filter(e->StringUtil.isNotEmpty (e.getArea ())).map (e ->e.getCity () + "-" + e.getArea ()).collect (Collectors.toSet ());


        Set<String> addressResult = addressService.filterAddress (addresses,areasXMca,areasXMc);

//      根据这三个参数查询到一个价格he skuid 生效时间，生效类型
        SummerFarmCostPriceVO summerFarmCostPriceVO = sfMallManageFacade.queryMallPriceInfo4Saas(areaNo,sku,tenantVO.getAdminId());
//      根据areaNo查询到他下面的所有省市区（注意没有区的市）循环所有的省市区结果落库
        addressResult.forEach(address->{
            String province ="";
            String city ="";
            String area ="";
            try {
                List<String> pca = Splitter.on("-").splitToStream(address).map(String::valueOf).collect(Collectors.toList());
                province = pca.get(0);
                city = pca.get(1);
                area = pca.get(2);
                costPriceDomianService.synchronize4Summerfarm(sku,tenantVO.getId(),area,city,province,summerFarmCostPriceVO);
            }catch (Exception e){
                costPriceDomianService.saveCompenstateCostPrice (sku,tenantVO.getId(), area ,city, province,e.toString ());
                log.error("major_price变更 cost_price更新失败，sku={},tenantId={},area={},city={},province={},summerFarmCostPriceVO={}",sku,tenantVO.getId(),area,city,province, JSON.toJSONString(summerFarmCostPriceVO),e);
            }
        });
    }
}
