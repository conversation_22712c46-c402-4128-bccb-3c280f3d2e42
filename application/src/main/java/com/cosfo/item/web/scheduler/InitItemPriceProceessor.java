package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemEnum.ItemTypeEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemParam;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.MarketItemOnsalePriceDealService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class InitItemPriceProceessor extends XianMuJavaProcessorV2 {

    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Autowired
    private MarketItemDao itemDao;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始进行InitItemPriceProceessor任务,instanceParameters={}", JSON.toJSONString(context.getInstanceParameters()));

        if (ObjectUtil.isNull(context)) {
            return new ProcessResult(true);
        }

        Map<String, String> map = Collections.emptyMap ();
        if (!StringUtils.isEmpty (context.getInstanceParameters())) {
            map = JSON.parseObject (context.getInstanceParameters  (), Map.class);
        }else if(!StringUtils.isEmpty (context.getJobParameters ())){
            map = JSON.parseObject (context.getJobParameters (), Map.class);
        }
        List<String> failedItemIds = new LinkedList<>();
        if (map.containsKey("tenant")) {
            Long tenantId = Long.valueOf(map.get("tenant"));
            //-99数据初始化 全部数据
            if (tenantId.equals(-99L)) {
                updateByTenant(null,failedItemIds);
            } else {
                updateByTenant(tenantId,failedItemIds);
            }
        } else if (map.containsKey("item")) {
            Long itemId = Long.valueOf(map.get("item"));
            MarketItem byId = itemDao.getById(itemId);
            if (ObjectUtil.isNotNull(byId)) {
                log.info("InitItemPriceProceessor - size={}", 1);
                batchDeal(Collections.singletonList (byId),failedItemIds);
            }
        } else if (map.containsKey("items")) {
            List<Long> itemIds = Arrays.stream(map.get("items").split(",")).map(Long::valueOf).collect(Collectors.toList());
            MarketItemParam params = new MarketItemParam();
            params.setItemIds(itemIds);
            List<MarketItem> byIds = itemDao.listByParam(params);
            if (CollectionUtils.isNotEmpty(byIds)) {
                log.info("InitItemPriceProceessor - list.size={}", byIds.size());
                batchDeal(byIds,failedItemIds);
            }
        }
        if (CollectionUtil.isEmpty (failedItemIds)) {
            return new ProcessResult(true);
        } else {
            return new ProcessResult(InstanceStatus.PARTIAL_FAILED, String.join(",", failedItemIds));
        }
    }
    private void batchDeal(List<MarketItem> list, List<String> failedItemIds){
        if(CollectionUtil.isEmpty (list)){
            return;
        }
        List<MarketItem> physicalItems = new LinkedList<>(), combineItems = new LinkedList<>();
        list.forEach(marketItem -> {
            if (Objects.equals(MarketItemEnum.ItemTypeEnum.COMBINE_ITEM.getCode(), marketItem.getItemType())) {
                combineItems.add(marketItem);
            } else {
                physicalItems.add(marketItem);
            }
        });

        List<MarketItem>[] allLists = new List[]{physicalItems, combineItems};

        for (List<MarketItem> subList : allLists) {
            subList.forEach(item -> {
                try {
                    ItemChangeMessageDTO dto = ItemConverter.marketItem2MsgDTO (item);
                    marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
                } catch (Exception exception) {
                    log.error("InitItemPriceProceessor error, item:{}, is PHYSICAL_ITEM:{}", JSON.toJSONString(item),
                            Objects.equals(ItemTypeEnum.PHYSICAL_ITEM.getCode(), item.getItemType()), exception);
                    failedItemIds.add(String.valueOf(item.getId()));
                }
            });
        }
    }
    private void updateByTenant(Long tenantId,List<String> failedItemIds) {
        boolean hasNext = true;
        int pageIndex = 1;
        Long total = null;
        Page<MarketItem> page;
        while(hasNext){
            page = itemDao.pageByTenantId (tenantId, pageIndex, 100);
            batchDeal(page.getRecords (),failedItemIds);
            hasNext = page.hasNext();
            total  = page.getTotal ();
            pageIndex = pageIndex + 1;
        }
        log.info("InitItemPriceProceessor - list.size={}", total);
    }
}
