package com.cosfo.item.web.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/10 17:25
 * @Description:
 */
@Data
public class MarketInfoVO {
    /**
     * spuId
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 二级分类Id
     */
    private Long secondClassificationId;
    /**
     * 二级分类
     */
    private String secondClassificationName;
    /**
     * 一级分类Id
     */
    private Long firstClassificationId;
    /**
     * 一级分类名称
     */
    private String firstClassificationName;
    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 销售商品
     */
    private List<MarketItemVO> marketItemVOList;

    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 商品描述 文字
     */
    private String descriptionString;
}
