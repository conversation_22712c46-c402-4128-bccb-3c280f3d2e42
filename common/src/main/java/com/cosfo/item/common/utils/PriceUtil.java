package com.cosfo.item.common.utils;

import com.cosfo.item.common.dto.CombineListPriceDTO;
import com.cosfo.item.common.dto.PriceRangeDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 16:10
 * @Description:
 */
public class PriceUtil {
    public static final String RUNG = "-";

    public static String calPriceRange(BigDecimal minPrice,BigDecimal maxPrice) {
//        if (minPrice.compareTo(BigDecimal.ZERO) == 0) {
//            return RUNG;
//        } else
        if (minPrice.compareTo(maxPrice) == 0) {
            return maxPrice.toString();
        } else {
            return minPrice + RUNG + maxPrice;
        }
    }

    public static PriceRangeDTO calCombinePriceRange(List<CombineListPriceDTO> combineListPriceDTOS){
        if (CollectionUtils.isEmpty(combineListPriceDTOS)) {
            return PriceRangeDTO.builder().priceStr(RUNG).build();
        }
        BigDecimal maxPrice = BigDecimal.ZERO;
        BigDecimal minPrice = BigDecimal.ZERO;
        for (CombineListPriceDTO combineVO : combineListPriceDTOS) {
            maxPrice = maxPrice.add(Optional.ofNullable(combineVO.getMaxPrice()).orElse(BigDecimal.ZERO));
            minPrice = minPrice.add(Optional.ofNullable(combineVO.getMinPrice()).orElse(BigDecimal.ZERO));
        }
        PriceRangeDTO priceRangeDTO = PriceRangeDTO.builder()
            .maxPrice(maxPrice)
            .minPrice(minPrice)
            .build();
        if (maxPrice.compareTo(minPrice) == 0) {
            priceRangeDTO.setPriceStr(maxPrice.toString());
        } else {
            priceRangeDTO.setPriceStr(minPrice + RUNG + maxPrice);
        }
        return priceRangeDTO;
    }
}
