package com.cosfo.item.infrastructure.offline.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.infrastructure.offline.model.SelfGoodsCostPrice;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 自营货品成本价格表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface SelfGoodsCostPriceDao extends IService<SelfGoodsCostPrice> {

    Page<SelfGoodsCostPrice> pageByTenantId(Long tenantId,int i, Integer pageNum);
}
