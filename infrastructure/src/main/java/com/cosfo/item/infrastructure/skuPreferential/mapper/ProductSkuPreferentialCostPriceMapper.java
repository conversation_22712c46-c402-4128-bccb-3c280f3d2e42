package com.cosfo.item.infrastructure.skuPreferential.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.item.common.skuPreferential.dto.ProductSkuPreferentialBasicDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPrice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 省心定报价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Mapper
public interface ProductSkuPreferentialCostPriceMapper extends BaseMapper<ProductSkuPreferentialCostPrice> {

    ProductSkuPreferentialBasicDTO queryBasicData(@Param("tenantId") Long tenantId, @Param("skuIds") List<Long> skuIds, @Param("availableFlag") Boolean availableFlag,
                                                  @Param("waitValid") Boolean waitValid);

    List<ProductPricingMessageDTO> selectFutureEndTime();

    List<ProductPricingMessageDTO> selectFutureStartTime();

    Integer optAvailableQuantity(@Param("id")Long id, @Param("optQuantity")Integer optQuantity);
}
