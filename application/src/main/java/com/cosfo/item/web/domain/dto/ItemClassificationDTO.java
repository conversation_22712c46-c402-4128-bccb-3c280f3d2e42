package com.cosfo.item.web.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: monna.chen
 * @Date: 2023/5/8 18:05
 * @Description: market_item对应的分类信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ItemClassificationDTO {
    private Long marketId;
    /**
     * 一级分类的tenant_id
     */
    private Long firstTenantId;
    /**
     * 二级分类的tenant_Id
     */
    private Long secondTenantId;
    /**
     * 二级分类Id
     */
    private Long secondClassificationId;
    /**
     * 二级分类
     */
    private String secondClassificationName;
    /**
     * 一级分类Id
     */
    private Long firstClassificationId;
    /**
     * 一级分类名称
     */
    private String firstClassificationName;
    /**
     * 分类全名
     */
    private String classificationFullName;
}
