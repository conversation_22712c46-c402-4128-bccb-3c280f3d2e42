package com.cosfo.item.web.domain.service;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/31
 */
@RunWith(SpringRunner.class)
@SpringBootTest
class MarketClassificationDomainServiceTest {
    @Resource
    private MarketClassificationDomainService marketClassificationDomainService;

    @Test
    void delete() {
        marketClassificationDomainService.delete(1259L, 2L);
    }
}