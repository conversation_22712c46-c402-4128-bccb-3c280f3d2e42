package com.cosfo.item.web.mq.consumer;

import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.strategy.DbTableDmlFactory;
import com.cosfo.item.web.mq.consumer.binlog.strategy.DbTableDmlStrategy;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
@MqListener(
        topic = RocketMqConstant.Topic.MYSQL_BINLOG_SAAS,
        consumerGroup = RocketMqConstant.ConsumeGroup.MYSQL_BINLOG_COSFO_ITEM_CENTER,
        tag = RocketMqConstant.Tag.COSFO_TABLENAMES_TAG_4_COST_PRICE,
        consumeThreadMin = 1,consumeThreadMax = 1)
public class CosfoBinLogListener extends AbstractMqListener<DtsModelEvent> {
    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public void process(DtsModelEvent dtsModel) {
        //log.info("rocketmq 收到 Cosfo 消息，升级扩展包，事件类型：{}，recordId/msg-key：{}， 表：{}.{}",dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());
        //long currentTime = System.currentTimeMillis();
        DbTableDmlStrategy creator = dbTableDmlFactory.creator(dtsModel.getTable());

        if(Objects.isNull(creator)){
            return;
        }

        creator.tableDml(dtsModel);
        // rocket MQ client集成了日志了；
        //log.info("rocketmq Cosfo 消息处理结束，recordId/msg-key：{}， 耗时 >>> {} ms", dtsModel.getMsgKey(), System.currentTimeMillis() - currentTime);
    }
}