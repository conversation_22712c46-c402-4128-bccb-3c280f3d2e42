package com.cosfo.item.infrastructure.classification.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.classification.dao.MarketItemClassificationDao;
import com.cosfo.item.infrastructure.classification.dto.MarketItemClassificationQueryParam;
import com.cosfo.item.infrastructure.classification.mapper.MarketItemClassificationMapper;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 商品分类关联表(MarketItemClassification)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-04 16:43:03
 */
@Service
public class MarketItemClassificationDaoImpl extends ServiceImpl<MarketItemClassificationMapper, MarketItemClassification> implements MarketItemClassificationDao {

    @Override
    public List<MarketItemClassification> listByParam(MarketItemClassificationQueryParam param) {
        return list(buildQueryWrapper(param));
    }

    private LambdaQueryWrapper<MarketItemClassification> buildQueryWrapper(MarketItemClassificationQueryParam param) {
        LambdaQueryWrapper<MarketItemClassification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getMarketId()), MarketItemClassification::getMarketId, param.getMarketId());
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MarketItemClassification::getTenantId, param.getTenantId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getMarketIds()), MarketItemClassification::getMarketId, param.getMarketIds());
        queryWrapper.eq(Objects.nonNull(param.getClassificationId()), MarketItemClassification::getClassificationId, param.getClassificationId());
        queryWrapper.in(!CollectionUtils.isEmpty(param.getClassificationIds()), MarketItemClassification::getClassificationId, param.getClassificationIds());
        return queryWrapper;
    }
}
