package com.cosfo.item.web.domain.vo;

import com.cofso.item.client.req.MarketItemInputReq;
import com.cosfo.item.web.domain.dto.ItemClassificationDTO;
import com.cosfo.item.web.domain.dto.MarketItemUnfairPriceStrategyDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 商品
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Data
public class MarketItemVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *  =====================================market_item======================================
     */
    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * sku主键
     */
    private Long skuId;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产地
     */
    private String origin;

    /**
     * 图片
     */
    private String mainPicture;

    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 主键Id
     */
    private Long marketId;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 供应商名称
     */
    private String supplierName;
    /**
     * 无货商品供应价
     */
    private BigDecimal noGoodsSupplyPrice;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 规格备注（区间）
     */
    private String weightNotes;

    /**
     * 供应商Id
     */
    private String supplierId;

    /**
     * 最大售后数
     */
    private Integer maxAfterSaleAmount;

    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价 3组合品按总价下调固定额度,4组合品按总价下调百分比 5组合品总价
     */
    private Integer priceType;
    /**
     * 售价
     */
    private String priceStr;
    /**
     * 最大售价
     */
    private BigDecimal maxPrice;
    /**
     * 最小售价
     */
    private BigDecimal minPrice;
    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    private Integer goodsType;
    /**
     * 0=实物商品,1=虚拟商品,2=组合品
     */
    private Integer itemType;
    /**
     * 0 下架 1 上架
     *
     * @see com.cofso.item.client.enums.OnSaleTypeEnum
     */
    private Integer onSale;
    /**
     * 库存数量（只有无货商品才有）
     */
    private Integer stockAmount;
    /**
     * 价格策略
     */
    private List<MarketItemPriceStrategyVO> priceStrategyList;
    /**
     * 上下架策略
     */
    private List<MarketItemOnsaleStrategyMappingVO> onsaleStrategyList;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;
    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;

    /**
     * 视频上传人
     */
    private String videoUploadUser;
    /**
     * 视频上传时间
     */
    private LocalDateTime videoUploadTime;
//  **  MarketItemDetail
    /**
     * marketitemid
     */
    private Long marketItemId;
    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;


    /**
     * 无货商品重量, 单位kg
     */
    private BigDecimal weight;

    /**
     * 标准单价
     */
    private BigDecimal standardUnitPrice;
    /**
     * 管控门店库存0=不管;1=管控
     */
    private Boolean storeInventoryControlFlag;
    /**
     *  =====================================market_item_detail======================================
     */
    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 起售规格
     */
    private Integer baseSaleUnit;

    /**
     * 0 不展示平均价  1 展示平均价
     */
    private Integer averagePriceFlag;

    /**
     *  是否加入样本池 0 不加入 1加入
     */
    private Integer samplePool;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 外部id
     */
    private Long outId;
    /**
     *  =====================================market_item_detail======================================
     */
    /**
     *  =====================================market======================================
     */
    /**
     * 主标题
     */
    private String marketTitle;
    /**
     * 副标题
     */
    private String marketSubTitle;
    /**
     * 后台类目
     */
    private Long categoryId;
    /**
     * 主图
     */
    private String marketMainPicture;
    /**
     * 详情图
     */
    private String marketDetailPicture;

    /**
     * 删除标记
     */
    private String marketDeleteFlag;

    /**
     * 商品描述 文字
     */
    private String descriptionString;

    /**
     *  =====================================market======================================
     */
    /**
     *  =====================================market_detail======================================
     */
    /**
     * 从截单开始的售后时间
     */
    private Integer afterSaleTime;

    /**
     * 售后原因
     */
    private String afterSaleReason;

    /**
     * 退款原因,拍多/拍错/不想要
     */
    private String refundReason;

    /**
     * 描述
     */
    private String description;

    /**
     * 广告语
     */
    private String slogan;

    private Long marketOutId;
    private Integer characters;
    /**
     * 销售属性说明
     */
    private String salePropertyDesc;
    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;
    /**
     *  =====================================market_detail======================================
     */
    /**
     * 价格倒挂策略
     */
    private UnfairPriceStrategyVO unfairPriceStrategyVO;
    /**
     * 前台分类
     */
    private ItemClassificationDTO itemClassificationDTO;
    /**
     * 单位
     */
    private List<MarketItemUnitVO> marketItemUnitList;
}
