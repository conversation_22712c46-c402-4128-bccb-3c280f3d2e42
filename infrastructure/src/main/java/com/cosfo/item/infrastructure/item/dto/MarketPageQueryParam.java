package com.cosfo.item.infrastructure.item.dto;

import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/10 11:59
 * @Description:
 */
@Data
public class MarketPageQueryParam {
    /**
     * 租户Id
     */
    private Long tenantId;
    /**
     * 大客户id
     */
    private Long adminId;
    /**
     * 鲜沐商品类型
     */
    private boolean notAllFlag;
    /**
     * 页码
     */
    private Integer pageNum;
    /**
     * 分页数量
     */
    private Integer pageSize;
    /**
     * 主标题
     */
    private String title;
    /**
     * spu
     */
    private Long id;
    /**
     * 前台分类
     */
    private Long classificationId;
    /**
     * 分类ids
     */
    private List<Long> classificationIds;
    /**
     * 后台类目
     */
    private Long categoryId;
    /**
     * 类目ids
     */
    private List<Long> categoryIds;
    /**
     * 商品编码
     */
    private Long itemId;
    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * marketIds
     */
    private List<Long> marketIds;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 0下架 1上架
     */
    private Integer onSale;

    /**
     * 0-已删除 1-正常使用
     */
    private Integer deleteFlag;

    /**
     * 货品类型 0-无货商品 1-报价商品 2-自营货品 3-组合品
     */
    private Integer goodsType;

    private List<Integer> goodsTypeList;
    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 防倒挂类型 0-以供应价售卖 1-已自定义价售卖 2-自动下架
     */
    private Integer unfairPriceStrategyType;

    /**
     * 防倒挂方式 1=是,0=否
     */
    private Integer unfairPriceStrategyDefaultFlag;

    /**
     * not in
     */
    private List<Long> notInMarketItemIds;

    /**
     * in
     */
    private List<Long> inMarketItemIds;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * 管控门店库存0=不管;1=管控
     */
    private Boolean storeInventoryControlFlag;
}
