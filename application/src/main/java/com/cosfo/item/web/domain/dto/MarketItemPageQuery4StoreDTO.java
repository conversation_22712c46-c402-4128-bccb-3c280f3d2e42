package com.cosfo.item.web.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/11 18:09
 * @Description:
 */
@Data
public class MarketItemPageQuery4StoreDTO {
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 门店ID
     * 门店ID为空时认为是未登录状态
     */
    private Long storeId;
    /**
     * 标题
     */
    private String title;
    /**
     * 分类id
     */
    private Long classificationId;
    /**
     * market_id
     */
    private List<Long> itemIds;

    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;
}
