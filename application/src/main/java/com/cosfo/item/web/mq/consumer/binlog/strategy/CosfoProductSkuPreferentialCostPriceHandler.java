package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.shade.scala.Int;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.mq.consumer.binlog.BinLogTimeUtil;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

@Slf4j
@Component
public class CosfoProductSkuPreferentialCostPriceHandler implements DbTableDmlStrategy {
    @Autowired
    private MqProducer mqProducer;
    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.PRODUCT_SKU_PREFERENTIAL_COST_PRICE;
    }
    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        LocalDate currentDate = LocalDate.now();

        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                Long skuId = Long.valueOf (map.get ("sku_id"));
                Long tenantId = Long.valueOf (map.get ("tenant_id"));
                LocalDateTime startTime = BinLogTimeUtil.formartterYMDHHMS (map.get ("start_time"));
                LocalDateTime endTime = BinLogTimeUtil.formartterYMDHHMS (map.get ("end_time"));
                if(startTime.toLocalDate ().isEqual (currentDate)){
                    ProductPricingMessageDTO productPricingMessageDTO = new ProductPricingMessageDTO ();
                    productPricingMessageDTO.setTenantId(tenantId);
                    productPricingMessageDTO.setSkuId(skuId);
                    productPricingMessageDTO.setDealTime (startTime);
                    mqProducer.sendStartDeliver (RocketMqConstant.Topic.SUPPLY_PRICE,RocketMqConstant.Tag.SUPPLY_PRICE,JSON.toJSON (productPricingMessageDTO),startTime.plusSeconds (1));
                }
                if(endTime.toLocalDate ().isEqual (currentDate)){
                    ProductPricingMessageDTO productPricingMessageDTO = new ProductPricingMessageDTO ();
                    productPricingMessageDTO.setTenantId(tenantId);
                    productPricingMessageDTO.setSkuId(skuId);
                    productPricingMessageDTO.setDealTime (endTime);
                    mqProducer.sendStartDeliver (RocketMqConstant.Topic.SUPPLY_PRICE,RocketMqConstant.Tag.SUPPLY_PRICE,JSON.toJSON (productPricingMessageDTO),startTime.plusSeconds (1));
                }
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                Map<String, String> oldMap = pair.getValue ();
                Long skuId = Long.valueOf (dataMap.get ("sku_id"));
                Long tenantId = Long.valueOf (dataMap.get ("tenant_id"));
                LocalDateTime startTime = BinLogTimeUtil.formartterYMDHHMS (dataMap.get ("start_time"));
                LocalDateTime endTime = BinLogTimeUtil.formartterYMDHHMS (dataMap.get ("end_time"));
                int availableQuantity = Integer.parseInt (dataMap.get ("available_quantity"));
                //删除了一个可用的
                if (oldMap.containsKey ("deleted") && availableFlag(startTime,endTime,availableQuantity)) {
                    handleItemPrice(skuId,tenantId);
                }else if (oldMap.containsKey ("available_quantity")) {
                    int oldAvailableQuantity = Integer.parseInt (oldMap.get ("available_quantity"));
                    boolean oldAvailableFlag = availableFlag(startTime,endTime,oldAvailableQuantity);
                    boolean newAvailableFlag = availableFlag(startTime,endTime,availableQuantity);
                    //状态改变 才发送消息
                    if(!Objects.equals (oldAvailableFlag,newAvailableFlag)){
                        handleItemPrice(skuId,tenantId);
                    }

                }
            }
        }
    }
//    判断生效状态 true=生效

    private boolean availableFlag(LocalDateTime startTime,LocalDateTime endTime,int availableQuantity){
        LocalDateTime now = LocalDateTime.now ();
        if (availableQuantity > 0 && now.isAfter (startTime) && now.isBefore (endTime)) {
            return true;
        }
        return false;
    }

    private void handleItemPrice(Long skuId, Long tenantId) {
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId (tenantId,Collections.singletonList (skuId));
        if (CollectionUtils.isEmpty(marketItems)) {
            log.warn("没有找到marketItemVOS：{}", tenantId);
            return;
        }
        for (MarketItem e : marketItems) {
            ItemChangeMessageDTO dto = ItemConverter.marketItem2MsgDTO (e);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }
}
