package com.cosfo.item.infrastructure.offline.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.item.model.MarketAreaItem;
import com.cosfo.item.infrastructure.offline.model.DataSynchronizationInformation;
import com.cosfo.item.infrastructure.offline.mapper.DataSynchronizationInformationMapper;
import com.cosfo.item.infrastructure.offline.dao.DataSynchronizationInformationDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 数据同步信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Service
@DS ("offline")
public class DataSynchronizationInformationDaoImpl extends ServiceImpl<DataSynchronizationInformationMapper, DataSynchronizationInformation> implements DataSynchronizationInformationDao {

    @Override
    public DataSynchronizationInformation selectByTName(String tName) {
        LambdaQueryWrapper<DataSynchronizationInformation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq (DataSynchronizationInformation::getTName,tName);
        return getOne (wrapper);
    }
}
