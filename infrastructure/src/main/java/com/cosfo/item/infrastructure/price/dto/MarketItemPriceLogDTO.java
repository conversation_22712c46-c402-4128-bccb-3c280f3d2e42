package com.cosfo.item.infrastructure.price.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MarketItemPriceLogDTO {
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 价格目标类型 0-品牌方,1-门店,2-运营区域
     */
    private Integer targetType;

    /**
     * 价格目标id
     */
    private Long targetId;

    /**
     * sku主键
     */
    private Long marketItemId;

    /**
     * 货品主键
     */
    private Long skuId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 价格规则
     */
    private String priceStrategy;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 操作类型,0-新增,1-修改,2-删除
     */
    private Integer opsType;
    /**
     * 价格规则
     */
    private String priceRule;
}
