package com.cosfo.item.infrastructure.price.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.common.enums.CostPriceEnum;
import com.cosfo.item.infrastructure.price.dto.CostPriceQueryDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingSupplyDTO;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import com.cosfo.item.infrastructure.price.mapper.CostPriceMapper;
import com.cosfo.item.infrastructure.price.dao.CostPriceDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 成本价格表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Service
public class CostPriceDaoImpl extends ServiceImpl<CostPriceMapper, CostPrice> implements CostPriceDao {
    @Override
    public List<ProductPricingSupplyDTO> selectProductPricingSupplyDTOBySkuId(Long skuId, Set<Long> cityIds, Long tenantId) {
        return getBaseMapper ().selectProductPricingSupplyDTOBySkuId(tenantId, cityIds, skuId);
    }
    @Override
    public ProductPricingMessageDTO selectSkuIdsByProductPricingSupplyId(Long productPricingSupplyId){
        return getBaseMapper ().selectSkuIdsByProductPricingSupplyId(productPricingSupplyId);
    }
    @Override
    public List<ProductPricingMessageDTO> selectFutureEndTimeProductPricingSupply(){
        return getBaseMapper ().selectFutureEndTimeProductPricingSupply ();
    }
    @Override
    public List<ProductPricingMessageDTO> selectFutureStartTimeProductPricingSupply(){
        return getBaseMapper ().selectFutureStartTimeProductPricingSupply ();
    }
    @Override
    public List<CostPrice> listByCityAreaAndTenantId(CostPriceQueryDTO dto) {
        LambdaQueryWrapper<CostPrice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CostPrice::getTenantId,dto.getTenantId());
        wrapper.eq(CostPrice::getArea,dto.getArea());
        wrapper.eq(CostPrice::getCity,dto.getCity());
//        wrapper.eq(CostPrice::getProvince,dto.getProvince ());

        wrapper.in(CollectionUtil.isNotEmpty(dto.getSkuIds()),CostPrice::getSkuId,dto.getSkuIds());
        wrapper.in(CollectionUtil.isNotEmpty(dto.getSkuCodes()),CostPrice::getSkuCode,dto.getSkuCodes());
        wrapper.eq(ObjectUtil.isNotNull(dto.getSysType()),CostPrice::getSysType,dto.getSysType());
        if(ObjectUtil.isNotNull(dto.getValidFlag())){
            LocalDateTime now = LocalDateTime.now();
            wrapper.le(CostPrice::getValidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
            wrapper.ge(CostPrice::getInvalidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
        }
        return list(wrapper);
    }

    @Override
    public List<CostPrice> listValidCostPriceBySkuCode(String sku, Long tenantId) {
        LambdaQueryWrapper<CostPrice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CostPrice::getTenantId,tenantId);
        wrapper.eq(CostPrice::getSkuCode,sku);
        LocalDateTime now = LocalDateTime.now();
        wrapper.le(CostPrice::getValidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
        wrapper.ge(CostPrice::getInvalidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
        return list(wrapper);
    }

    @Override
    public List<CostPrice> listValidByCitysSkuIdsTenantId(Set<Long> skuIds, Set<String> citys, Long tenantId) {
        LambdaQueryWrapper<CostPrice> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CostPrice::getSkuId,skuIds);
        wrapper.in(CollectionUtil.isNotEmpty (citys),CostPrice::getCity,citys);
        LocalDateTime now = LocalDateTime.now();
        wrapper.le(CostPrice::getValidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
        wrapper.ge(CostPrice::getInvalidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
        wrapper.eq(CostPrice::getTenantId,tenantId);
        return list(wrapper);
    }

    @Override
    public List<CostPrice> listInvalidCostPrice() {
        LambdaQueryWrapper<CostPrice> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        wrapper.lt(CostPrice::getInvalidTime, LocalDateTimeUtil.format(now,"yyyy-MM-dd HH:mm:ss"));
        wrapper.eq(CostPrice::getValidType, CostPriceEnum.ValidType.CYCLE.getCode ());
        return list(wrapper);
    }

    @Override
    public void saveOrUpdateByDuplicateKey(CostPrice costPrice) {
        getBaseMapper ().saveOrUpdateByDuplicateKey(costPrice);
    }

    @Override
    public List<CostPrice> listBySkuIdsWithoutIds(Long tenantId,Set<Long> skuIds, List<Long> ids) {
        LambdaQueryWrapper<CostPrice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CostPrice::getTenantId,tenantId);
        wrapper.in(CostPrice::getSkuId,skuIds);
        wrapper.notIn (CollectionUtil.isNotEmpty (ids),CostPrice::getId, ids);
        return list(wrapper);
    }
}
