package com.cosfo.item.web.facade.converter;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.web.domain.dto.MerchantAddressDTO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.manage.client.merchant.resp.MerchantStoreAddressResp;

public class MerchantStoreConverter {

    public static MerchantStoreAddressVO merchantStoreAddressResp2VO(MerchantStoreAddressResp resp) {
        if(ObjectUtil.isEmpty (resp)){
            return null;
        }
        MerchantStoreAddressVO vo = new MerchantStoreAddressVO ();
        vo.setStoreId(resp.getStoreId ());
        vo.setProvince(resp.getProvince ());
        vo.setCity(resp.getCity ());
        vo.setArea(resp.getArea ());
        vo.setCityId(resp.getCityId ());
        vo.setAddressKey (buildCityAreaKey(resp.getCity (),resp.getArea ()));
        return vo;
    }
    public static MerchantAddressDTO merchantStoreAddress2MerchantAddressDTO(MerchantStoreAddressVO vo) {
        if(ObjectUtil.isEmpty (vo)){
            return null;
        }
        MerchantAddressDTO dto = new MerchantAddressDTO ();
        dto.setProvince(vo.getProvince ());
        dto.setCity(vo.getCity ());
        dto.setArea(vo.getArea ());
        dto.setCityId(vo.getCityId ());
        return dto;
    }
    /**
     * 用《城市_区》 来做Map和Set的key；
     * @param city
     * @param area
     * @return
     */
    public static String buildCityAreaKey(String city, String area) {
        return String.format("%s_%s", city, area);
    }
}
