package com.cosfo.item.web.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/31 14:36
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchOnSaleResultVO {

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 成功列表
     */
    private List<Long> successItemIds;

    /**
     * 失败原因
     */
    private List<OnSaleFailResultVO> resultList;
}
