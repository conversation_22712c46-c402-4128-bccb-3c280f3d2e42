package com.cosfo.item.infrastructure.classification.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.classification.model.MarketClassification;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationQueryParam;

import java.util.List;

/**
 * 商品分类表(MarketClassification)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-04 16:37:57
 */
public interface MarketClassificationDao extends IService<MarketClassification> {

    List<MarketClassification> listByParam(MarketClassificationQueryParam param);

    /**
     * 查询该一级分类的子类目个数
     *
     * @param parentId
     * @param tenantId
     * @return
     */
    Integer selectMaxSort(Long parentId, Long tenantId);
}
