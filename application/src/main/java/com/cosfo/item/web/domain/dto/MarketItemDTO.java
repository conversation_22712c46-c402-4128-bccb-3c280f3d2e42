package com.cosfo.item.web.domain.dto;


import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceStrategyDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 14:11
 * @Description: market_item详情
 */
@Data
public class MarketItemDTO {

    /**
     * 主键Id
     */
    private Long marketItemId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 销售主键Id
     */
    private Long marketId;
    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    private Integer goodsType;
    /**
     * 货源skuId
     */
    private Long skuId;
    /**
     * 商品名称
     */
    private String itemTitle;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 商品头图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 规格
     */
    private String specification;
    /**
     * 规格备注
     */
    private String weightNotes;
    /**
     * 规格单位
     */
    private String specificationUnit;
    /**
     * 产地
     */
    private String origin;
    /**
     * 供应商Id
     */
    private String supplierId;
    /**
     * 供应商
     */
    private String supplierName;
    /**
     * 无货商品供应价
     */
    private BigDecimal noGoodsSupplyPrice;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 库存
     */
    private Integer amount;
    /**
     * 变更库存
     */
    private Integer changeQuantity;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * 价格类型 0所有门店展示并统一定价 1所有门店展示单差异化定价 2部分门店展示且差异化定价
     */
    private Integer priceType;

    /**
     * 自有编码
     */
    private String itemCode;
    /**
     * 售后单位
     */
    private String afterSaleUnit;

    /**
     * 最大售后数量
     */
    private Integer maxAfterSaleAmount;

    /**
     * 0下架 1上架
     */
    private Integer onSale;

    /**
     * 上下架策略
     */
    private List<MarketItemOnsaleStrategyDTO> onSaleList;

    /**
     * 价格策略
     */
    private List<MarketItemPriceStrategyDTO> priceList;

    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;


    /**
     * 鲜沐ID
     */
    private Long outId;

    /**
     * sku性质：0、常规 2、临保 3、拆包 4、不卖 5、破袋
     */
    private Integer extType;

    /**
     * 起售规格
     */
    private Integer baseSaleUnit;

    /**
     * 0 不展示平均价  1 展示平均价
     */
    private Integer averagePriceFlag;

    /**
     * 是否加入样本池 0 不加入 1加入
     */
    private Integer samplePool;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;
    /**
     * 倒挂策略
     */
    private MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 商品标签
     */
    private String itemLabel;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 售后规则详情
     */
    private String afterSaleRuleDetail;

    /**
     * 视频上传人
     */
    private String videoUploadUser;
    /**
     * 视频上传时间
     */
    private LocalDateTime videoUploadTime;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;
    /**
     * 标准单价
     */
    private BigDecimal standardUnitPrice;
    /**
     * 管控门店库存0=不管;1=管控
     */
    private Boolean storeInventoryControlFlag;
    /**
     * 单位
     */
    private List<MarketItemUnitDTO> marketItemUnitList;

    /**
     * 供应商报价类型：0-默认类型，1-按斤报价，2-按件报价
     */
    private Integer quoteType;

    /**
     * 自动补差售后量阈值
     */
    private Integer minAutoAfterSaleThreshold;
    /**
     * 销售属性说明
     */
    private String salePropertyDesc;

    /**
     * 无货商品重量, 单位kg
     */
    private BigDecimal weight;
}
