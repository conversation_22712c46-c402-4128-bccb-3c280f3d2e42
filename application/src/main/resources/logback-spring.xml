<?xml version="1.0" encoding="UTF-8" ?>

<configuration>
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%p] [%X{xm-rqid}] [%X{token}] [%X{xm-phone}] [%X{xm-uid}] [%X{xm-platform}] [%X{xm-biz}] [%X{xm-tenant-id}] [%X{EagleEye-TraceID}] [%X{EagleEye-RpcID}] [%X{xm-inbound-flag}] [%c][%M][%L] -> %msg%n            </pattern>
        </layout>
    </appender>
    <root level="info">
        <appender-ref ref="consoleLog"/>
    </root>
</configuration>
