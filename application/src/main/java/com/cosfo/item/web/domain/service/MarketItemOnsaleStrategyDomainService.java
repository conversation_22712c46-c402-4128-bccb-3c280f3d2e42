package com.cosfo.item.web.domain.service;

import static com.cosfo.item.web.domain.converter.MarketItemOnsaleStrategyMappingConverter.marketItemOnsaleStrategyDTO2Entity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.enums.MTypeEnum;
import com.cofso.item.client.enums.OnSaleTypeEnum;
import com.cofso.item.client.req.OnsaleStrategyCommonQueryReq;
import com.cosfo.item.common.constants.StringsConstantsUtil;
import com.cosfo.item.infrastructure.item.dao.MarketItemOnsaleStrategyMappingDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;
import com.cosfo.item.web.domain.converter.MarketItemOnsaleStrategyMappingConverter;
import com.cosfo.item.web.domain.vo.MarketItemOnsaleStrategyMappingVO;
import com.cosfo.item.web.domain.vo.MarketItemOnsaleStrategyVO;
import com.google.common.collect.Iterators;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.*;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 上下架
 */
@Service
@Slf4j
public class MarketItemOnsaleStrategyDomainService {

    @Autowired
    private MarketItemOnsaleStrategyMappingDao onsaleDao;

    @Autowired
    private TransactionTemplate transactionTemplate;

    private static final int BATCH_SIZE = 1000;

    /**
     * 批量保存上下架对象
     */
    @Transactional(propagation = Propagation.REQUIRED)
    public void saveOrUpdateMarketItemOnsaleStrategy(Long tenantId, List<MarketItemOnsaleStrategyDTO> dtos) {
        if (CollectionUtil.isEmpty(dtos)) {
            return;
        }
        //确保每次更新都是同一个itemID；
        //TODO 可以先查询、对比两个字段：on_sle,后，再决定要不要insert或者update；
        Map<Long, List<MarketItemOnsaleStrategyDTO>> groupedDtoList = dtos.stream().collect(Collectors.groupingBy(MarketItemOnsaleStrategyDTO::getItemId));
        groupedDtoList.forEach((marketItemId, dtoSubList) -> {
            //将每个批次控制在1000个对象以内；
            List<MarketItemOnsaleStrategyMapping> updatingList = dtoSubList.stream().map(
                e -> marketItemOnsaleStrategyDTO2Entity(tenantId, e)).collect(Collectors.toList());
            updatingList = filterOutNoNeedUpsertItems(tenantId, marketItemId, updatingList);
            if (CollectionUtils.isEmpty(updatingList)) {
                log.warn("过滤后最终无须更新：{}, dtoSubList.get(0):{}", dtoSubList.size(), dtoSubList.get(0));
                return;
            }
            log.info("marketItemId:{}, 计划更新条数:{}, 过滤后实际更新条数:{}", marketItemId, dtoSubList.size(), updatingList.size());
            Iterators.partition(updatingList.listIterator(), BATCH_SIZE).forEachRemaining(batchList -> {
                log.info("saveOrUpdateMarketItemOnsaleStrategy 批量更新 tenantId={}, marketItemId={}, size:{}, dtos[0]={}", tenantId, marketItemId, batchList.size(),
                    JSON.toJSONString(batchList.get(0)));
                onsaleDao.saveOrUpdateByItemIdAndTargetIdAndStrategyType(batchList);
            });
        });
    }

    private List<MarketItemOnsaleStrategyMapping> filterOutNoNeedUpsertItems(long tenantId, long marketItemId, List<MarketItemOnsaleStrategyMapping> originList){
        List<MarketItemOnsaleStrategyMapping> listFromDatabase = onsaleDao.listMarketItemOnsaleStrategyByItemIds(tenantId, Lists.newArrayList(marketItemId));
        if(CollectionUtils.isEmpty(listFromDatabase)){
            log.info("数据库中无任何数据，全部都需要insert:{}, originList[0]:{}", originList.size(), originList.get(0));
            return originList;
        }

        List<MarketItemOnsaleStrategyMapping> listToBeUpsert = Lists.newLinkedList();
        Map<String, MarketItemOnsaleStrategyMapping> uniqueKeyMap = listFromDatabase.stream()
            .collect(Collectors.toMap(MarketItemOnsaleStrategyDomainService::buildOnsaleStrategyUniqueKey, mapping -> mapping));
        for (MarketItemOnsaleStrategyMapping strategyMapping : originList) {
            String uniqueKey = buildOnsaleStrategyUniqueKey(strategyMapping);
            MarketItemOnsaleStrategyMapping strategyMappingFromDb = uniqueKeyMap.get(uniqueKey);
            if (strategyMappingFromDb == null) {
                listToBeUpsert.add(strategyMapping);
                log.info("新增：{}, strategyMapping:{}", uniqueKey, strategyMapping);
                continue;
            }

            // 判断 on_sale、m_type、show_flag是否变更
            if (needUpdate(strategyMapping, strategyMappingFromDb)) {
                listToBeUpsert.add(strategyMapping);
            }

        }
        return listToBeUpsert;
    }

    private boolean needUpdate(MarketItemOnsaleStrategyMapping strategyMapping,MarketItemOnsaleStrategyMapping strategyMappingFromDb) {
        // 判断 on_sale、m_type、show_flag是否变更
        if (isCoreFieldUpdated(MarketItemOnsaleStrategyMapping::getOnSale, strategyMapping, strategyMappingFromDb)) {
            return true;
        }
        if (isCoreFieldUpdated(MarketItemOnsaleStrategyMapping::getMType, strategyMapping, strategyMappingFromDb)) {
            return true;
        }
        if (isCoreFieldUpdated(MarketItemOnsaleStrategyMapping::getShowFlag, strategyMapping, strategyMappingFromDb)) {
            return true;
        }
        if (isCoreFieldUpdated(MarketItemOnsaleStrategyMapping::getStrategyType, strategyMapping, strategyMappingFromDb)) {
            return true;
        }
        return false;
    }
    private static boolean isCoreFieldUpdated(Function<MarketItemOnsaleStrategyMapping, ? super Number> function
        , MarketItemOnsaleStrategyMapping objToUpdate, MarketItemOnsaleStrategyMapping objToCompare) {
        if (!Objects.equals(function.apply(objToUpdate), function.apply(objToCompare))) {
            log.info("更新了字段, objToUpdate:{}, objToCompare", objToUpdate, objToCompare);
            return true;
        }
        return false;
    }

    /**
     * Unique key为：tenant_id, item_id, target_id, strategy_type
     * @param strategyMapping
     * @return
     */
    private static String buildOnsaleStrategyUniqueKey(MarketItemOnsaleStrategyMapping strategyMapping) {
        return String.format("%d_%d_%d_%d", strategyMapping.getTenantId(), strategyMapping.getItemId(), strategyMapping.getTargetId(), strategyMapping.getStrategyType());
    }


    /**
     * 查询商品上下架策略
     */
    public List<MarketItemOnsaleStrategyVO> listMarketItemOnsaleStrategyByItemIds(Long tenantId, List<Long> itemIds) {
        List<MarketItemOnsaleStrategyMapping> dblist = onsaleDao.listMarketItemOnsaleStrategyByItemIds(tenantId, itemIds);
        return dblist.stream().map(MarketItemOnsaleStrategyMappingConverter::marketItemOnsaleStrategy2VO).collect(Collectors.toList());
    }
    public List<MarketItemOnsaleStrategyMappingVO> listMarketItemOnsaleStrategyMappingByItemIds(Long tenantId, List<Long> itemIds) {
        List<MarketItemOnsaleStrategyMapping> dblist = onsaleDao.listMarketItemOnsaleStrategyByItemIds(tenantId, itemIds);
        if (CollectionUtil.isEmpty(dblist)){
            return Lists.newArrayList();
        }
        return dblist.stream().map(MarketItemOnsaleStrategyMappingConverter::marketItemOnsaleStrategy2VO2).collect(Collectors.toList());
    }
    /**
     * 返回每个item的上下架状态 同一租户下有任一上架策略即为上架 key: tenant_id + item_id value: onSaleType
     *
     * @param tenantId
     * @param itemIds
     * @return
     */
    public Map<String, Integer> getMarketItemOnSale(Long tenantId, List<Long> itemIds) {
        List<MarketItemOnsaleStrategyMapping> onSaleList = onsaleDao.listByParam(MarketItemOnsaleStrategyQueryParam.builder()
          .tenantId(tenantId)
          .itemIds(itemIds)
          .build());
        return onSaleList.stream()
          .collect(Collectors.groupingBy(m -> fetchOnSaleMapGroupKey(m.getTenantId(), m.getItemId()),
            Collectors.collectingAndThen(Collectors.toList(), this::getOnSaleStatus)));
    }
    /**
     * 返回每个item的上下架状态
     *
     * @param tenantId
     * @param itemIds
     * @return
     */
    public Map<Long, OnSaleTypeEnum> listMarketItemOnSale(Long tenantId, List<Long> itemIds,List<Long> targetIds) {
        Map<Long, OnSaleTypeEnum> map = new HashMap<> ();
        Set<Long> onsleIds = onsaleDao.listOnSaleMarketItem (tenantId, itemIds,targetIds);
        itemIds.forEach (itemId->{
            if(onsleIds.contains (itemId)){
                map.put (itemId,OnSaleTypeEnum.ON_SALE);
            }else {
                map.put (itemId,OnSaleTypeEnum.SOLD_OUT);
            }
        });
        return map;
    }

    public Map<Long, MTypeEnum> listMarketItemMType(Long tenantId, List<Long> itemIds, List<Long> targetIds) {
        Map<Long, MTypeEnum> map = new HashMap<>();
        Set<Long> mTypeIds = onsaleDao.listMTypeMarketItem(tenantId, itemIds, targetIds);
        itemIds.forEach(itemId -> {
            if (mTypeIds.contains(itemId)) {
                map.put(itemId, MTypeEnum.BIG_CUSTOMER_EXCLUSIVITY);
            } else {
                map.put(itemId, MTypeEnum.NOT_BIG_CUSTOMER_EXCLUSIVITY);
            }
        });
        return map;
    }

    /**
     * 生成getMarketItemOnSale配套key
     *
     * @param tenantId
     * @param itemId
     * @return
     */
    public String fetchOnSaleMapGroupKey(Long tenantId, Long itemId) {
        return tenantId + StringsConstantsUtil.CONCAT_ADD + itemId;
    }

    private Integer getOnSaleStatus(List<MarketItemOnsaleStrategyMapping> onSaleStrategyMappingList) {
        if (onSaleStrategyMappingList.stream().anyMatch(i -> OnSaleTypeEnum.ON_SALE.getCode().equals(i.getOnSale()))) {
            return OnSaleTypeEnum.ON_SALE.getCode();
        } else {
            return OnSaleTypeEnum.SOLD_OUT.getCode();
        }
    }

    public void soldOutByMarketItemId(Long tenantId,Long marketItemId) {
        onsaleDao.soldOutByMarketItemId(tenantId,marketItemId);
    }
    public void saveOrUpdateMarketItemOnsaleStrategyByItemIdAndTargetId(Long tenantId, Long itemId, MarketItemOnsaleStrategyDTO dto) {
        List<MarketItemOnsaleStrategyMapping> dblist = onsaleDao.listMarketItemOnsaleStrategyByItemIdAndTargetId(tenantId, itemId,dto.getTargetId ());
        List<MarketItemOnsaleStrategyMapping> result = new ArrayList<> ();
        if(CollectionUtil.isNotEmpty (dblist)){
            List<MarketItemOnsaleStrategyMapping> updatingList = dblist.stream().map( e -> {
                        MarketItemOnsaleStrategyMapping marketItemOnsaleStrategyMapping = marketItemOnsaleStrategyDTO2Entity (tenantId, dto);
                        if (needUpdate(marketItemOnsaleStrategyMapping, e)) {
                            marketItemOnsaleStrategyMapping.setId (e.getId ());
                            return marketItemOnsaleStrategyMapping;
                        }else{
                            return null;
                        }
            }).filter (ObjectUtil::isNotNull).collect(Collectors.toList());
            result = updatingList;
        }else{
            MarketItemOnsaleStrategyMapping marketItemOnsaleStrategyMapping = marketItemOnsaleStrategyDTO2Entity (tenantId, dto);
            result.add (marketItemOnsaleStrategyMapping);
        }
        if(CollectionUtil.isNotEmpty (result)) {
            onsaleDao.saveOrUpdateBatch (result);
        }
    }

    public List<MarketItemOnsaleStrategyMappingVO> listMarketItemOnsaleStrategyMappingByCommonQuery(Long tenantId, OnsaleStrategyCommonQueryReq req) {
        List<MarketItemOnsaleStrategyMapping> dblist = onsaleDao.listByParam(MarketItemOnsaleStrategyQueryParam.builder()
                .tenantId(tenantId)
                .itemIds(req.getItemIds ())
                .mType (req.getMType ())
                .onSale (req.getOnSale ())
                .showFlag (req.getShowFlag ())
                .strategyType (req.getStrategyType ())
                .targetId (req.getTargetId ())
                .build());
        if (CollectionUtil.isEmpty(dblist)){
            return Lists.newArrayList();
        }
        return dblist.stream().map(MarketItemOnsaleStrategyMappingConverter::marketItemOnsaleStrategy2VO2).collect(Collectors.toList());
    }
}
