package com.cosfo.item.infrastructure.item.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 商品标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Getter
@Setter
@TableName("market_item_label")
public class MarketItemLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 状态 0-禁用 1-启用（默认）
     */
    private Integer labelStatus;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;


}
