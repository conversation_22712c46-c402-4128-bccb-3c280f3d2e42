package com.cosfo.item.infrastructure.price.dao;

import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategyMapping;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 商品item价格策略映射 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface MarketItemPriceStrategyMappingDao extends IService<MarketItemPriceStrategyMapping> {

    List<MarketItemPriceStrategyMapping> listByStrategyIds(List<Long> strategyIds);

    void removeByStrategyIdAndTargetIds(Long strategyId, List<Long> targetIds);
}
