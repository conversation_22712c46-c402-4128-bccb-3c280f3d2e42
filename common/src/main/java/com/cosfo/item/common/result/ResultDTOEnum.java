package com.cosfo.item.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 18:51
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum ResultDTOEnum {
    /**
     * 200-成功
     */
    SUCCESS(200, "成功"),
    /**
     * 403-权限不足
     */
    NO_PERMISSION(403, "登录信息已失效"),
    /**
     * 404-资源不存在
     */
    NOT_FOUND(404, "资源不存在"),
    /**
     * 500-服务器错误
     */
    SERVER_ERROR(500, "服务器错误"),
    /**
     *
     */
    PARAMETER_MISSING(501, "参数缺失"),
    /**
     * 用户名或密码错误
     */
    USER_OR_PASSWORD_WRONG(502, "用户名或密码错误"),
    /**
     * 账号失效
     */
    ACCOUNT_FAILURE(503, "账号失效"),

    /**
     * 1001-当前分类名称已存在
     */
    CLASSIFICATION_HAS_EXIST(1001, "当前分类名称已存在"),

    /**
     * 1002-当前分类下有商品
     */
    CLASSIFICATION_HAS_PRODUCT(1002, "当前分类下有商品，不可删除"),

    /**
     * 1003- 不存在此上传类型
     */
    UPLOAD_TYPE_NOT_EXIST(1003, "不存在此上传类型"),

    /**
     * 1004-最多不超过十个主图
     */
    MAIN_PICTURE_OVER(1004, "最多不超过十个主图"),

    /**
     * 1005-最多不超过十个详情图
     */
    DETAIL_PICTURE_OVER(1004, "最多不超过十个详情图"),

    /**
     * 1005-一级分类图标不能为空
     */
    PRIMARY_CLASSIFICATION_ICON_EMPTY(1005, "一级分类图标不能为空"),

    /**
     * 1006-未查询到用户信息
     */
    MERCHANT_INFO_NOT_FOUND(1006, "未查询到用户信息"),

    /**
     * 1007-报价不可用
     */
    SUPPLY_NOT_AVAILABLE(1007, "报价不可用"),

    /**
     * 1008-未查到门店信息
     */
    STORE_NOT_FOUND(1008, "未查询到门店信息"),

    /**
     * 1008-未查到门店信息
     */
    CONTACT_NOT_FOUND(1009, "未查询到联系人信息"),

    /**
     * 1010-至少需要有一个联系人
     */
    CONTACT_AT_LEAST_ONE(1010, "至少需要有一个联系人"),

    /**
     * 1016-门店名称不符合条件
     */
    STORE_NAME_ERROR(1011, "门店名称不符合条件，请检查是否包含特殊符号以及门店名称长度"),

    /**
     * 1017-门店地址不符合条件
     */
    STORE_ADDRESS_ERROR(1012, "门店地址不符合条件，请检查是否包含特殊符号以及地址长度"),

    /**
     * 1018-门店地址不符合条件
     */
    STORE_HOUSE_NUMBER_ERROR(1013, "门牌号不符合条件，请检查是否包含特殊符号以及地址长度"),

    /**
     * 1014-一级分类不可以添加商品
     */
    CLASSIFICATION_ERROR(1014, "一级分类不可以添加商品"),

    /**
     * 报价单失效
     */
    PRICING_SUPPLY_FAILURE(1015, "报价单已失效，请重新关联新的报价单"),

    /**
     * 运费错误
     */
    DELIVERY_FEE_ERROR(1016, "运费不能小于0或者大于9999"),

    /**
     * 运费小数点只可以保留两位
     */
    DELIVERY_FEE_SCALE_ERROR(1017, "运费小数点只可以保留两位"),

    /**
     * 订单状态非待配送
     */
    ORDER_STATUS_ERROR(1018, "订单状态非待配送，无法自提"),

    /**
     * 未查询到订单自提信息
     */
    ORDER_SELF_LIFTING_ERROR(1019, "未查询到订单对应仓库信息"),

    /**
     * 自提详情 不能空
     */
    ORDER_SELF_LIFTING_DETAIL_ERROR(10191, "自提详情 不能空"),
    /**
     * 未设置出账规则
     */
    NOT_SET_BILL_RULE(1020, "未设置出账规则"),

    /**
     * 账期规则已存在，无法新增
     */
    FINANCIAL_RULE_EXISTED(1021, "账期规则已存在,不可新增"),

    /**
     * 凭证不存在
     */
    NOT_FOUND_CREDENTIALS(1022, "该账单没有上传账单凭证,不能确认"),

    /**
     * 该门店状态非经营中无法关店
     */
    ONLY_SUCCESS_COULD_CLOSE(1023, "该门店状态非经营中,无法关店"),

    /**
     * 该门店状态非关店中无法开店
     */
    ONLY_CLOSE_COULD_OPEN(1023, "该门店状态非关店中,无法开店"),

    /**
     * 手机号已注册
     */
    PHONE_HAS_REGISTERED(1024, "该手机号已经是其他店铺的店长手机号，请更换一个手机号"),

    /**
     * 未读取到门店信息
     */
    EXCEL_STORE_INFO_EMPTY(1025, "未读取到门店信息，请填写门店信息后导入"),

    /**
     * 读取到导入门店超过500行
     */
    EXCEL_STORE_OVER_AMOUNT(1026, "读取到导入门店超过500行，请分批导入"),

    /**
     * 门店名称不能为空
     */
    STORE_NAME_EMPTY(1027, "门店名称不能为空"),

    /**
     * 店长手机号不能为空
     */
    MANAGER_PHONE_EMPTY(1028, "店长手机号不能为空"),

    /**
     * 店长手机号格式错误
     */
    MANAGER_PHONE_ERROR(1029, "店长手机号格式错误"),

    /**
     * 门店类型不能为空
     */
    STORE_TYPE_EMPTY(1029, "门店类型不能为空"),

    /**
     * 账期权限不能为空
     */
    BILL_PERMISSION_EMPTY(1030, "账期权限不能为空"),

    /**
     * 账期权限类型错误
     */
    BILL_PERMISSION_ERROR(1031, "账期权限类型错误"),

    /**
     * 省份或直辖市不能为空
     */
    PROVINCE_EMPTY(1032, "省份或直辖市不能为空"),

    /**
     * 错误的省份或者直辖市
     */
    PROVINCE_ERROR(1033, "错误的省份或者直辖市"),

    /**
     * 市名不能为空
     */
    CITY_EMPTY(1034, "市名不能为空"),

    /**
     * 错误的城市
     */
    CITY_ERROR(1035, "错误的市名"),

    /**
     * 市名不能为空
     */
    AREA_EMPTY(1036, "县区不能为空"),

    /**
     * 错误的城市
     */
    AREA_ERROR(1037, "错误的县区"),

    /**
     * 收货地址不能为空
     */
    ADDRESS_EMPTY(1038, "收货地址不能为空"),

    /**
     * 默认联系人姓名不能为空
     */
    CONTACT_NAME_EMPTY(1039, "默认联系人姓名不能为空"),

    /**
     * 默认联系人电话不能为空
     */
    CONTACT_PHONE_EMPTY(1040, "默认联系人电话不能为空"),

    /**
     * 默认联系人号码格式错误
     */
    CONTACT_PHONE_ERROR(1041, "默认联系人号码格式错误"),

    /**
     * 门店名称重复
     */
    STORE_NAME_REPEAT(1042, "门店名称重复"),

    /**
     * 门店名称过长
     */
    STORE_NAME_TOO_LONG(1043, "门店名称过长"),

    /**
     * 详细地址过长
     */
    ADDRESS_TOO_LONG(1044, "详细地址过长"),

    /**
     * 地址备注过长
     */
    REMARK_TOO_LONG(1045, "地址备注过长"),

    /**
     * 默认联系人姓名过长
     */
    CONTACT_NAME_TOO_LONG(1046, "默认联系人姓名过长"),

    /**
     * 门牌号过长
     */
    HOUSE_NUMBER_TOO_LONG(1047, "门牌号过长"),

    /**
     * 门店类型错误
     */
    STORE_TYPE_ERROR(1048, "门店类型错误"),

    /**
     * 行政区域长度过长
     */
    ADMINISTRATIVE_AREA_TOO_LONG(1049, "行政区域长度过长"),

    /**
     * 基于仓报价，对应数据不能为空
     */
    FOLLOW_WAREHOUSE_FAILED(1050, "基于仓报价，对应数据不能为空"),


    /**
     * 状态非待审核
     */
    STATUS_NOT_AUDITING(1051, "状态非待审核，请确认后重新发起"),

    /**
     * 售后金额不能为空
     */
    TOTAL_PRICE_EMPTY(1052, "售后金额不能为空，请输入售后金额"),

    /**
     * 售后金额不能小于0
     */
    TOTAL_PRICE_NEGATIVE(1053, "售后金额不能小于0"),

    /**
     * 售后金额不可大于申请金额
     */
    TOTAL_PRICE_TOO_MUCH(1054, "售后金额不可大于申请金额，请确认后重新发起"),

    /**
     * 无权限审核
     */
    NO_PERMISSION_REVIEW(1055, "该售后订单为优选仓订单，无权限进行审核"),

    /**
     * 未查询到商品信息
     */
    PRODUCT_NOT_FOUND(1056, "未查询到货品信息"),

    /**
     * 该商品已经发布到商品库中
     */
    PRODUCT_HAS_CREATE(1057, "该商品已经发布到商品库中"),

    /**
     * 该订单存在未处理售后单，请先处理售后单
     */
    HAVING_AFTER_SALE_ORDER(1058, "该订单存在未处理售后单，请先处理售后单"),

    /**
     * 该订单状态不是待配送状态，关单失败
     */
    CLOSE_ORDER_FAILED(1059, "该订单状态不是待配送状态，关单失败"),

    /**
     * 关单失败
     */
    CREATE_AFTER_SALE_FAILED_CLOSE_ORDER_FAILED(1060, "关单失败"),

    /**
     * 分组名称已存在
     */
    MERCHANT_STORE_GROUP_NAME_EXISTING(1061, "门店分组已经存在"),

    /**
     * 分组名称不能为空
     */
    MERCHANT_STORE_GROUP_NAME_NOT＿NULL(1062, "分组名称不能为空"),

    /**
     * 门店暂未注册
     */
    STORE_NOT_EXISTING(1063, "门店暂未注册"),

    /**
     * 门店已在当前分组存在
     */
    STORE_HAVING_EXISTING_IN_GROUP(1064, "门店已在当前分组存在"),

    /**
     * 门店已在别的分组存在
     */
    STORE_HAVING_EXISTING_IN_OTHER_GROUP(1065, "门店已在别的分组存在"),

    /**
     * 价格不能为空
     */
    PRICE_NOT_NULL(1066, "价格不能小于0.01"),

    /**
     * 价格策略中门店不能重复
     */
    PRICE_STRATEGY_STORE_NOT_REPEAT(1067, "价格策略中门店不能重复"),

    /**
     * 门店分组名称长度不能大于10
     */
    GROUP_NAME_TOO_LONG(1068, "门店分组名称长度不能大于10"),

    /**
     * 门店统一定价价格策略参数错误
     */
    ALL_STORE_UNIFIED_PRICE_ERROR(1069, "请设置统一价"),

    /**
     * 所有门店展示但差异化定价价格策略错误
     */
    ALL_STORE_DIFFERENCES_PRICE_ERROR(1070, "请新增门店信息"),

    /**
     * 部分门店展示但差异化定价价格策略参数错误
     */
    PART_STORE_DIFFERENCES_PRICE_ERROR(1071, "请新增门店信息"),

    /**
     * 货源Id不能为空
     */
    SKU_ID_NOT_NULL(1072, "货源Id不能为空"),

    /**
     * 暂无合作仓库
     */
    WAREHOUSE_NO_IS_EMPTY(1073, "暂无合作仓库，无法查询"),

    /**
     * 查询时间跨度不能超过一个月
     */
    QUERY_TIME_ERROR(1074, "查询时间跨度最久一个月"),

    /**
     * 关联门店不能为空
     */
    GROUP_STORE_ID_ERROR(1075, "关联门店不能为空"),
    /**
     * 更新的门店不存在
     */
    GROUP_STORE_NON_FOUND(1076, "目标门店不存在"),
    /**
     * 该租户没有配置默认分组
     */
    DEFAULT_GROUP_NOT_FOUND(1077, "该租户没有配置默认分组"),
    /**
     * 门店编号重复
     */
    STORE_NO_REPEAT(1078, "门店编号已存在"),
    /**
     * 该门店不存在默认分组
     */
    STORE_NO_EXISTS(1079, "该门店不存在默认分组"),
    /**
     * 该门店编号长度超过20个字符
     */
    STORE_NO_OVER_LENGTH(1080, "该门店编号长度超过20个字符"),
    /**
     * 该门店编号只能为英文或者数字
     */
    STORE_NO_FORMAT_INCORRECT(1081, "该门店编号只能为英文或者数字"),
    /**
     * 请确认传入门店都包含在默认分组中
     */
    STORE_ID_NOT_TRUE(1082, "请确认传入门店都包含在默认分组或当前分组中"),
    /**
     * 缺少分页参数
     */
    NO_PAGE_PARAMS(1083, "缺少分页参数"),
    /**
     * 修改门店时编号不能为空
     */
    UPDATE_STORE_NO_NONNULL(1084, "修改门店时编号不能为空"),
    /*
     * 请传入配送时间
     */
    DELIVERY_TIME_START(1085, "请选择配送日期范围"),
    /**
     * 请传入配送截止时间
     */
    DELIVERY_TIME_END(1086, "请选择配送日期范围"),

    /**
     * 1075-短信验证码发送失败
     */
    SEND_CODE_FAIL(1075, "短信验证码发送失败"),
    /**
     * 账期权限类型错误
     */
    BALANCE_PERMISSION_ERROR(1100, "余额权限类型错误"),

    NO_WAREHOUSE_ORDER_ERROR(1110, "无仓订单不可执行该操作"),
    TENANT_DELIVERY_RULE_CHANGE_BUSY(1111, "商城运费其他管理员正在修改,请稍后再试"),
    ORDER_CONCURRENCY_ERROR(1112, "订单操作中,请稍后再试"),
    RESENT_STOCK_LOCK_ERROR(1113, "库存不足,请和仓库确认库存足够后再试"),

    TENANT_MATCH_ERROR(1114,"租户信息不匹配"),

    TARGET_TYPE_ERROR(1115,"价格目标类型错误"),

    /**
     * 价格策略中门店不能重复
     */
    PRICE_STRATEGY_STORE_GROUP_NOT_REPEAT(1116, "价格策略中门店分组不能重复"),
    ;


    private final Integer code;

    private final String message;

    @Override
    public String toString() {
        return "ResultEnum{" +
            "code=" + code +
            ", message='" + message + '\'' +
            '}';
    }
}
