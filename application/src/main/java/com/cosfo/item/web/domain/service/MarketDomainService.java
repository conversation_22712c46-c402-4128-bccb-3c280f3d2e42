package com.cosfo.item.web.domain.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cosfo.item.common.constants.RedisKeyConstants;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.classification.dao.MarketItemClassificationDao;
import com.cosfo.item.infrastructure.classification.dto.MarketItemClassificationQueryParam;
import com.cosfo.item.infrastructure.classification.model.MarketClassification;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;
import com.cosfo.item.infrastructure.item.dao.MarketDao;
import com.cosfo.item.infrastructure.item.dao.MarketDetailDao;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketDetailParam;
import com.cosfo.item.infrastructure.item.dto.MarketItemParam;
import com.cosfo.item.infrastructure.item.dto.MarketPageQueryParam;
import com.cosfo.item.infrastructure.item.model.Market;
import com.cosfo.item.infrastructure.item.model.MarketDetail;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.model.MarketItemUnfairPriceStrategy;
import com.cosfo.item.web.domain.converter.MarketDomainConvert;
import com.cosfo.item.web.domain.dto.*;
import com.cosfo.item.web.domain.vo.AddMarketVO;
import com.cosfo.item.web.domain.vo.MarketInfoVO;
import com.cosfo.item.web.domain.vo.MarketItemVO;
import com.cosfo.item.web.domain.vo.MarketPageInfoVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.springframework.data.redis.core.RedisTemplate;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 13:53
 * @Description:
 */
@Service
@Slf4j
public class MarketDomainService {
    @Autowired
    private MarketDao marketDao;
    @Autowired
    private MarketDetailDao marketDetailDao;
    @Autowired
    private MarketItemClassificationDao marketItemClassificationDao;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketClassificationDomainService classificationDomainService;
    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private UnfairPriceStrategyDomainService unfairPriceStrategyDomainService;


    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;

    @Transactional(rollbackFor = Exception.class)
    public AddMarketVO addMarket(MarketInfoDTO dto) {
        checkValid(dto);
        // 保存market
        Market market = MarketDomainConvert.INSTANCE.convert2Market(dto);
        // 保存market_detail
        if (Objects.nonNull(dto.getOutId())) {
            // 保存 xianmu 信息
            List<MarketDetail> marketDetails = marketDetailDao.listByParam(MarketDetailParam.builder()
                .outId(dto.getOutId())
                .build());
            MarketDetail marketDetail = MarketDomainConvert.INSTANCE.convert2MarketDetail(dto);
            if (CollectionUtils.isNotEmpty(marketDetails)) {
                market.setId(marketDetails.get(0).getMarketId());
                marketDao.updateById(market);
                marketDetail.setId(marketDetails.get(0).getId());
                marketDetailDao.updateById(marketDetail);
            }else {
                marketDao.save(market);
                marketDetail.setMarketId(market.getId());
                marketDetailDao.save(marketDetail);
            }
        }else {
            marketDao.save(market);
        }

        // 保存分类
        if (Objects.nonNull(dto.getClassificationId())) {
            saveOrUpdateClass(dto,market.getId());
        }

        // 保存 market_item
        Long itemId = null;
        if (Objects.nonNull(dto.getMarketItem())) {
            dto.getMarketItem().setMarketId(market.getId());
            dto.getMarketItem().setTenantId(market.getTenantId());
            itemId = itemDomainService.saveItem(dto.getMarketItem());
        }

        return AddMarketVO.builder()
            .marketId(market.getId())
            .itemId(itemId)
            .build();
    }

    /**
     * 校验参数
     *
     * @param dto
     */
    private void checkValid(MarketInfoDTO dto) {
        if (Objects.isNull(dto.getTenantId())) {
            throw new ParamsException("租户ID不可为空");
        }
        if (xmTenantId.equals(dto.getTenantId()) && Objects.isNull(dto.getOutId())) {
            throw new ParamsException("鲜沐ID不可为空");
        }
    }


    /**
     * 更新market
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMarket(MarketInfoDTO dto) {
        // 更新market_detail
        if (Objects.nonNull(dto.getOutId())) {
            List<MarketDetail> marketDetails = marketDetailDao.listByParam(MarketDetailParam.builder()
                .outId(dto.getOutId())
                .build());
            if (CollectionUtils.isEmpty(marketDetails)) {
                throw new ParamsException("未找到商品,请先保存.");
            }
            MarketDetail marketDetail = MarketDomainConvert.INSTANCE.convert2MarketDetail(dto);
            marketDetail.setId(marketDetails.get(0).getId());
            marketDetail.setMarketId(marketDetails.get(0).getMarketId());
            dto.setMarketId(marketDetails.get(0).getMarketId());
            marketDetailDao.saveOrUpdate(marketDetail);
        }
        // 更新market
        if (Objects.isNull(dto.getMarketId())) {
            throw new ParamsException("id不能为空");
        }
        Market market = MarketDomainConvert.INSTANCE.convert2Market(dto);
        marketDao.updateById(market);

        saveOrUpdateClass(dto, market.getId());
    }

    private void saveOrUpdateClass(MarketInfoDTO dto, Long marketId) {
        List<MarketItemClassification> classificationDTOList = marketItemClassificationDao.listByParam(MarketItemClassificationQueryParam.builder()
            .tenantId(dto.getTenantId())
            .marketId(marketId)
            .build());
        if (CollectionUtils.isEmpty(classificationDTOList)) {
            MarketItemClassification marketItemClassification = new MarketItemClassification();
            marketItemClassification.setTenantId(dto.getTenantId());
            marketItemClassification.setClassificationId(dto.getClassificationId());
            marketItemClassification.setMarketId(marketId);
            marketItemClassificationDao.save(marketItemClassification);
        } else {
            MarketItemClassification marketItemClassification = classificationDTOList.get(0);
            marketItemClassification.setClassificationId(dto.getClassificationId());
            marketItemClassificationDao.updateById(marketItemClassification);
        }
    }


    /**
     * 商品列表
     *
     * @param queryDTO
     * @return
     */
    public Page<MarketPageInfoVO> queryMarketListPage(MarketPageQueryDTO queryDTO) {
        if (!dealQueryCondition(queryDTO)) {
            return null;
        }
        MarketPageQueryParam param = MarketDomainConvert.INSTANCE.convert2MarketPageParam (queryDTO);
        if(ObjectUtil.isNotEmpty (queryDTO.getAdminId ())){
//            adminid - > 查询这个admin代仓 + 全部自营
            param.setNotAllFlag (true);
            param.setAdminId (queryDTO.getAdminId ());
        }else if(ObjectUtil.isNotEmpty (queryDTO.getCharacters ()) && queryDTO.getCharacters ()==0){
//            Characters ==0 - > 仅查询自营
            param.setNotAllFlag (true);
            param.setAdminId (null);
        }
        Page<Market> marketPage = marketDao.pageMarketByParam(param);
        Page<MarketPageInfoVO> pageInfo = new Page<>();
        pageInfo.setTotal(marketPage.getTotal());
        pageInfo.setRecords(fillMarketPageInfo(marketPage.getRecords(),queryDTO.getTenantId (),queryDTO.isWithOutItemListFlag (), queryDTO.getCharacters (), queryDTO.getAdminId (),queryDTO.getMarketItemInfoQueryFlagDTO ()));
        return pageInfo;
    }

    /**
     * 查询关联表字段
     *
     * @param queryDTO
     * @return
     */
    private boolean dealQueryCondition(MarketPageQueryDTO queryDTO) {
        // 前台类目
        if (Objects.nonNull(queryDTO.getClassificationId())) {
            List<MarketClassification> marketClassifications = classificationDomainService.queryChildList(queryDTO.getClassificationId());
            if (CollectionUtils.isEmpty(marketClassifications)) {
                return false;
            }
            List<Long> classificationIds = marketClassifications.stream().map(MarketClassification::getId).collect(Collectors.toList());
            queryDTO.setClassificationIds(classificationIds);
        }

        if(Objects.nonNull(queryDTO.getUnfairPriceStrategyType()) || Objects.nonNull(queryDTO.getUnfairPriceStrategyDefaultFlag())){
            List<Integer> notInDefaultStrategyType = new ArrayList<>();
            // 查询默认的规则
            MarketItemUnfairPriceStrategyEnum.StrategyValueEnum defaultStrategyValueEnum = unfairPriceStrategyDomainService.getDefaultStrategyValueByTenantId(queryDTO.getTenantId());
            // 默认规则
            if(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.Y.getCode().equals(queryDTO.getUnfairPriceStrategyDefaultFlag())){
                if(Objects.nonNull(queryDTO.getUnfairPriceStrategyType()) && !defaultStrategyValueEnum.getCode().equals(queryDTO.getUnfairPriceStrategyType())) {
                    return false;
                }

                if(Objects.nonNull(queryDTO.getUnfairPriceStrategyType())) {
                    for (MarketItemUnfairPriceStrategyEnum.StrategyValueEnum strategyValueEnum : MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.values()) {
                        if (!strategyValueEnum.equals(defaultStrategyValueEnum)) {
                            notInDefaultStrategyType.add(strategyValueEnum.getCode());
                        }
                    }
                }

                List<MarketItemUnfairPriceStrategy> unfairPriceStrategies = unfairPriceStrategyDomainService.getByDefaultFlagAndStrategyValue(notInDefaultStrategyType, MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.N.getCode(), null, queryDTO.getTenantId());
                if(CollectionUtils.isEmpty(unfairPriceStrategies)){
                    return true;
                }

                List<Long> marketItemIds = unfairPriceStrategies.stream().map(MarketItemUnfairPriceStrategy::getItemId).collect(Collectors.toList());
                queryDTO.setNotInMarketItemIds(marketItemIds);
                return true;
            }

            if(Objects.nonNull(queryDTO.getUnfairPriceStrategyType())){
                notInDefaultStrategyType = Arrays.asList(queryDTO.getUnfairPriceStrategyType());
            }else {
                notInDefaultStrategyType = Collections.emptyList();
            }

            // 非默认规则
            List<MarketItemUnfairPriceStrategy> unfairPriceStrategies = unfairPriceStrategyDomainService.getByDefaultFlagAndStrategyValue(notInDefaultStrategyType, queryDTO.getUnfairPriceStrategyDefaultFlag(), null, queryDTO.getTenantId());
            unfairPriceStrategies = unfairPriceStrategies.stream().filter(e -> !e.getItemId().equals(-1L)).collect(Collectors.toList());
            if(Objects.nonNull(queryDTO.getUnfairPriceStrategyDefaultFlag()) && CollectionUtils.isEmpty(unfairPriceStrategies)){
                return false;
            }

            if(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.N.getCode().equals(queryDTO.getUnfairPriceStrategyDefaultFlag())) {
                List<Long> marketItemIds = unfairPriceStrategies.stream().map(MarketItemUnfairPriceStrategy::getItemId).collect(Collectors.toList());
                queryDTO.setInMarketItemIds(marketItemIds);
                return true;
            }

            if(Objects.isNull(queryDTO.getUnfairPriceStrategyDefaultFlag())){
                if(!defaultStrategyValueEnum.getCode().equals(queryDTO.getUnfairPriceStrategyType())){
                    List<Long> marketItemIds = unfairPriceStrategies.stream().map(MarketItemUnfairPriceStrategy::getItemId).collect(Collectors.toList());
                    queryDTO.setInMarketItemIds(marketItemIds);
                    return true;
                }

                if(defaultStrategyValueEnum.getCode().equals(queryDTO.getUnfairPriceStrategyType())){
                    notInDefaultStrategyType = new ArrayList<>();
                    for (MarketItemUnfairPriceStrategyEnum.StrategyValueEnum strategyValueEnum : MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.values()) {
                        if (!strategyValueEnum.equals(defaultStrategyValueEnum)) {
                            notInDefaultStrategyType.add(strategyValueEnum.getCode());
                        }
                    }
                }

                List<MarketItemUnfairPriceStrategy> unfairPriceStrategyList = unfairPriceStrategyDomainService.getByDefaultFlagAndStrategyValue(notInDefaultStrategyType, MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.N.getCode(), null, queryDTO.getTenantId());
                if(!CollectionUtils.isEmpty(unfairPriceStrategyList)) {
                    List<Long> marketItemIds = unfairPriceStrategyList.stream().map(MarketItemUnfairPriceStrategy::getItemId).collect(Collectors.toList());
                    queryDTO.setNotInMarketItemIds(marketItemIds);
                }

                return true;
            }
        }

        return true;
    }

    /**
     * 填充分页列表的其它字段
     *
     * @param pageInfoVOS
     * @return
     */
    private List<MarketPageInfoVO> fillMarketPageInfo(List<Market> pageInfoVOS,Long tenantId,boolean withOutItemListFlag, Integer characters, Long adminId,MarketItemInfoQueryFlagDTO flagDTO) {
        if (CollectionUtils.isEmpty(pageInfoVOS)) {
            return Collections.emptyList();
        }
        List<Long> marketIds = pageInfoVOS.stream()
            .map(Market::getId)
            .collect(Collectors.toList());
        /**
         * 分组名称
         */
        Map<Long, ItemClassificationDTO> itemClassMap = classificationDomainService.getItemClassification(null, marketIds);

        /**
         * item列表
         */
        Map<Long, List<MarketItemVO>> itemMap;
        if(!withOutItemListFlag) {
            itemMap = itemDomainService.queryMarketItemList (marketIds,tenantId,characters, adminId, flagDTO);
        } else {
            itemMap = Collections.emptyMap ();
        }
        /**
         * fill all info
         */
        return pageInfoVOS.stream()
            .map(i -> {
                MarketPageInfoVO marketPageInfoVO = MarketDomainConvert.INSTANCE.convert2MarketPage(i);
                ItemClassificationDTO itemClassInfo = itemClassMap.get(i.getId());
                if (Objects.nonNull(itemClassInfo) && i.getTenantId().equals(itemClassInfo.getFirstTenantId())) {
                    marketPageInfoVO.setFirstClassificationId(itemClassInfo.getFirstClassificationId());
                    marketPageInfoVO.setFirstClassificationName(itemClassInfo.getFirstClassificationName());
                }
                if (Objects.nonNull(itemClassInfo) && i.getTenantId().equals(itemClassInfo.getSecondTenantId())) {
                    marketPageInfoVO.setSecondClassificationId(itemClassInfo.getSecondClassificationId());
                    marketPageInfoVO.setSecondClassificationName(itemClassInfo.getSecondClassificationName());
                }
                marketPageInfoVO.setMarketItemVOList(itemMap.get(i.getId()));
                return marketPageInfoVO;
            })
            .collect(Collectors.toList());
    }


    /**
     * 商品详情
     * 不含item
     *
     * @return
     */
    public MarketInfoVO getMarketDetailById(Long tenantId,Long marketId) {
        Market market = marketDao.getById(marketId);
        if (Objects.isNull(market)) {
            return null;
        }
        if (Objects.nonNull(tenantId) && !tenantId.equals(market.getTenantId())) {
            return null;
        }
        MarketInfoVO marketInfoVO = MarketDomainConvert.INSTANCE.convert2MarketVo(market);
        Map<Long, ItemClassificationDTO> itemClassificationMap = classificationDomainService.getItemClassification(market.getTenantId(), Collections.singletonList(marketId));
        ItemClassificationDTO itemClassificationDTO = itemClassificationMap.get(marketId);
        if (Objects.nonNull(itemClassificationDTO)) {
            marketInfoVO.setFirstClassificationId(itemClassificationDTO.getFirstClassificationId());
            marketInfoVO.setFirstClassificationName(itemClassificationDTO.getFirstClassificationName());
            marketInfoVO.setSecondClassificationId(itemClassificationDTO.getSecondClassificationId());
            marketInfoVO.setSecondClassificationName(itemClassificationDTO.getSecondClassificationName());
        }
        return marketInfoVO;
    }
    public MarketInfoVO getMarketInfoByOutId(Long tenantId, Long outId) {
        String key = String.format ("%s:%s:%s", RedisKeyConstants.MARKET_OUTID, tenantId, outId);
        Object data = redisTemplate.opsForValue ().get(key);
        if (data != null) {
            MarketInfoVO result = JSON.parseObject((String)data, MarketInfoVO.class);
            return result;
        }
        List<MarketDetail> marketDetails = marketDetailDao.listByParam(MarketDetailParam.builder()
                .outId(outId)
                .tenantId (tenantId)
                .build());
        if(CollectionUtils.isEmpty (marketDetails)){
            return null;
        }
        Market market = marketDao.getById(marketDetails.get (0).getMarketId());
        if(ObjectUtil.isEmpty (market)){
            return null;
        }
        MarketInfoVO marketInfoVO = MarketDomainConvert.INSTANCE.convert2MarketVo(market);
        redisTemplate.opsForValue().set(key, JSON.toJSONString(marketInfoVO), 2, TimeUnit.HOURS);
        return marketInfoVO;
    }

    public List<MarketInfoVO> listMarketInfoByOutIds(Long tenantId, List<Long> outIds) {
        List<MarketDetail> marketDetails = marketDetailDao.listByParam(MarketDetailParam.builder()
                .outIds(outIds)
                .tenantId (tenantId)
                .build());
        if(CollectionUtils.isEmpty (marketDetails)){
            return Collections.emptyList ();
        }
        List<Market> markets = marketDao.listByIds (marketDetails.stream().map (MarketDetail::getMarketId).collect(Collectors.toList()));
        if(CollectionUtils.isEmpty (markets)){
            return Collections.emptyList ();
        }
        List<MarketInfoVO> marketInfoVOs = MarketDomainConvert.INSTANCE.convert2MarketVoList(markets);
        return marketInfoVOs;
    }
    @Transactional(rollbackFor = Exception.class)
    public void deleteMarket(MarketDeleteDTO dto) {
        if (Objects.isNull(dto.getId()) && Objects.isNull(dto.getTenantId())) {
            throw new ParamsException("商品Id或租户ID不能为空");
        }
        Market market = marketDao.getById(dto.getId());
        if (Objects.isNull(market)) {
            throw new ParamsException("未查询到该商品信息");
        }
        if (Objects.nonNull(dto.getTenantId()) && !market.getTenantId().equals(dto.getTenantId())) {
            throw new BizException("不可删除其它租户的商品");
        }
        List<MarketItem> marketItems = marketItemDao.listByParam(MarketItemParam.builder()
            .tenantId(dto.getTenantId())
            .marketId(market.getId())
            .deleteFlag(MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag())
            .build());
        if (CollectionUtils.isNotEmpty(marketItems)) {
            throw new BizException("请先将所有的规格的商品删除");
        }

        List<MarketItemClassification> marketItemClassifications = marketItemClassificationDao.listByParam(MarketItemClassificationQueryParam.builder()
            .marketId(market.getId())
            .build());
        if (CollectionUtils.isNotEmpty(marketItemClassifications)){
            List<Long> classIds = marketItemClassifications.stream().map(MarketItemClassification::getId).collect(Collectors.toList());
            marketItemClassificationDao.removeByIds(classIds);
        }

        Market update = new Market();
        update.setId(dto.getId());
        update.setDeleteFlag(MarketItemEnum.DeleteFlagEnum.DELETED.getFlag());
        marketDao.updateById(update);
    }

    public Set<String> queryBrandNameByTenantId(Long tenantId) {
        return marketDao.queryBrandNameByTenantId (tenantId);
    }
}
