package com.cosfo.item.web.domain.service.itempricefactory;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceLogDTO;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyVO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.UnfairPriceStrategyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 按按组合品总价百分比下调 价格生成
 */
@Service
public class MarketItemPriceBuilder4SalePriceReducePercentage implements MarketItemPriceBuilder{
    @Autowired
    private MarketItemPriceBaseBuilder marketItemPriceBaseBuilder;
    @Override
    public MarketItemPriceStrategyEnum.StrategyTypeEnum getSupportType() {
        return MarketItemPriceStrategyEnum.StrategyTypeEnum.SALE_PRICE_REDUCE_PERCENTAGE;
    }

    @Override
    public List<MarketItemPriceLogDTO> buildMarketItemPriceLogDTOS(List<MarketItemPriceStrategyVO> strategyVOS, MarketItem marketItem, List<MerchantStoreAddressVO> merchantStoreAddressVOS, UnfairPriceStrategyVO unfairPriceStrategyVO) {
        Long marketItemId = marketItem.getId ();
        Long tenantId = marketItem.getTenantId ();
        List<MarketItemPriceLogDTO> result = marketItemPriceBaseBuilder.buildMarketItemPriceLogDTOS4Combine (strategyVOS, marketItemId, tenantId);
        result.forEach (dto->{
            MarketItemPriceStrategyVO strategyVO = JSON.parseObject (dto.getPriceStrategy(),MarketItemPriceStrategyVO.class);
            dto.setPrice (NumberUtil.div(NumberUtil.mul(dto.getBasePrice (), NumberUtil.sub (BigDecimal.valueOf(100), strategyVO.getStrategyValue())),BigDecimal.valueOf(100), 2));
        });
        return result;
    }
}
