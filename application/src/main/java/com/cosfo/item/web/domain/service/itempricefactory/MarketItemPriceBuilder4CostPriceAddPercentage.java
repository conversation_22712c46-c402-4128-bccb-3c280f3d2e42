package com.cosfo.item.web.domain.service.itempricefactory;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceLogDTO;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyVO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.UnfairPriceStrategyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 *按成本价百分比上浮 售价生成
 */
@Service
public class MarketItemPriceBuilder4CostPriceAddPercentage implements MarketItemPriceBuilder{
    @Autowired
    private MarketItemPriceBaseBuilder builder;

    @Override
    public MarketItemPriceStrategyEnum.StrategyTypeEnum getSupportType() {
        return MarketItemPriceStrategyEnum.StrategyTypeEnum.COST_PRICE_ADD_PERCENTAGE;
    }

    @Override
    public List<MarketItemPriceLogDTO> buildMarketItemPriceLogDTOS(List<MarketItemPriceStrategyVO> strategyVOS, MarketItem marketItem, List<MerchantStoreAddressVO> merchantStoreAddressVOS, UnfairPriceStrategyVO unfairPriceStrategyVO) {
        Long marketItemId = marketItem.getId ();
        Long tenantId = marketItem.getTenantId ();

        List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = Collections.emptyList ();
        if (Objects.equals (MarketItemEnum.GoodsType.QUOTATION.getCode (),marketItem.getGoodsType ())) {
            marketItemPriceLogDTOS  = builder.buildMarketItemPriceLogDTOS4Quotation (strategyVOS, marketItem, merchantStoreAddressVOS);
        }else if(Objects.equals (MarketItemEnum.GoodsType.SELF_SUPPORT.getCode (),marketItem.getGoodsType ()) ){
            marketItemPriceLogDTOS = builder.buildMarketItemPriceLogDTOS4SelfSupport (strategyVOS, marketItem, merchantStoreAddressVOS);
        }else if(Objects.equals (MarketItemEnum.GoodsType.VIRTUAL.getCode (),marketItem.getGoodsType ()) ){
            List<MarketItemPriceLogDTO> result = new ArrayList<> ();
            strategyVOS.forEach (strategyVO -> strategyVO.getTargetIds ().forEach (targetId -> {
                MarketItemPriceLogDTO marketItemPriceLogDTO = builder.initMarketItemPriceLogDTO (marketItemId, tenantId, strategyVO, targetId, strategyVO.getTargetType (), marketItem.getSkuId ());
                marketItemPriceLogDTO.setPrice (marketItemPriceLogDTO.getBasePrice ());
                marketItemPriceLogDTO.setBasePrice(marketItem.getNoGoodsSupplyPrice() == null ? marketItemPriceLogDTO.getPrice() : marketItem.getNoGoodsSupplyPrice());
                result.add (marketItemPriceLogDTO);
            }));
            marketItemPriceLogDTOS = result;
        }
        marketItemPriceLogDTOS.forEach (dto->{
            MarketItemPriceStrategyVO strategyVO = JSON.parseObject (dto.getPriceStrategy(),MarketItemPriceStrategyVO.class);
            dto.setPrice (NumberUtil.div(NumberUtil.mul(dto.getBasePrice (), NumberUtil.add(BigDecimal.valueOf(100), strategyVO.getStrategyValue())),BigDecimal.valueOf(100), 2));
        });
        return marketItemPriceLogDTOS;
    }
}
