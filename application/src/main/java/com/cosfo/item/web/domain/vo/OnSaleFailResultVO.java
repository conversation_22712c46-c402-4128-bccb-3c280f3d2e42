package com.cosfo.item.web.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: monna.chen
 * @Date: 2023/5/31 14:37
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnSaleFailResultVO {

    /**
     * 失败的商品编码
     */
    private Long itemId;

    /**
     * 失败的原因
     */
    private String failReason;
}
