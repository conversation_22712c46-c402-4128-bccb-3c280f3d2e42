package com.cosfo.item.web.facade.converter;

import com.cosfo.item.web.domain.dto.ProductQueryDTO;
import com.cosfo.item.web.domain.vo.ProductPricingSupplyCityMappingVO;
import com.cosfo.item.web.domain.vo.ProductSkuVO;
import com.cosfo.manage.client.product.resp.ProductPricingSupplyCityMappingResp;
import net.summerfarm.goods.client.req.ProductSkuDetailQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/5 14:00
 * @Description:
 */
@Mapper
public interface ProductConvert {
    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    ProductSkuDetailQueryReq convert2Req(ProductQueryDTO dto);

    ProductSkuVO convert2SkuVO(ProductSkuDetailResp resp);

    List<ProductPricingSupplyCityMappingVO> convert2VOs(List<ProductPricingSupplyCityMappingResp> resps);

    ProductPricingSupplyCityMappingVO convert2VO(ProductPricingSupplyCityMappingResp resp);
}
