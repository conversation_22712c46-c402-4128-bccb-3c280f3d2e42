<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.item.mapper.MarketItemOnsaleStrategyMappingMapper">
    <insert id="saveOrUpdateByItemIdAndTargetIdAndStrategyType" parameterType="com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping">
        INSERT INTO `market_item_onsale_strategy_mapping`( `tenant_id`, `m_type`, `show_flag`, `item_id`, `target_id`, `on_sale`, `strategy_type`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantId}, #{item.mType}, #{item.showFlag}, #{item.itemId}, #{item.targetId}, #{item.onSale},#{item.strategyType})
        </foreach>
            ON DUPLICATE KEY UPDATE
            m_type = values(m_type),
            show_flag = values(show_flag),
            on_sale = values(on_sale)
    </insert>
</mapper>
