package com.cosfo.item.web.mq.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.common.enums.MarketItemOnSaleEnum;
import com.cosfo.item.common.enums.MarketItemOnsaleStrategyMappingEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.service.MarketItemOnsaleStrategyDomainService;
import com.cosfo.item.web.domain.service.MarketItemPriceDomianService;
import com.cosfo.item.web.domain.vo.MarketItemOnsaleStrategyVO;
import com.cosfo.item.web.domain.vo.MarketItemPriceVO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.PriceDetailVO;
import com.cosfo.item.web.facade.MerchantStoreFacade;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * 描述: 接收到上下架变更通知
 */
@Slf4j
@Component
@MqOrderlyListener(topic = RocketMqConstant.Topic.ITEM_ONSALE,
    consumerGroup = RocketMqConstant.ConsumeGroup.ITEM_ONSALE,
    tag = RocketMqConstant.Tag.ITEM_ONSALE,
    consumeThreadMin = 1,consumeThreadMax = 1
)

public class OrderSelfItemOnsaleListener extends AbstractMqListener<ItemChangeMessageDTO> {

    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private MarketItemPriceDomianService itemPriceDomianService;
    @Autowired
    private MarketItemOnsaleStrategyDomainService marketItemOnsaleStrategyDomainService;
    @Autowired
    private MerchantStoreFacade storeFacade;

    @Override
    public void process(ItemChangeMessageDTO dto) {
        log.info("收到market item上下架变更通知消息，内容：{}", JSONObject.toJSONString(dto));
        Long tenantId = dto.getTenantId();
        Long marketItemId = dto.getMarketItemId();
        Integer onSale = dto.getOnSale();
        if (null == onSale) {
            MarketItem marketItem = marketItemDao.getById(marketItemId);
            if (ObjectUtil.isNull(marketItem)) {
                return;
            }
            onSale = marketItem.getOnSale();
        }
        boolean onsale = Objects.equals(MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode(), onSale);

        List<MerchantStoreAddressVO> storeList = Collections.EMPTY_LIST;
        Set<Long> storeIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(dto.getStoreIds())) {
            storeIds = dto.getStoreIds();
        } else if (null != dto.getStoreId()) {
            storeIds.add(dto.getStoreId());
        } else {
            log.info("查询tenant全部门店：{}", tenantId);
            storeList = storeFacade.batchQueryStoreAddressFromCache(tenantId);
        }
        if (!CollectionUtils.isEmpty(storeIds)) {
            log.info("只查询部分门店：{}", JSON.toJSONString(storeIds));
            storeList = storeFacade.batchQueryStoreAddressByIds(tenantId, Lists.newArrayList(storeIds));
        }

        if (CollectionUtil.isEmpty(storeList)) {
            return;
        }
        List<MarketItemOnsaleStrategyDTO> result = new ArrayList<>();
        List<Long> marketItemIds = Collections.singletonList(marketItemId);
        //上架 过滤有价格门店上架、没有价格的门店下架
        if (onsale) {
            List<MarketItemPriceVO> marketItemPriceVOS = itemPriceDomianService.listAllPriceByItemIds(tenantId, marketItemIds);
            if (CollectionUtil.isEmpty(marketItemPriceVOS)) {
                marketItemOnsaleStrategyDomainService.soldOutByMarketItemId(tenantId, marketItemId);
            } else {
                List<MarketItemOnsaleStrategyVO> dbOnsaleList = marketItemOnsaleStrategyDomainService.listMarketItemOnsaleStrategyByItemIds(tenantId, marketItemIds);
                Map<Long, Integer> dbOnsaleStoreMap;
                if (CollectionUtil.isEmpty(dbOnsaleList)) {
                    dbOnsaleStoreMap = Collections.emptyMap();
                } else {
                    dbOnsaleStoreMap = dbOnsaleList.stream().filter(e -> Objects.equals(e.getStrategyType(), MarketItemOnsaleStrategyMappingEnum.StrategyTypeEnum.STORE.getCode()))
                        .collect(Collectors.toMap(MarketItemOnsaleStrategyVO::getTargetId, MarketItemOnsaleStrategyVO::getOnSale));
                }

                MarketItemPriceVO priceVO = marketItemPriceVOS.get(0);

                PriceDetailVO tenantPrice = priceVO.getTenantPrice();

                Map<Long, PriceDetailVO> storePriceList = priceVO.getStorePriceList();

                for (MerchantStoreAddressVO merchantStoreAddressVO : storeList) {
                    Long storeId = merchantStoreAddressVO.getStoreId();
                    Integer priceOnsale = null;
                    if (ObjectUtil.isNotNull(tenantPrice)) {
                        priceOnsale = MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode();
                    }
                    Integer dbOnsale = dbOnsaleStoreMap.get(storeId);
                    if (ObjectUtil.isEmpty(priceOnsale)) {
                        if (storePriceList.containsKey(storeId)) {
                            priceOnsale = MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode();
                        } else {
                            priceOnsale = MarketItemOnSaleEnum.OnSaleTypeEnum.SOLD_OUT.getCode();
                        }
                    }
                    if (!Objects.equals(dbOnsale, priceOnsale)) {
                        MarketItemOnsaleStrategyDTO onsaleStrategyDTO = getMarketItemOnsaleStrategyDTO(marketItemId, priceOnsale, merchantStoreAddressVO.getStoreId());
                        result.add(onsaleStrategyDTO);
                    }
                }
            }
        } else {
            for (MerchantStoreAddressVO merchantStoreAddressVO : storeList) {
                MarketItemOnsaleStrategyDTO onsaleStrategyDTO = getMarketItemOnsaleStrategyDTO(marketItemId, MarketItemOnSaleEnum.OnSaleTypeEnum.SOLD_OUT.getCode(),
                    merchantStoreAddressVO.getStoreId());
                result.add(onsaleStrategyDTO);
            }
        }
        if (CollectionUtil.isNotEmpty(result)) {
            marketItemOnsaleStrategyDomainService.saveOrUpdateMarketItemOnsaleStrategy(tenantId, result);
        }
    }

    private static MarketItemOnsaleStrategyDTO getMarketItemOnsaleStrategyDTO(Long marketItemId, Integer priceOnsale, Long storeId) {
        MarketItemOnsaleStrategyDTO dto = new MarketItemOnsaleStrategyDTO();
        dto.setItemId(marketItemId);
        dto.setTargetId(storeId);
        dto.setStrategyType(MarketItemOnsaleStrategyMappingEnum.StrategyTypeEnum.STORE.getCode());
        dto.setOnSale(priceOnsale);
        return dto;
    }
}
