package com.cosfo.item.web.provider.converter;

import com.cofso.item.client.req.CombineAddReq;
import com.cofso.item.client.req.CombineMarketQueryInputReq;
import com.cofso.item.client.resp.CombineItemResp;
import com.cofso.item.client.resp.CombineMarketDetailResp;
import com.cofso.item.client.resp.CombineMarketListResp;
import com.cosfo.item.infrastructure.item.model.Market;
import com.cosfo.item.web.domain.dto.CombineAddDTO;
import com.cosfo.item.web.domain.dto.CombineMarketQueryDTO;
import com.cosfo.item.web.domain.vo.CombineMarketDetailVO;
import com.cosfo.item.web.domain.vo.CombineMarketListVO;
import com.cosfo.item.web.domain.vo.MarketCombineVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

import static com.cosfo.item.common.constants.NumberConstants.PAGE_NUM;
import static com.cosfo.item.common.constants.NumberConstants.PAGE_SIZE;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 10:20
 * @Description:
 */
@Mapper
public interface CombineMarketConvert {
    CombineMarketConvert INSTANCE = Mappers.getMapper(CombineMarketConvert.class);

    List<CombineItemResp> convert2CombineItems(List<MarketCombineVO> vos);

    @Mapping(source = "pageNum", target = "pageNum", defaultValue = PAGE_NUM)
    @Mapping(source = "pageSize", target = "pageSize", defaultValue = PAGE_SIZE)
    CombineMarketQueryDTO convert2QueryDto(CombineMarketQueryInputReq inputReq);

    List<CombineMarketListResp> convert2Resps(List<CombineMarketListVO> list);

    CombineMarketDetailResp convert2Resp(CombineMarketDetailVO vo);

    CombineAddDTO convert2Dto(CombineAddReq resp);

    @Mapping(source = "combineMarketId", target = "id")
    @Mapping(source = "combineMarketTitle", target = "title")
    @Mapping(source = "combineMarketSubTitle", target = "subTitle")
    Market convert2Market(CombineAddDTO dto);
}
