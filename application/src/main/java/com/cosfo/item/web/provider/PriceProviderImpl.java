package com.cosfo.item.web.provider;

import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cofso.item.client.provider.PriceProvider;
import com.cofso.item.client.req.MaxCostPriceQueryReq;
import com.cofso.item.client.req.PriceStrategyFloatingRangeReq;
import com.cofso.item.client.resp.PriceStrategyRangeResp;
import com.cosfo.item.web.domain.dto.MarketPageQueryDTO;
import com.cosfo.item.web.domain.dto.MaxCostPriceQueryDTO;
import com.cosfo.item.web.domain.service.UnfairPriceStrategyDomainService;
import com.cosfo.item.web.domain.vo.PriceStrategyRangeVO;
import com.cosfo.item.web.provider.converter.ItemPricePrividerConverter;
import com.cofso.item.client.resp.ItemPriceDetailResp;
import com.cofso.item.client.resp.PriceDetailResp;
import com.cosfo.item.web.domain.service.PriceDomianService;
import com.cosfo.item.web.domain.vo.ItemPriceDetailVO;
import com.cosfo.item.web.domain.vo.PriceDetailVO;
import com.cosfo.item.web.provider.converter.MarketConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class PriceProviderImpl implements PriceProvider {
    @Resource
    private PriceDomianService priceDomianService;
    @Resource
    private UnfairPriceStrategyDomainService unfairPriceStrategyDomainService;
    @Override

    public DubboResponse<Map<Long, PriceDetailResp>> listItemPriceDetailByItemIds(Long tenantId, Long targetId, Integer targetType, List<Long> itemIds){
        Map<Long, PriceDetailVO> priceDetailVOMap = priceDomianService.listItemPriceDetailByItemIds (tenantId, targetId, targetType, itemIds);
        Map<Long, PriceDetailResp> reult = new HashMap<> ();
        priceDetailVOMap.forEach ((key,value)-> reult.put (key,ItemPricePrividerConverter.priceDetailVO2Resp(value)));
        return DubboResponse.getOK(reult);
    }

    @Override
    public DubboResponse<Map<Long, PriceDetailResp>> listItemPriceDetailByItemIds(Long tenantId, Long targetId, Integer targetType, Map<Long, Integer> itemBuyAmount,boolean throwExceptionFlag) {
        Map<Long, PriceDetailVO> priceDetailVOMap = priceDomianService.listItemPriceDetailByItemIdsWithQuantity (tenantId, targetId, targetType, itemBuyAmount, throwExceptionFlag);
        Map<Long, PriceDetailResp> reult = new HashMap<> ();
        priceDetailVOMap.forEach ((key,value)-> reult.put (key,ItemPricePrividerConverter.priceDetailVO2Resp(value)));
        return DubboResponse.getOK(reult);
    }

    @Override
    public DubboResponse<List<ItemPriceDetailResp>> listItemPriceDetailByItemIds4CombineItem(Long tenantId, Long storeId, List<Long> itemIds) {
        List<ItemPriceDetailVO> itemPriceDetailVOS = priceDomianService.listItemPriceDetailByItemIds4CombineItem (tenantId, storeId, itemIds);
        return DubboResponse.getOK(itemPriceDetailVOS.stream().map (ItemPricePrividerConverter::itemPriceDetailVO2Resp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<List<ItemPriceDetailResp>> listItemPriceDetailByItemIds4CombineItem(Long tenantId, Long storeId, Map<Long, Integer> itemBuyAmount,boolean throwExceptionFlag) {
        List<ItemPriceDetailVO> itemPriceDetailVOS = priceDomianService.listItemPriceDetailByItemIds4CombineItem (tenantId, storeId, itemBuyAmount,throwExceptionFlag);
        return DubboResponse.getOK(itemPriceDetailVOS.stream().map (ItemPricePrividerConverter::itemPriceDetailVO2Resp).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<BigDecimal> queryMaxCostPrice(MaxCostPriceQueryReq req) {
        return DubboResponse.getOK(priceDomianService.queryMaxCostPrice (ItemPricePrividerConverter.maxCostPriceQueryReq2DTO(req)));
    }

    @Override
    public DubboResponse<PriceStrategyRangeResp> batchQueryPriceStrategy(Long tenantId, List<Long> marketItemIds) {
        PriceStrategyRangeVO priceStrategyRangeVO = priceDomianService.batchQueryPriceStrategy (tenantId, marketItemIds);
        return DubboResponse.getOK(ItemPricePrividerConverter.priceStrategyRangeVO2Resp(priceStrategyRangeVO));
    }

    @Override
    public DubboResponse<Void> batchUpdatePriceStrategy(PriceStrategyFloatingRangeReq req) {
        priceDomianService.batchUpdatePriceStrategy (ItemPricePrividerConverter.priceStrategyFloatingRangeReq2DTO(req));
        return DubboResponse.getOK();
    }

    @Override
    public DubboResponse<MarketItemUnfairPriceStrategyEnum.StrategyValueEnum> queryDefaultUnfairPriceStrategy(Long tenantId) {
        return DubboResponse.getOK(unfairPriceStrategyDomainService.getDefaultStrategyValueByTenantId (tenantId));
    }

    @Override
    public DubboResponse<Void> upsertDefaultUnfairPriceStrategy(Long tenantId, MarketItemUnfairPriceStrategyEnum.StrategyValueEnum strategyValueEnum) {
        unfairPriceStrategyDomainService.upsertDefaultUnfairPriceStrategy(tenantId,strategyValueEnum);
        return DubboResponse.getOK();
    }
}
