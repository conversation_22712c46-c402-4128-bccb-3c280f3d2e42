package com.cosfo.item.web.domain.service.itempricefactory;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.enums.MarketItemPriceEnum;
import com.cosfo.item.common.enums.MarketItemPriceLogEnum;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.infrastructure.item.dao.MarketCombineItemMappingDao;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketCombineItemMapping;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceLogDTO;
import com.cosfo.item.web.domain.dto.MerchantAddressDTO;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.service.MarketItemPriceDomianService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.MerchantStoreFacade;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.facade.converter.MerchantStoreConverter;
import com.cosfo.item.web.skuPreferential.domain.SkuPreferentialDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MarketItemPriceBaseBuilder {

    @Autowired
    private CostPriceDomianService costPriceService;

    @Autowired
    private MarketItemPriceDomianService itemPriceDomianService;

    @Autowired
    private MerchantStoreFacade storeFacade;

    @Autowired
    private TenantFacade tenantFacade;

    @Autowired
    private ProductFacade productFacade;

    @Autowired
    private MarketCombineItemMappingDao combineItemMappingDao;

    @Autowired
    private SkuPreferentialDomainService preferentialDomainService;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;

    /**
     * 按照组合品 计算
     *
     * @param strategyVOS
     * @param marketItemId
     * @param tenantId
     * @return
     */
    public List<MarketItemPriceLogDTO> buildMarketItemPriceLogDTOS4Combine(List<MarketItemPriceStrategyVO> strategyVOS, Long marketItemId, Long tenantId) {
        List<MarketCombineItemMapping> subItemMappings = combineItemMappingDao.listByParam(MarketCombineQueryParam.builder()
            .itemId(marketItemId)
            .build());
        List<Long> itemIds = subItemMappings.stream().map(MarketCombineItemMapping::getCombineItemId).collect(Collectors.toList());

        List<MarketItemPriceLogDTO> result = new ArrayList<>();
        Map<Integer, List<MarketItemPriceStrategyVO>> targetTypeMap = strategyVOS.stream().collect(Collectors.groupingBy(MarketItemPriceStrategyVO::getTargetType));

        MarketItemPriceStrategyVO tenantStrategyVO =
            targetTypeMap.containsKey(MarketItemPriceStrategyEnum.TargetTypeEnum.TENANT.getCode()) ? targetTypeMap.get(MarketItemPriceStrategyEnum.TargetTypeEnum.TENANT.getCode())
                .stream().findFirst().orElse(null) : null;
        if (ObjectUtil.isEmpty(tenantStrategyVO)) {
            return result;
        }
        //查询所有的门店
        List<MerchantStoreAddressVO> storeList = storeFacade.batchQueryStoreAddressFromCache(tenantId);
        if (CollectionUtil.isEmpty(storeList)) {
            return result;
        }
        List<MarketItemPriceVO> marketItemPriceVOS = itemPriceDomianService.listAllPriceByItemIds(tenantId, itemIds);
        Map<Long, MarketItemPriceVO> priceVOMap = marketItemPriceVOS.stream().collect(Collectors.toMap(MarketItemPriceVO::getMarketItemId, Function.identity()));
        for (MerchantStoreAddressVO merchantStoreAddressVO : storeList) {
            Long storeId = merchantStoreAddressVO.getStoreId();
            MarketItemPriceLogDTO dto = initMarketItemPriceLogDTO(marketItemId, tenantId, tenantStrategyVO, storeId, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode(), null);
            if (CollectionUtil.isEmpty(priceVOMap)) {
                dto.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode());
                continue;
            }
            BigDecimal basePrice = BigDecimal.ZERO;
            for (MarketCombineItemMapping subItem : subItemMappings) {
                Integer quantity = subItem.getQuantity();

                MarketItemPriceVO itemPriceVO = priceVOMap.get(subItem.getCombineItemId());
                if (ObjectUtil.isEmpty(itemPriceVO)) {
                    dto.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode());
                } else {
                    Map<Long, PriceDetailVO> storePriceList = itemPriceVO.getStorePriceList();
                    PriceDetailVO tenantPrice = itemPriceVO.getTenantPrice();
                    if (storePriceList.containsKey(storeId)) {
                        basePrice = basePrice.add(storePriceList.get(storeId).getPrice().multiply(new BigDecimal(quantity)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else if (ObjectUtil.isNotNull(tenantPrice)) {
                        basePrice = basePrice.add(tenantPrice.getPrice().multiply(new BigDecimal(quantity)).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        dto.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode());
                    }
                }
                dto.setBasePrice(basePrice);
            }
            result.add(dto);
        }

        return result;
    }

    public List<MarketItemPriceLogDTO> buildMarketItemPriceLogDTOS4Quotation(List<MarketItemPriceStrategyVO> strategyVOS, MarketItem marketItem,
                                                                             List<MerchantStoreAddressVO> storeList) {
        Long marketItemId = marketItem.getId();
        Long tenantId = marketItem.getTenantId();

        List<MarketItemPriceLogDTO> result = new ArrayList<>();

        Map<Long, MerchantStoreAddressVO> storeAddressMap = storeList.stream().collect(Collectors.toMap(MerchantStoreAddressVO::getStoreId, Function.identity()));
        Set<MerchantAddressDTO> addressDTOS = storeList.stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MerchantStoreAddressVO::getAddressKey))),
                        ArrayList::new
                )).stream().map (MerchantStoreConverter::merchantStoreAddress2MerchantAddressDTO).collect(Collectors.toSet ());

        String sku = null;
        if (!Objects.isNull(marketItem.getSkuId())) {
            ProductAgentSkuMappingVO productAgentSkuMapping = productFacade.getProductMappingBySkuIdAndTenantId (marketItem.getSkuId (), xmTenantId, xmTenantId);
            if (!Objects.isNull (productAgentSkuMapping)) {
                sku = productAgentSkuMapping.getAgentSkuCode ();
            }
        }
        List<CostPriceVO> areaCostPriceDTOS = costPriceService.selectProductPricingSupplyPriceDTOBySkuId (marketItem.getSkuId (), sku, addressDTOS, tenantId);
        Map<String, CostPriceVO> areaCostMap = areaCostPriceDTOS.stream().collect(Collectors.toMap(CostPriceVO::getAddressKey, Function.identity()));


        strategyVOS.forEach(strategyVO -> strategyVO.getTargetIds().forEach(storeId -> {
            MerchantStoreAddressVO merchantStoreAddressVO = storeAddressMap.get(storeId);
            CostPriceVO areaCostPriceDTO = null;
            if (ObjectUtil.isNotNull(merchantStoreAddressVO) && ObjectUtil.isNotNull(merchantStoreAddressVO.getCityId())) {
                areaCostPriceDTO = areaCostMap.get (merchantStoreAddressVO.getAddressKey ());
            }
            result.add(initMarketItemPriceLogDTO4CostPrice(marketItemId, tenantId, strategyVO,storeId, areaCostPriceDTO));
        }));
        return result;
    }

    private MarketItemPriceLogDTO initMarketItemPriceLogDTO4CostPrice(Long marketItemId, Long tenantId, MarketItemPriceStrategyVO tenantStrategyVO,
        Long storeId,CostPriceVO areaCostPriceDTO) {
        log.info ("initMarketItemPriceLogDTO4CostPrice,marketItemId={}, tenantId={}, tenantStrategyVO={}, storeId={}, areaCostPriceDTO={}", marketItemId,  tenantId, JSON.toJSONString (tenantStrategyVO), storeId,JSON.toJSONString (areaCostPriceDTO));
        MarketItemPriceLogDTO dto = initMarketItemPriceLogDTO(marketItemId, tenantId, tenantStrategyVO, storeId, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode(), null);
        if (!ObjectUtil.isNull(areaCostPriceDTO)) {
            Long skuId = areaCostPriceDTO.getSkuId();
            dto.setSkuId(skuId);
            BigDecimal basePrice = areaCostPriceDTO.getPrice ();
            if (ObjectUtil.isEmpty(basePrice)) {
                dto.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode());
                return dto;
            }
            dto.setBasePrice(basePrice);
        } else {
            dto.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode());
        }
        return dto;
    }

    public MarketItemPriceLogDTO initMarketItemPriceLogDTO(Long marketItemId, Long tenantId, MarketItemPriceStrategyVO tenantStrategyVO, Long targetId, Integer targetType,
        Long skuId) {
        MarketItemPriceLogDTO dto = new MarketItemPriceLogDTO();
        dto.setTenantId(tenantId);
        dto.setTargetType(targetType);
        dto.setTargetId(targetId);
        dto.setMarketItemId(marketItemId);
        dto.setSkuId(skuId);
        tenantStrategyVO.setTargetIds(null);
        dto.setPriceStrategy(JSON.toJSONString(tenantStrategyVO));
        dto.setOpsType(MarketItemPriceLogEnum.OptType.ADD.getCode());
        dto.setBasePrice(tenantStrategyVO.getStrategyValue());
        return dto;
    }

    public List<MarketItemPriceLogDTO> buildMarketItemPriceLogDTOS4SelfSupport(List<MarketItemPriceStrategyVO> strategyVOS, MarketItem marketItem, List<MerchantStoreAddressVO> storeList) {
        Long marketItemId = marketItem.getId();
        Long tenantId = marketItem.getTenantId();

        List<MarketItemPriceLogDTO> result = new ArrayList<>();

        Map<Long, MerchantStoreAddressVO> storeAddressMap = storeList.stream().collect(Collectors.toMap(MerchantStoreAddressVO::getStoreId, Function.identity()));

        List<CostPriceVO> costPriceVOS;
        ProductAgentSkuMappingVO productAgentSkuMapping;
        if (!Objects.isNull(marketItem.getSkuId())) {
            productAgentSkuMapping = productFacade.getProductMappingBySkuIdAndTenantId(marketItem.getSkuId(), xmTenantId,tenantId);
            if (!Objects.isNull(productAgentSkuMapping)) {
                String sku = productAgentSkuMapping.getAgentSkuCode();
                costPriceVOS = costPriceService.listValidCostPriceBySkuCode(sku, tenantId);
            } else {
                costPriceVOS = Collections.emptyList();
            }
        } else {
            productAgentSkuMapping = null;
            costPriceVOS = Collections.emptyList();
        }

        strategyVOS.forEach(strategyVO -> strategyVO.getTargetIds().forEach(storeId -> {
            MerchantStoreAddressVO merchantStoreAddressVO = storeAddressMap.get(storeId);
            MarketItemPriceLogDTO dto = initMarketItemPriceLogDTO(marketItemId, tenantId, strategyVO, storeId, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode(), null);
            if (ObjectUtil.isEmpty(productAgentSkuMapping)) {
                dto.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode());
            }else {
                if(ObjectUtil.isNotNull (merchantStoreAddressVO)) {
                    BigDecimal basePrice = costPriceVOS.stream ()
                            .filter (e -> Objects.equals (e.getArea (), merchantStoreAddressVO.getArea ()) && Objects.equals (e.getCity (), merchantStoreAddressVO.getCity ())).findFirst ()
                            .orElse (new CostPriceVO ()).getPrice ();
                    if (ObjectUtil.isEmpty (basePrice)) {
                        dto.setOpsType (MarketItemPriceLogEnum.OptType.DELETE.getCode ());
                    } else {
                        dto.setBasePrice (basePrice);
                    }
                }else{
                    log.error ("更新自营成本价时未查询到该门店地址，marketItemId={},tenantid={},storeid={}",marketItemId,tenantId,storeId);
                    dto.setOpsType (MarketItemPriceLogEnum.OptType.DELETE.getCode ());
                }
            }
            result.add(dto);
        }));
        return result;
    }
}
