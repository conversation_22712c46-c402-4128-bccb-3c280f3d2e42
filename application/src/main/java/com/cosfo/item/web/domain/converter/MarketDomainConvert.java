package com.cosfo.item.web.domain.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.req.MarketItemPriceInput;
import com.cofso.item.client.resp.MarketItemWithClassificationResp;
import com.cofso.item.client.resp.PageMarketItemResp;
import com.cosfo.item.infrastructure.item.dto.MarketItemStoreQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketItemWithClassificationDTO;
import com.cosfo.item.infrastructure.item.dto.MarketPageQueryParam;
import com.cosfo.item.infrastructure.item.dto.PageMarketItemDTO;
import com.cosfo.item.infrastructure.item.model.Market;
import com.cosfo.item.infrastructure.item.model.MarketDetail;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceStrategyDTO;
import com.cosfo.item.web.domain.dto.*;
import com.cosfo.item.web.domain.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 16:09
 * @Description:
 */
@Mapper
public interface MarketDomainConvert {
    MarketDomainConvert INSTANCE = Mappers.getMapper(MarketDomainConvert.class);

    @Mapping(source = "marketId", target = "id")
    Market convert2Market(MarketInfoDTO dto);

    Page<MarketItemVO> convert2PageItems(Page<MarketItem> itemPage);

    List<MarketItemVO> convert2VOs(List<MarketItem> items);

    @Mapping(source = "id", target = "marketItemId")
    MarketItemVO convert2ItemVO(MarketItem entity);

    @Mapping(source = "marketItemId", target = "id")
    @Mapping(source = "itemTitle", target = "title")
    MarketItem convert2Entity(MarketItemDTO dto);

    MarketDetail convert2MarketDetail(MarketInfoDTO dto);

    MarketItemDetail convert2MarketItemDetail(MarketItemDTO dto);

    MarketPageQueryParam convert2MarketPageParam(MarketPageQueryDTO queryDTO);

    MarketPageInfoVO convert2MarketPage(Market market);

    MarketInfoVO convert2MarketVo(Market market);

    List<MarketInfoVO> convert2MarketVoList(List<Market> markets);

    MarketItemStoreQueryParam convert2ItemStoreParam(MarketItemPageQuery4StoreDTO dto);

    @Mapping(source = "id", target = "marketItemId")
    MarketItem4StoreVO convert2StoreItemVO(MarketItem item);

    MarketItemStoreQueryParam convert2Param(MarketItemQuery4StoreDTO dto);

    List<MarketItemSimpleInfoVO> convert2SimpleItemVOs(List<MarketItem> marketItems);

    @Mapping(source = "id", target = "marketItemId")
    MarketItemSimpleInfoVO convert2SimpleItemVO(MarketItem marketItem);

    @Mapping(source = "id", target = "itemId")
    MarketItemDetail4StoreVO convert2ItemDetailVO(MarketItem marketItem);

    List<MarketItemPriceStrategyDTO> convert2PriceStrategyDtos(List<MarketItemPriceInput> inputs);

    List<MarketItemWithClassificationResp> convert2RespList(List<MarketItemWithClassificationDTO> dtoList);

    List<PageMarketItemResp> convert2respList(List<PageMarketItemDTO> dtoList);

    @Mapping(source = "classificationId", target = "secondClassificationId")
    PageMarketItemResp convert2resp(PageMarketItemDTO dto);
}
