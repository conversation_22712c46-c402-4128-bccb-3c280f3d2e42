package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: monna.chen
 * @Date: 2023/5/13 15:19
 * @Description:
 */
public interface ProductEnum {
    @Getter
    @AllArgsConstructor
    public enum ProductSupplierSkuAssociateEnum {

        /**
         * 未关联
         */
        NOT_ASSOCIATE(0, "未关联"),

        /**
         * 已关联
         */
        HAS_ASSOCIATE(1, "已关联");

        /**
         * type
         */
        private Integer type;

        /**
         * desc
         */
        private String desc;
    }
}
