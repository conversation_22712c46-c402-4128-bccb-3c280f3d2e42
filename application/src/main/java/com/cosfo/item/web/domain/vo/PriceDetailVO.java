package com.cosfo.item.web.domain.vo;

import com.cosfo.item.common.dto.LadderPriceDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PriceDetailVO {
    /**
     * 销售单价
     */
    private BigDecimal price;
    /**
     * 成本单价 - 供应价
     */
    private BigDecimal costPrice;
    /**
     * 原销售单价
     */
    private BigDecimal marketItemPrice;
    /**
     * 阶梯价
     * eg：[{"price":197.14,"unit":1}]
     */
    private String ladderPrice;

    private List<LadderPriceDTO> ladderPriceDTOS;
}
