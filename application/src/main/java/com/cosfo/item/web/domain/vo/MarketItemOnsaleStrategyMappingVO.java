package com.cosfo.item.web.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemOnsaleStrategyMappingVO  implements Serializable {

    private static final long serialVersionUID = -7516859785609722693L;

    /**
     * 是否大客户专享
     */
    private Integer mType;

    /**
     * 是否展示
     */
    private Integer showFlag;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 上下架目标id
     */
    private Long targetId;

    /**
     * 0下架 1上架
     */
    private Integer onSale;

    /**
     * 上架目标类型 1按租户,2 按门店 3单店 4大客户
     */
    private Integer strategyType;
}
