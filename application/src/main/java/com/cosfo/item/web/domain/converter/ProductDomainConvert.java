package com.cosfo.item.web.domain.converter;

import com.cosfo.item.web.domain.vo.ProductAgentSkuMappingVO;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @author: monna.chen
 * @Date: 2023/9/13 18:59
 * @Description:
 */
@Mapper
public interface ProductDomainConvert {
    ProductDomainConvert INSTANCE = Mappers.getMapper(ProductDomainConvert.class);

    @Mapping(source = "sku",target = "agentSkuCode")
    ProductAgentSkuMappingVO convert2VO(ProductsMappingResp resp);
}
