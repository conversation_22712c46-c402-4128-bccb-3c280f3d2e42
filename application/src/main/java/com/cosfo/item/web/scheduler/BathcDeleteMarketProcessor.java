package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.common.domain.InstanceStatus;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.item.web.domain.dto.MarketDeleteDTO;
import com.cosfo.item.web.domain.service.MarketDomainService;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.task.process.XianMuJavaProcessor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 手动执行批量删除
 */
@Component
@Slf4j
public class BathcDeleteMarketProcessor extends XianMuJavaProcessor {

    @Autowired
    private MarketDomainService marketDomainService;
    @Override
    public ProcessResult processResult(JobContext context) throws Exception {
        log.info("开始进行BathcDeleteMarketProcessor任务,jobParameters ={},instanceParameters ={}",context.getJobParameters (),context.getInstanceParameters ());
        Map<String, String> map = Collections.emptyMap ();
        if (!StringUtils.isEmpty (context.getInstanceParameters ())) {
            map = JSON.parseObject (context.getInstanceParameters (), Map.class);
        }
        Long tenantId = null;
        Set<Long> marketIds = null;
        if (map.containsKey ("tenant")) {
            tenantId = Long.valueOf (map.get ("tenant"));
        }
        if (map.containsKey ("ids")) {
            marketIds = Splitter.on(",").splitToStream(map.get ("ids")).map(Long::valueOf).collect(Collectors.toSet ());
        }
        log.info ("开始进行BathcDeleteMarketProcessor任务 , marketIds={}",JSON.toJSONString (marketIds));
        List<String> failedIds = new LinkedList<>();
        if(CollectionUtil.isNotEmpty (marketIds)){
            for (Long marketId: marketIds) {
                MarketDeleteDTO dto = new MarketDeleteDTO();
                dto.setTenantId (tenantId);
                dto.setId (marketId);
                log.info ("开始 删除 market={}",JSON.toJSONString (dto));
                try{
                    marketDomainService.deleteMarket (dto);
                }catch (BizException e){
                    log.warn ("删除商品失败，失败原因={},marketId={}",e.getMessage (),marketId);
                }catch (Exception e){
                    failedIds.add (String.valueOf(marketId));
                    log.error ("删除商品失败,marketId={}",marketId,e);
                }
            }
        }

        if (CollectionUtil.isEmpty (failedIds)) {
            return new ProcessResult(true);
        } else {
            return new ProcessResult(InstanceStatus.PARTIAL_FAILED, String.join(",", failedIds));
        }
    }
}