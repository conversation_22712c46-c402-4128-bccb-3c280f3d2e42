package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.lang.Pair;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.MarketItemOnsalePriceDealService;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class CosfoMarketCombineItemMappingHandler implements DbTableDmlStrategy {

    @Autowired
    private MarketItemDao marketItemDao;


    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.MARKET_COMBINE_ITEM_MAPPING;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        Set<Long> result = new HashSet<> ();

        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                Long tenantId = Long.valueOf (map.get ("tenant_id"));
                Long itemId = Long.valueOf (map.get ("item_id"));
                result.add (itemId);
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                Long itemId = Long.valueOf (dataMap.get ("item_id"));
                result.add (itemId);
            }
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.DELETE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue();
                Long itemId = Long.valueOf (oldMap.get ("item_id"));
                result.add (itemId);
            }
        }
        List<MarketItem> marketItems = marketItemDao.listByIds (result);
        marketItems.forEach(item -> {
            ItemChangeMessageDTO dto = ItemConverter.marketItem2MsgDTO (item);
            log.info("触发更新组合品价格，tenantId:{}, combineItemId:{}", item.getTenantId (), item.getId ());
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        });
    }
}
