package com.cosfo.item.provider;

import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.provider.MarketClassificationProvider;
import com.cofso.item.client.resp.MarketItemClassificationResp;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Map;

/**
 *
 * @author: xiaowk
 * @time: 2023/5/16 下午4:59
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MarketClassificationProviderTest {

    @DubboReference
    private MarketClassificationProvider marketClassificationProvider;


    @Test
    public void testQuery(){
        DubboResponse<Map<Long, MarketItemClassificationResp>> resp = marketClassificationProvider.queryByMarketIds(2L, Arrays.asList(2001082L));
        System.err.println(JSONObject.toJSONString(resp));
    }
}
