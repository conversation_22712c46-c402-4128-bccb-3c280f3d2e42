package com.cosfo.item.web.mq.consumer.binlog.input;

import cn.hutool.core.lang.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
public class DtsModelHandler {

    /**
     * 获取DtsModel对齐的数据
     * @return pair集合（pair左边是data，右边是old）
     */
    public static List<Pair<Map<String, String>, Map<String, String>>> getAlignedData(DtsModelEvent dtsModelEvent){
        List<Pair<Map<String, String>, Map<String, String>>> pairList = new ArrayList<>();
        int size = dtsModelEvent.getData() == null ? 0 : dtsModelEvent.getData().size();
        int oldSize = dtsModelEvent.getOld() == null ? 0 : dtsModelEvent.getOld().size();
        if (size != oldSize){
            return pairList;
        }
        for (int i = 0; i < size; i++) {
            pairList.add(new Pair<>(dtsModelEvent.getData().get(i), dtsModelEvent.getOld().get(i)));
        }
        return pairList;
    }

    /**
     * 只获取老数据(此方法仅供删除事件使用)
     *
     * @param dtsModelEvent dts模型事件
     * @return {@link List}<{@link Pair}<{@link Map}<{@link String}, {@link String}>, {@link Map}<{@link String}, {@link String}>>>
     */
    public static List<Pair<Map<String, String>, Map<String, String>>> getOnlyOldData(DtsModelEvent dtsModelEvent){
        List<Pair<Map<String, String>, Map<String, String>>> pairList = new ArrayList<>();
        int oldSize = dtsModelEvent.getOld() == null ? 0 : dtsModelEvent.getOld().size();
        for (int i = 0; i < oldSize; i++) {
            pairList.add(new Pair<>(null, dtsModelEvent.getOld().get(i)));
        }
        return pairList;
    }
}
