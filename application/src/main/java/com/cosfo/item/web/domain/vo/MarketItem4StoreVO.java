package com.cosfo.item.web.domain.vo;

import com.cofso.item.client.req.LadderPrice;
import com.cosfo.item.common.dto.LadderPriceDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/11 18:08
 * @Description:
 */
@Data
public class MarketItem4StoreVO {
    /**
     * market_id
     */
    private Long marketItemId;
    /**
     * market_id
     */
    private Long marketId;
    /**
     * tenant_id
     */
    private Long tenantId;
    /**
     * 商品标题
     */
    private String title;
    /**
     * 货源类型
     *
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;
    /**
     * item类型
     *
     * @see com.cofso.item.client.enums.ItemTypeEnum
     */
    private Integer itemType;
    /**
     * 库存
     */
    private Integer stockAmount;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * sku_id
     */
    private Long skuId;
    /**
     * 品牌
     */
    private Long brandId;
    /**
     * 规格
     */
    private String specification;
    private String specificationUnit;

    /**
     * 子商品列表
     * 只有当itemType = 组合品时 才会有
     */
    private List<MarketCombineVO> combineItemList;

    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 最后编辑人
     */
    private Long editUserId;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    private List<LadderPriceDTO> ladderPrices;
}
