package com.cosfo.item.web.provider.converter;

import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.*;
import com.cofso.item.client.resp.BatchChangNoGoodsSupplyPriceVO;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnSaleSimpleDTO;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import com.cosfo.item.infrastructure.item.dto.MarketItemParam;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceStrategyDTO;
import com.cosfo.item.web.domain.dto.*;
import com.cosfo.item.web.domain.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

import static com.cosfo.item.common.constants.NumberConstants.PAGE_NUM;
import static com.cosfo.item.common.constants.NumberConstants.PAGE_SIZE;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 14:45
 * @Description:
 */
@Mapper
public interface MarketConvert {
    MarketConvert INSTANCE = Mappers.getMapper(MarketConvert.class);

    @Mapping(source = "pageNum", target = "pageNum", defaultValue = PAGE_NUM)
    @Mapping(source = "pageSize", target = "pageSize", defaultValue = PAGE_SIZE)
    @Mapping(source = "marketItemInfoQueryFlagReq", target = "marketItemInfoQueryFlagDTO")
    MarketItemCommonQueryDTO convert2QueryDto(MarketItemCommonQueryReq req);

    List<MarketItemInfoResp> convert2Items(List<MarketItemVO> vos);
    List<MarketItemPriceStrategyUpdateResultResp> marketItemPriceStrategyUpdateResultVO2respList(List<MarketItemPriceStrategyUpdateResultVO> vos);

    @Mapping(target = "itemId", source = "id")
    @Mapping(target = "itemTitle", source = "title")
    @Mapping(target = "unfairPriceStrategyResp", source = "unfairPriceStrategyVO")
    @Mapping(target = "marketItemClassificationResp", source = "itemClassificationDTO")
    MarketItemInfoResp convert2Item(MarketItemVO vo);

    @Mapping(source = "MType",target = "mType")
    MarketItemOnsaleStrategyMappingResp convert2OnSaleResp(MarketItemOnsaleStrategyMappingVO onSaleVO);

    List<MarketItemOnsaleStrategyMappingResp> convert2OnSaleRespList(List<MarketItemOnsaleStrategyMappingVO> onSaleVO);

    MarketItemParam convert2Param(MarketItemCommonQueryDTO queryDTO);

    @Mapping(source = "marketItemInputReq", target = "marketItem")
    MarketInfoDTO convert2MarketDTO(MarketInputReq req);

    @Mapping(source = "onSaleInputList", target = "onSaleList")
    @Mapping(source = "priceInputs", target = "priceList")
    @Mapping(source = "marketItemUnfairPriceStrategyReq", target = "marketItemUnfairPriceStrategyDTO")
    MarketItemDTO convert2MarketItemDTO(MarketItemInputReq req);

    AddMarketResp convert2Resp(AddMarketVO vo);
    BatchChangNoGoodsSupplyPriceResp convert2Resp(BatchChangNoGoodsSupplyPriceVO vo);

    @Mapping(source = "pageNum", target = "pageNum", defaultValue = PAGE_NUM)
    @Mapping(source = "pageSize", target = "pageSize", defaultValue = PAGE_SIZE)
    @Mapping(source = "marketItemInfoQueryFlagReq", target = "marketItemInfoQueryFlagDTO")
    MarketPageQueryDTO convert2QueryDto(MarketQueryReq queryReq);

    List<MarketResp> covert2MarketResps(List<MarketPageInfoVO> pageInfoVOs);

    MarketDetailResp convert2MarketDetail(MarketInfoVO marketInfoVO);

//    MarketDetailQueryDTO conert2QueryDto(MarketDetailQueryReq req);

    MarketItemPageQuery4StoreDTO convert2QueryDto(MarketItemPageQuery4StoreReq req);

    List<MarketItem4StoreResp> convert2MallRespList(List<MarketItem4StoreVO> vos);

    @Mapping(source = "combineItemList",target = "combineItemRespList")
    MarketItem4StoreResp convert2MallResp(MarketItem4StoreVO vo);

    MarketItemQuery4StoreDTO convert2QueryDTO(MarketItemQuery4StoreReq req);

    List<MarketItemSimpleInfoResp> convert2SimpleInfoResp(List<MarketItemSimpleInfoVO> vos);

    MarketItemDetailQueryDTO convert2ItemDetail4Store(MarketItemDetailQueryReq req);

    MarketItemDetail4StoreResp convert2ItemDetailResp4Store(MarketItemDetail4StoreVO vo);

    MarketDeleteDTO convert2DeleteDto(MarketDeleteReq req);

    MarketItemOnSaleInputDTO convert2Dto(MarketItemOnSaleInputReq req);

    List<MarketItemOnSaleSimple4StoreResp> convert2OnsaleSimpleResp(List<MarketItemOnSaleSimpleDTO> marketItemOnSaleSimple4StoreRespList);

    BatchOnSaleResultResp convert2Resp(BatchOnSaleResultVO vo);
    MarketItemOnsaleStrategyDTO convert2OnsaleStrategyDTO(MarketItemOnsaleInput onSaleInput);
    MarketItemPriceStrategyDTO convert2PriceStrategyDTO(MarketItemPriceInput priceInput);

    MarketInfoResp convert2MarketInfo(MarketInfoVO marketInfo);

    List<MarketInfoResp> convert2MarketInfoList(List<MarketInfoVO> marketInfos);


    MarketItemDetail4XmQueryDTO convert2Dto(MarketItemDetail4XmQueryReq req);

}