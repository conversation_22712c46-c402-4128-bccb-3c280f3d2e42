package com.cosfo.item.infrastructure.skuPreferential.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * @Author: fansongsong
 * @Date: 2024-02-22
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ProductSkuPreferentialQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商城Id
     */
    private Long tenantId;

    /**
     * 货品Ids
     */
    private List<Long> skuIds;

    /**
     * Ids
     */
    private Set<Long> ids;

    /**
     * 是否可用 ture = 可用 ， false = 不可用
     */
    private Boolean availableFlag;

    /**
     * 待生效状态
     */
    private Boolean waitValid;

    /**
     * 分页
     */
    private Integer pageIndex;

    private Integer pageSize;
}
