package com.cosfo.item.web.facade.converter;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.web.domain.vo.TenantVO;
import com.cosfo.manage.client.tenant.resp.TenantResp;

public class TenantConverter {
    public static TenantVO tenantResp2VO(TenantResp resp) {
        if(ObjectUtil.isNull(resp)){
            return null;
        }
        TenantVO tenantVO = new TenantVO();
        tenantVO.setId(resp.getId());
        tenantVO.setPhone(resp.getPhone());
        tenantVO.setTenantName(resp.getTenantName());
        tenantVO.setAdminId(resp.getAdminId());
        return tenantVO;
    }
}
