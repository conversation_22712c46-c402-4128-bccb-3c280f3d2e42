package com.cosfo.item.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.common.enums.*;
import com.cosfo.item.infrastructure.item.dao.MarketCombineItemMappingDao;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceDao;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceLogDao;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceLogDTO;
import com.cosfo.item.infrastructure.price.model.MarketItemPrice;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceLog;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.converter.PriceConvert;
import com.cosfo.item.web.domain.service.itempricefactory.MarketItemPriceBuilderFactory;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyVO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.UnfairPriceStrategyVO;
import com.cosfo.item.web.facade.MerchantStoreFacade;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.rocketmq.support.producer.SendResultDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * 描述: 所有的变更都先发送到顺序消息队列，以防止并发更新； </b> 更新 商品售价及 上下架服务类 </b>消息自产自销
 */
@Slf4j
@Component
@MqOrderlyListener(topic = RocketMqConstant.Topic.ITEM_PRICE,
    consumerGroup = RocketMqConstant.ConsumeGroup.ITEM_PRICE,
    tag = RocketMqConstant.Tag.ITEM_PRICE,
    consumeThreadMin = 1,consumeThreadMax = 1
)
public class MarketItemOnsalePriceDealService extends AbstractMqListener<ItemChangeMessageDTO> {

    @Autowired
    private MarketItemPriceDao priceDao;

    @Autowired
    private MarketCombineItemMappingDao marketCombineItemMappingDao;

    @Autowired
    private MarketItemDao marketItemDao;

    @Autowired
    private MarketItemPriceLogDao priceLogDao;

    @Autowired
    private MerchantStoreFacade storeFacade;

    @Autowired
    private MarketItemPriceStrategyDomainService priceStrategyDomainService;

    @Autowired
    private MarketItemPriceBuilderFactory marketItemPriceBuilderFactory;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Autowired
    private MqProducer mqProducer;

    @Autowired
    private MarketItemOnsalePriceDealService dealService;

    @Autowired
    private UnfairPriceStrategyDomainService unfairPriceStrategyDomainService;

    public boolean sendToOrderedQueue(ItemChangeMessageDTO dto) {
        log.info("发送marketItemId价格变更顺序消息:{}", dto);
        String messageKey = ItemChangeMessageDTO.getOrderedMessageKey(dto);
        SendResultDTO resultDTO = mqProducer.sendOrderly(RocketMqConstant.Topic.ITEM_PRICE, RocketMqConstant.Tag.ITEM_PRICE, JSON.toJSONString(dto), messageKey, dto.getMarketItemId());
        log.info("发送消息结果：{}", resultDTO);
        return null != resultDTO && StringUtils.isNotBlank(resultDTO.getMsgId());
    }

    /**
     * 批量更新/新增价格
     */
    protected void saveOrUpdateBatchMarketItemPrice(ItemChangeMessageDTO dto) {
        Long tenantId = dto.getTenantId();
        Long marketItemId = dto.getMarketItemId();
        Long storeId = dto.getStoreId();
        Set<Long> storeIds = dto.getStoreIds();
        Integer bodyVersion = dto.getBodyVersion ();
        log.info("批量更新/新增价格,线程:{}, ItemChangeMessageDTO:{}", Thread.currentThread().getName(), dto);
        try {
            MarketItem marketItem;
            if (!ObjectUtil.isNull(bodyVersion)  && bodyVersion  == 1) {
                marketItem = ItemConverter.itemChangeMessageDTO2marketItem(dto);
            }else{
                marketItem = marketItemDao.getById(marketItemId);
            }
            if (ObjectUtil.isNull(marketItem)){
                return;
            }

            List<MerchantStoreAddressVO> merchantStoreAddressVOS;
            if(!xmTenantId.equals (tenantId)) {
                if (storeId != null && storeId.longValue () > 0L) {
                    // 单个店铺更新，用于新增店铺或者店铺修改了区域；
                    storeIds = Sets.newHashSet (storeId);
                    MerchantStoreAddressVO storeAddressVO = storeFacade.queryStoreAddress (tenantId, storeId);
                    if (null == storeAddressVO) {
                        log.error ("不合法的店铺地址:{}", dto, new Exception ());
                        return;
                    }
                    merchantStoreAddressVOS = Lists.newArrayList (storeAddressVO);
                } else if (CollectionUtils.isEmpty (storeIds)) {
                    // 如果storeId和storeIds都是空，则全量更新所有店铺下的该market-item-ID的价格：
                    merchantStoreAddressVOS = storeFacade.batchQueryStoreAddressFromCache (tenantId);
                    if (!Objects.equals (tenantId, xmTenantId)) {
                        storeIds = merchantStoreAddressVOS.stream ().map (MerchantStoreAddressVO::getStoreId).collect (Collectors.toSet ());
                    } else {
                        storeIds = Collections.emptySet ();
                    }
                } else {
                    //更新消息里面的storeIds的价格；
                    merchantStoreAddressVOS = storeFacade.batchQueryStoreAddressByIds (tenantId, Lists.newArrayList (storeIds));
                }
                UnfairPriceStrategyVO unfairPriceStrategyVO = unfairPriceStrategyDomainService.getStrategyValueByItemIdAndTargetType (tenantId, marketItemId, MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode ());
                saveOrUpdateBatchMarketItemPrice4Saas(marketItem, merchantStoreAddressVOS, storeIds, unfairPriceStrategyVO);
            }else{
                //鲜沐价格拆除
                saveOrUpdateBatchMarketItemPrice4Summerfarm(marketItem);
            }
        } catch (Exception e) {
            log.error("保存价格错误，tenantId={} , marketItemId={} ", tenantId, marketItemId, e);
        }
    }

    private void saveOrUpdateBatchMarketItemPrice4Summerfarm(MarketItem marketItem) {
        Long marketItemId = marketItem.getId(), tenantId = marketItem.getTenantId();
        List<MarketItemPriceStrategyVO> marketItemPriceStrategyVOS = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds(tenantId,
                Collections.singletonList(marketItemId));

        List<MarketItemPriceStrategyVO> areanoStrategys = marketItemPriceStrategyVOS.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.AREA_NO.getCode())).collect(Collectors.toList());
        Map<Integer, List<MarketItemPriceStrategyVO>> areanoStrategyTypeMap = areanoStrategys.stream()
                .collect(Collectors.groupingBy(MarketItemPriceStrategyVO::getStrategyType));
        areanoStrategyTypeMap.forEach((strategyType, marketItemPriceStrategyList) -> {
            List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = marketItemPriceBuilderFactory.getBuilder(MarketItemPriceStrategyEnum.StrategyTypeEnum.of(strategyType))
                    .orElseThrow(() -> new BizException("不支持的售价价格策略类型"))
                    .buildMarketItemPriceLogDTOS(marketItemPriceStrategyList, marketItem,null,null);
            saveOrUpdateMarketItemPriceAndOnsale4AreaNo(tenantId, marketItemId, marketItemPriceLogDTOS);
        });
    }

    private void saveOrUpdateBatchMarketItemPrice4Saas(MarketItem marketItem, List<MerchantStoreAddressVO> merchantStoreAddressVOS, Set<Long> storeIds,UnfairPriceStrategyVO unfairPriceStrategyVO) {
        String copyStoreIds = JSON.toJSONString (storeIds);
        Long marketItemId = marketItem.getId(), tenantId = marketItem.getTenantId();
        List<MarketItemPriceStrategyVO> marketItemPriceStrategyVOS = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds(tenantId,
            Collections.singletonList(marketItemId));

        if (Objects.equals(MarketItemEnum.GoodsType.QUOTATION.getCode(), marketItem.getGoodsType())
            || Objects.equals(MarketItemEnum.GoodsType.COMBINE.getCode(), marketItem.getGoodsType())
            || Objects.equals(MarketItemEnum.GoodsType.SELF_SUPPORT.getCode(), marketItem.getGoodsType())) {
            List<MarketItemPriceStrategyVO> storeStrategys = marketItemPriceStrategyVOS.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.STORE.getCode())).collect(Collectors.toList());
            Map<Integer, List<MarketItemPriceStrategyVO>> storeStrategyTypeMap = storeStrategys.stream().collect(Collectors.groupingBy(MarketItemPriceStrategyVO::getStrategyType));
            storeStrategyTypeMap.forEach((strategyType, marketItemPriceStrategyList) -> {
                List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = marketItemPriceBuilderFactory.getBuilder(MarketItemPriceStrategyEnum.StrategyTypeEnum.of(strategyType))
                    .orElseThrow(() -> new BizException("不支持的售价价格策略类型"))
                    .buildMarketItemPriceLogDTOS(marketItemPriceStrategyList, marketItem, merchantStoreAddressVOS, unfairPriceStrategyVO);
                if (CollectionUtil.isNotEmpty(marketItemPriceLogDTOS)) {
                    saveOrUpdateMarketItemPrice4Store(tenantId, marketItemId, marketItemPriceLogDTOS, storeIds);
                } else {
                    removeBatchMarketItemPrice(tenantId, marketItemId, storeIds, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode());
                }
            });

            List<MarketItemPriceStrategyVO> storeGroupStrategys = marketItemPriceStrategyVOS.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.STORE_GROUP.getCode())).collect(Collectors.toList());

            ArrayList<Long> groupIds = new ArrayList<> ();
            storeGroupStrategys.forEach (e->groupIds.addAll (e.getTargetIds ()));
            Map<Long, List<Long>> groupStoreMap = storeFacade.getGroupByStoreGroupIds (tenantId, groupIds);


            Map<Integer, List<MarketItemPriceStrategyVO>> storeGroupStrategyTypeMap = storeGroupStrategys.stream().collect(Collectors.groupingBy(MarketItemPriceStrategyVO::getStrategyType));
            storeGroupStrategyTypeMap.forEach((strategyType, marketItemPriceStrategyList) -> {
                marketItemPriceStrategyList.forEach (strategy->{
                    ArrayList<Long> storeIdList = new ArrayList<> ();
                    strategy.getTargetIds ().forEach (group-> {
                        if(CollectionUtil.isNotEmpty (groupStoreMap.get (group))){
                            storeIdList.addAll (groupStoreMap.get (group));
                        }
                    });
                    strategy.setTargetIds(storeIdList);
                });

                List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = marketItemPriceBuilderFactory.getBuilder(MarketItemPriceStrategyEnum.StrategyTypeEnum.of(strategyType))
                    .orElseThrow(() -> new BizException("不支持的售价价格策略类型"))
                    .buildMarketItemPriceLogDTOS(marketItemPriceStrategyList, marketItem, merchantStoreAddressVOS, unfairPriceStrategyVO);
                if (CollectionUtil.isNotEmpty(marketItemPriceLogDTOS)) {
                    saveOrUpdateMarketItemPrice4Store(tenantId, marketItemId, marketItemPriceLogDTOS, storeIds);
                } else {
                    removeBatchMarketItemPrice(tenantId, marketItemId, storeIds, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode());
                }
            });


            MarketItemPriceStrategyVO tenantStrategy = marketItemPriceStrategyVOS.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.TENANT.getCode())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(tenantStrategy)) {
                tenantStrategy.setTargetIds(new ArrayList<>(storeIds));
                List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = marketItemPriceBuilderFactory.getBuilder(
                        MarketItemPriceStrategyEnum.StrategyTypeEnum.of(tenantStrategy.getStrategyType())).orElseThrow(() -> new BizException("不支持的售价价格策略类型"))
                    .buildMarketItemPriceLogDTOS(Collections.singletonList(tenantStrategy), marketItem, merchantStoreAddressVOS, unfairPriceStrategyVO);
                if (CollectionUtil.isNotEmpty(marketItemPriceLogDTOS)) {
                    saveOrUpdateMarketItemPrice4Store(tenantId, marketItemId, marketItemPriceLogDTOS, storeIds);
                } else {
                    removeBatchMarketItemPrice(tenantId, marketItemId, storeIds, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode());
                }
            } else {
                removeBatchMarketItemPrice(tenantId, marketItemId, storeIds, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode());
            }
            //铺平到门店， 不存在品牌方纬度的价格。
            removeBatchMarketItemPrice(tenantId, marketItemId, Collections.singleton (tenantId), MarketItemPriceEnum.TargetTypeEnum.TENANT.getCode());
        } else {
            List<MarketItemPriceStrategyVO> storePriceStrategyVOS = marketItemPriceStrategyVOS.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.STORE.getCode())).collect(Collectors.toList());
            Map<Integer, List<MarketItemPriceStrategyVO>> strategyTypeMap = storePriceStrategyVOS.stream()
                .collect(Collectors.groupingBy(MarketItemPriceStrategyVO::getStrategyType));
            strategyTypeMap.forEach((strategyType, marketItemPriceStrategyList) -> {
                List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = marketItemPriceBuilderFactory.getBuilder(MarketItemPriceStrategyEnum.StrategyTypeEnum.of(strategyType))
                    .orElseThrow(() -> new BizException("不支持的售价价格策略类型"))
                    .buildMarketItemPriceLogDTOS(marketItemPriceStrategyList, marketItem, merchantStoreAddressVOS, unfairPriceStrategyVO);
                saveOrUpdateMarketItemPrice4Store(tenantId, marketItemId, marketItemPriceLogDTOS, storeIds);
            });

            List<MarketItemPriceStrategyVO> storeGroupStrategys = marketItemPriceStrategyVOS.stream()
                    .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.STORE_GROUP.getCode())).collect(Collectors.toList());

            ArrayList<Long> groupIds = new ArrayList<> ();
            storeGroupStrategys.forEach (e->groupIds.addAll (e.getTargetIds ()));
            Map<Long, List<Long>> groupStoreMap = storeFacade.getGroupByStoreGroupIds (tenantId, groupIds);


            Map<Integer, List<MarketItemPriceStrategyVO>> storeGroupStrategyTypeMap = storeGroupStrategys.stream().collect(Collectors.groupingBy(MarketItemPriceStrategyVO::getStrategyType));
            storeGroupStrategyTypeMap.forEach((strategyType, marketItemPriceStrategyList) -> {
                marketItemPriceStrategyList.forEach (strategy->{
                    ArrayList<Long> storeIdList = new ArrayList<> ();
                    strategy.getTargetIds ().forEach (group-> {
                        if(CollectionUtil.isNotEmpty (groupStoreMap.get (group))){
                            storeIdList.addAll (groupStoreMap.get (group));
                        }
                    });
                    strategy.setTargetType (MarketItemPriceEnum.TargetTypeEnum.STORE.getCode());
                    strategy.setTargetIds(storeIdList);
                });

                List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = marketItemPriceBuilderFactory.getBuilder(MarketItemPriceStrategyEnum.StrategyTypeEnum.of(strategyType))
                        .orElseThrow(() -> new BizException("不支持的售价价格策略类型"))
                        .buildMarketItemPriceLogDTOS(marketItemPriceStrategyList, marketItem, merchantStoreAddressVOS, unfairPriceStrategyVO);
                if (CollectionUtil.isNotEmpty(marketItemPriceLogDTOS)) {
                    saveOrUpdateMarketItemPrice4Store(tenantId, marketItemId, marketItemPriceLogDTOS, storeIds);
                } else {
                    removeBatchMarketItemPrice(tenantId, marketItemId, storeIds, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode());
                }
            });

            MarketItemPriceStrategyVO tenantStrategy = marketItemPriceStrategyVOS.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.TENANT.getCode())).findFirst().orElse(null);
            if (ObjectUtil.isNotNull(tenantStrategy)) {
                List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = marketItemPriceBuilderFactory.getBuilder(
                        MarketItemPriceStrategyEnum.StrategyTypeEnum.of(tenantStrategy.getStrategyType())).orElseThrow(() -> new BizException("不支持的售价价格策略类型"))
                    .buildMarketItemPriceLogDTOS(Collections.singletonList(tenantStrategy), marketItem, merchantStoreAddressVOS, unfairPriceStrategyVO);
                saveOrUpdateMarketItemPrice4Tenant(tenantId, marketItemId, marketItemPriceLogDTOS.stream().findFirst().orElse(null), storeIds);
            } else {
                removeBatchMarketItemPrice(tenantId, marketItemId, Sets.newHashSet(tenantId), MarketItemPriceEnum.TargetTypeEnum.TENANT.getCode());
            }
            if (CollectionUtil.isNotEmpty(storeIds)) {
                removeBatchMarketItemPrice(tenantId, marketItemId, storeIds, MarketItemPriceEnum.TargetTypeEnum.STORE.getCode());
            }
        }
        //如果是组合品的子item，需要重新计算组合品整体的价格
        Set<Long> itemIds = marketCombineItemMappingDao.listItemIdsByCombineItemId(tenantId, marketItemId);
        if (CollectionUtil.isNotEmpty(itemIds)) {
            List<MarketItem> marketItems = marketItemDao.listByIds (itemIds);
            marketItems.forEach(item -> {
                ItemChangeMessageDTO dto = ItemConverter.marketItem2MsgDTO (item);
                log.info("触发更新组合品价格，tenantId:{}, combineItemId:{}", tenantId, item.getId ());
                sendToOrderedQueue (dto);
            });
        }

        ItemChangeMessageDTO dto = new ItemChangeMessageDTO();
        dto.setMarketItemId(marketItemId);
        dto.setTenantId(tenantId);
        dto.setStoreIds(new HashSet<> (JSON.parseArray (copyStoreIds,Long.class)));
        dto.setOnSale(marketItem.getOnSale());
        log.info("发送marketItemId上下架变更顺序消息:{}", dto);
        String messageKey = ItemChangeMessageDTO.getOrderedMessageKey(dto);
        mqProducer.sendOrderly(RocketMqConstant.Topic.ITEM_ONSALE, RocketMqConstant.Tag.ITEM_ONSALE, JSON.toJSONString(dto), messageKey, marketItemId);
    }

    private void saveOrUpdateMarketItemPriceAndOnsale4AreaNo(Long tenantId, Long marketItemId, List<MarketItemPriceLogDTO> marketItemPriceLogDTOS) {
        if (CollectionUtil.isEmpty(marketItemPriceLogDTOS)) {
            return;
        }
        dealService.saveOrUpdateMarketItemPrice(tenantId, marketItemId, marketItemPriceLogDTOS);
    }


    private void saveOrUpdateMarketItemPrice4Tenant(Long tenantId, Long marketItemId, MarketItemPriceLogDTO marketItemPriceLogDTO, Set<Long> storeIds) {
        if (!ObjectUtil.isEmpty(marketItemPriceLogDTO)) {
            dealService.saveOrUpdateMarketItemPrice(tenantId, marketItemId, Collections.singletonList(marketItemPriceLogDTO));
        }
    }

    private void saveOrUpdateMarketItemPrice4Store(Long tenantId, Long marketItemId, List<MarketItemPriceLogDTO> marketItemPriceLogDTOS, Set<Long> storeIds) {
        if (CollectionUtil.isEmpty(marketItemPriceLogDTOS)) {
            return;
        }
        List<Long> dealPriceStoreIds = new ArrayList<>();
        List<MarketItemPriceLogDTO> dtos = new ArrayList<>();
        if (!CollectionUtil.isEmpty(marketItemPriceLogDTOS)) {
            marketItemPriceLogDTOS.forEach(dto -> {
                if (storeIds.contains(dto.getTargetId())) {
                    dtos.add(dto);
                    dealPriceStoreIds.add(dto.getTargetId());
                }
                if (!Objects.equals(dto.getOpsType(), MarketItemPriceLogEnum.OptType.DELETE.getCode())) {
                    storeIds.remove(dto.getTargetId());
                }
            });
        }
        dealService.saveOrUpdateMarketItemPrice(tenantId, marketItemId, dtos);
        storeIds.removeAll(dealPriceStoreIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateMarketItemPrice(Long tenantId, Long marketItemId, List<MarketItemPriceLogDTO> dtos) {
        List<Long> removeIds = new ArrayList<>();
        List<MarketItemPriceLog> marketItemPriceLogs = new ArrayList<>();
        List<MarketItemPrice> marketItemPrices = new ArrayList<>();
        List<MarketItemPrice> marketItemPriceList = priceDao.listByItemIds(tenantId, Collections.singletonList(marketItemId));
        dtos.forEach(dto -> {
            Optional<MarketItemPrice> one = marketItemPriceList.stream()
                .filter(e -> Objects.equals(dto.getTargetId(), e.getTargetId()) && Objects.equals(dto.getTargetType(), e.getTargetType())).findFirst();
            if (one.isPresent()) {
                MarketItemPrice marketItemPrice = one.get();
                if (Objects.equals(dto.getOpsType(), MarketItemPriceLogEnum.OptType.DELETE.getCode())) {
                    removeIds.add(marketItemPrice.getId());
                    MarketItemPriceLog marketItemPriceLog = PriceConvert.marketItemPriceLogDTO2PriceLogEntity(dto);
                    marketItemPriceLogs.add(marketItemPriceLog);
                } else if (!(Objects.equals(marketItemPrice.getPrice(), dto.getPrice()) && Objects.equals(marketItemPrice.getBasePrice(), dto.getBasePrice()) && Objects.equals(
                    marketItemPrice.getPriceStrategy(), dto.getPriceStrategy()))) {
                    marketItemPrices.add(PriceConvert.marketItemPriceLogDTO2PriceEntity(dto));
                    MarketItemPriceLog logEmtity = PriceConvert.marketItemPriceLogDTO2PriceLogEntity(dto);
                    logEmtity.setOpsType(MarketItemPriceLogEnum.OptType.UPDATE.getCode());
                    marketItemPriceLogs.add(logEmtity);
                }
            } else if (!Objects.equals(dto.getOpsType(), MarketItemPriceLogEnum.OptType.DELETE.getCode())) {
                marketItemPrices.add(PriceConvert.marketItemPriceLogDTO2PriceEntity(dto));
                marketItemPriceLogs.add(PriceConvert.marketItemPriceLogDTO2PriceLogEntity(dto));
            }
        });
        if (CollectionUtil.isNotEmpty(removeIds)) {
            priceDao.removeByIds(removeIds);
        }
        if (CollectionUtil.isNotEmpty(marketItemPriceLogs)) {
            priceLogDao.batchInsert(marketItemPriceLogs);
        }
        if (CollectionUtil.isNotEmpty(marketItemPrices)) {
            priceDao.saveOrUpdateByItemIdAndTargetIdAndTargetType(marketItemPrices);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void removeBatchMarketItemPrice(Long tenantId, Long marketItemId, Set<Long> targetIds, Integer targetType) {
        if (CollectionUtil.isEmpty(targetIds)) {
            return;
        }
        List<MarketItemPrice> marketItemPrices = priceDao.ListByItemIdAndTargetIdsAndTargetType(tenantId, marketItemId, targetIds, targetType);
        if (CollectionUtil.isNotEmpty(marketItemPrices)) {
            priceDao.removeByIds(marketItemPrices.stream().map(MarketItemPrice::getId).collect(Collectors.toList()));
            priceLogDao.batchInsert(marketItemPrices.stream().map(PriceConvert::marketItemPrice2PriceLogEntity4Del).collect(Collectors.toList()));
        }
    }

    @Override
    public void process(ItemChangeMessageDTO dto) {
        log.info("收到market item价格价格变更消息，内容：{}", JSONObject.toJSONString(dto));
        saveOrUpdateBatchMarketItemPrice(dto);
    }
}
