package com.cosfo.item.infrastructure.item.mapper;

import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品上下架表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Mapper
public interface MarketItemOnsaleStrategyMappingMapper extends BaseMapper<MarketItemOnsaleStrategyMapping> {

    void saveOrUpdateByItemIdAndTargetIdAndStrategyType(@Param("list") List<MarketItemOnsaleStrategyMapping> list);
}
