<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.skuPreferential.mapper.ProductSkuPreferentialCostPriceMappingMapper">

    <insert id="batchSave">
        INSERT INTO product_sku_preferential_cost_price_mapping
        (tenant_id, sku_preferential_cost_price_id, city_id, city_name, sku_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantId}, #{item.skuPreferentialCostPriceId}, #{item.cityId}, #{item.cityName},
            #{item.skuId}, NOW())
        </foreach>
    </insert>
</mapper>
