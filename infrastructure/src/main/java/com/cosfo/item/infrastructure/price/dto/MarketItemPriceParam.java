package com.cosfo.item.infrastructure.price.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 15:56
 * @Description:  market_item_price 通用查询参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemPriceParam {
    /**
     * market_item_id
     */
    private List<Long> marketItemIds;
}
