package com.cosfo.item.infrastructure.price.dao;

import com.cosfo.item.infrastructure.price.dto.CostPriceQueryDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingSupplyDTO;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 成本价格表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
public interface CostPriceDao extends IService<CostPrice> {
    /**
     * 查询有效期内sku对应的城市报价单
     *
     * @param itemId
     * @param cityIds
     * @param tenantId
     * @return
     */
    List<ProductPricingSupplyDTO> selectProductPricingSupplyDTOBySkuId(Long itemId, Set<Long> cityIds, Long tenantId);
    ProductPricingMessageDTO selectSkuIdsByProductPricingSupplyId(Long productPricingSupplyId);
    List<ProductPricingMessageDTO> selectFutureEndTimeProductPricingSupply();
    List<ProductPricingMessageDTO> selectFutureStartTimeProductPricingSupply();




    List<CostPrice> listByCityAreaAndTenantId(CostPriceQueryDTO dto);
    List<CostPrice> listValidCostPriceBySkuCode(String sku, Long tenantId);

    List<CostPrice> listValidByCitysSkuIdsTenantId(Set<Long> skuIds, Set<String> citys, Long tenantId);

    List<CostPrice> listInvalidCostPrice();

    void saveOrUpdateByDuplicateKey(CostPrice costPrice);

    List<CostPrice> listBySkuIdsWithoutIds(Long tenantId,Set<Long> skuIds, List<Long> ids);
}
