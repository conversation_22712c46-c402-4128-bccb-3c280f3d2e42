package com.cosfo.item.web.provider.converter;

import com.cofso.item.client.enums.MarketItemLabelStatusEnum;
import com.cofso.item.client.req.MarketItemInputReq;
import com.cofso.item.client.req.MarketItemLabelInsertReq;
import com.cofso.item.client.req.MarketItemLabelQueryReq;
import com.cofso.item.client.resp.MarketItemDetailResp;
import com.cofso.item.client.resp.MarketItemLabelResp;
import com.cosfo.item.web.domain.dto.MarketItemDTO;
import com.cosfo.item.web.domain.dto.MarketItemLabelDTO;
import com.cosfo.item.web.domain.dto.MarketItemLabelQueryDTO;
import com.cosfo.item.web.domain.vo.MarketItemDetailVO;
import com.cosfo.item.web.domain.vo.MarketItemLabelVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName MarketItemConverter
 * @Description
 * <AUTHOR>
 * @Date 14:37 2024/4/29
 * @Version 1.0
 **/
public class MarketItemConverter {


    public static MarketItemDTO marketItemInputReqToDTO(MarketItemInputReq marketItemInputReq) {
        if (marketItemInputReq == null) {
            return null;
        }
        MarketItemDTO marketItemDTO = new MarketItemDTO();
        marketItemDTO.setOutId(marketItemInputReq.getOutId());
        marketItemDTO.setItemLabel(marketItemInputReq.getItemLabel());
        marketItemDTO.setTenantId(marketItemInputReq.getTenantId());
        marketItemDTO.setItemCode(marketItemInputReq.getItemCode());
        return marketItemDTO;
    }

    public static List<MarketItemDetailResp> marketItemDtailVOToResp(List<MarketItemDetailVO> itemDetailByOutId) {
        if (CollectionUtils.isEmpty(itemDetailByOutId)) {
            return Collections.emptyList();
        }

        List<MarketItemDetailResp> marketItemDetailResps = new ArrayList<>(itemDetailByOutId.size());
        itemDetailByOutId.stream().forEach(item -> {
            MarketItemDetailResp marketItemDetailResp = new MarketItemDetailResp();
            marketItemDetailResp.setItemLabel(item.getItemLabel());
            marketItemDetailResp.setOutId(item.getOutId());
            marketItemDetailResp.setItemCode (item.getItemCode ());
            marketItemDetailResps.add(marketItemDetailResp);
        });
        return marketItemDetailResps;
    }
}
