package com.cosfo.item.infrastructure.item.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品上下架表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
public class MarketItemOnsaleStrategyDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 是否大客户专享
     */
    private Integer mType;

    /**
     * 是否展示
     */
    private Integer show;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 上下架目标id
     */
    private Long targetId;

    /**
     * 0下架 1上架
     */
    private Integer onSale;

    /**
     * 上架目标类型 1按租户,2 按门店 3单店 4大客户
     */
    private Integer strategyType;
}
