package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dao.MarketItemUnitDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.item.model.MarketItemUnit;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 初始化 商品单位
 * -99L 表示初始化所有商品单位
 */
@Component
@Slf4j
public class InitItemUnitProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private MarketItemDao itemDao;
    @Autowired
    private MarketItemUnitDao itemUnit;
    @Autowired
    private TenantFacade tenantFacade;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
//        5斤*1包
        String regex = "(\\d+(?:\\.\\d+)?)([A-Za-z\\u4e00-\\u9fa5]+)\\*(\\d+(?:\\.\\d+)?)([A-Za-z\\u4e00-\\u9fa5]+)";


        Pattern pattern = Pattern.compile (regex);

//        List<Long> tenantIds = Collections.singletonList (2L);
        List<Long> tenantIds;
        log.info ("开始进行InitItemUnitProcessor任务,instanceParameters={}", JSON.toJSONString (context.getInstanceParameters ()));
        if (ObjectUtil.isNull (context) || ObjectUtil.isNull (context.getInstanceParameters ())) {
            return new ProcessResult (true);
        } else {
            Long tenantId = Long.valueOf (context.getInstanceParameters ());
            //-99数据初始化 全部数据
            if (tenantId.equals (-99L)) {
                tenantIds = tenantFacade.listAllTenant ().stream ().map (TenantVO::getId).collect (Collectors.toList ());
            } else {
                tenantIds = Collections.singletonList (tenantId);
            }
        }

        tenantIds.forEach (tenantId -> {
            boolean hasNext = true;
            int pageIndex = 1;
            Long total = null;
            Page<MarketItem> page;
            while (hasNext) {
                page = itemDao.pageByTenantId (tenantId, pageIndex, 500);

                page.getRecords ().forEach (e -> {
                    try {
                        String specification = e.getSpecification ();
                        if(StringUtils.isNotBlank (specification) && !specification.contains ("/")) {
                            List<MarketItemUnit> collect = new ArrayList<> ();

                            Matcher matcher = pattern.matcher (specification);

                            if (matcher.find ()) {
//                                200mL*10瓶
                                BigDecimal number1 = new BigDecimal (matcher.group (1));
                                String unit1 = matcher.group (2);
                                BigDecimal number2 = new BigDecimal (matcher.group (3));
                                String unit2 = matcher.group (4);

//                        1=门店订货单位(基本单位)
                                MarketItemUnit marketItemUnit1 = new MarketItemUnit ();
                                marketItemUnit1.setTenantId (tenantId);
                                marketItemUnit1.setItemId (e.getId ());
                                marketItemUnit1.setUnitDesc (e.getSpecificationUnit ());
                                marketItemUnit1.setUnitType (1);
                                marketItemUnit1.setStoreOrderingUnitMultiple (new BigDecimal (1));
                                collect.add (marketItemUnit1);

//                        2=门店库存单位
                                MarketItemUnit marketItemUnit2 = new MarketItemUnit ();
                                marketItemUnit2.setTenantId (tenantId);
                                marketItemUnit2.setItemId (e.getId ());
                                marketItemUnit2.setUnitDesc (unit2);
                                marketItemUnit2.setUnitType (2);
                                marketItemUnit2.setStoreOrderingUnitMultiple (number2);
                                collect.add (marketItemUnit2);

//                        3=门店成本单位
                                MarketItemUnit marketItemUnit3 = new MarketItemUnit ();
                                marketItemUnit3.setTenantId (tenantId);
                                marketItemUnit3.setItemId (e.getId ());
                                marketItemUnit3.setUnitDesc (unit1);
                                marketItemUnit3.setUnitType (3);
                                marketItemUnit3.setStoreOrderingUnitMultiple (number1.multiply (number2));
                                collect.add (marketItemUnit3);
                            }

                            if (CollectionUtil.isNotEmpty (collect)) {
                                itemUnit.saveBatch (collect);
                            }
                        }
                    } catch (Exception ex) {
                        log.warn ("初始化商品单位异常，itemId={}",e.getId (),ex);
                    }
                });
                hasNext = page.hasNext ();
                total = page.getTotal ();
                pageIndex = pageIndex + 1;
                log.info ("InitItemPriceProceessor - list.size={}", total);
            }
        });
        return new ProcessResult (true);
    }
}