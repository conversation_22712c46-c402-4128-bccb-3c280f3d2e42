package com.cosfo.item.web.domain.converter;

import cn.hutool.core.collection.CollectionUtil;
import com.cofso.item.client.resp.StockResp;
import com.cosfo.item.infrastructure.item.model.Stock;
import com.cosfo.item.web.domain.dto.StockDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/16
 */
public class StockConvert {

    /**
     * 转化为StockList
     *
     * @param stockList
     * @return
     */
    public static List<StockDTO> convertToStockList(List<Stock> stockList){

        if (stockList == null) {
            return Collections.emptyList();
        }
        List<StockDTO> stockDTOList = new ArrayList<>();
        for (Stock stock : stockList) {
            stockDTOList.add(toStockDTO(stock));
        }
        return stockDTOList;
    }

    public static StockDTO toStockDTO(Stock stock) {
        if (stock == null) {
            return null;
        }
        StockDTO stockDTO = new StockDTO();
        stockDTO.setId(stock.getId());
        stockDTO.setTenantId(stock.getTenantId());
        stockDTO.setItemId(stock.getItemId());
        stockDTO.setAmount(stock.getAmount());
        return stockDTO;
    }

    public static List<StockResp> convertStockRespList(List<StockDTO> stockDTOList){

        if (stockDTOList == null) {
            return Collections.emptyList();
        }
        List<StockResp> stockRespList = new ArrayList<>();
        for (StockDTO stockDTO : stockDTOList) {
            stockRespList.add(toStockResp(stockDTO));
        }
        return stockRespList;
    }

    public static StockResp toStockResp(StockDTO stockDTO) {
        if (stockDTO == null) {
            return null;
        }
        StockResp stockResp = new StockResp();
        stockResp.setId(stockDTO.getId());
        stockResp.setTenantId(stockDTO.getTenantId());
        stockResp.setItemId(stockDTO.getItemId());
        stockResp.setAmount(stockDTO.getAmount());
        return stockResp;
    }
}
