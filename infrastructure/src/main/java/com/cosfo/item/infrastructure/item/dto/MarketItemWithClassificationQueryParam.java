package com.cosfo.item.infrastructure.item.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MarketItemWithClassificationQueryParam implements Serializable {
    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品编码
     */
    private List<Long> itemIds;

    /**
     * 前台分类
     */
    private List<Long> classificationIds;

    /**
     * 租户id
     */
    private Long tenantId;

    private Integer pageIndex;

    private Integer pageSize;
}
