package com.cosfo.item.infrastructure.item.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 17:08
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemParam {

    /**
     * 商品编码
     */
    private Long itemId;
    private List<Long> itemIds;

    /**
     * tenant_id
     */
    private Long tenantId;

    /**
     * 商品标题
     */
    private String itemTitle;
    private String eqTitle;
    /**
     * sku_id
     */
    private Long skuId;
    private Set<Long> skuIds;
    /**
     * market_id
     */
    private List<Long> marketIds;
    private Long marketId;
    /**
     * 品牌名
     */
    private String brandName;
    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 自有编码模糊查询
     */
    private String itemCodeLike;
    /**
     * 自有编码
     */
    private List<String> itemCodes;
    /**
     * 货源类型
     */
    private Integer goodsType;
    private List<Integer> goodsTypes;
    /**
     * item类型
     */
    private Integer itemType;
    /**
     * 查询非组合品
     * true-只查组合品 false-查询非组合品
     * 为空，查询所有Item
     */
    private Boolean combineFlag;
    /**
     * 上架状态
     */
    private Integer onSale;
    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;
    /**
     * 前台分类
     */
    private List<Long> classificationIds;

    /**
     * 后台类目
     */
    private List<Long> categoryIds;

    /**
     * 大客户id
     */
    private Long adminId;
    /**
     * 鲜沐商品类型
     */
    private boolean notAllFlag;
    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;

    /**
     * 商品预售开关 0-不可预售 1-可预售
     */
    private Integer presaleSwitch;

    /**
     * 外部ID -- pdId
     */
    private Long outId;
}
