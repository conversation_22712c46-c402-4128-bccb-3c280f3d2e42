package com.cosfo.item.infrastructure.item.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.UpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.common.enums.MarketItemOnSaleEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemOnsaleStrategyMappingDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyQueryParam;
import com.cosfo.item.infrastructure.item.mapper.MarketItemOnsaleStrategyMappingMapper;
import com.cosfo.item.infrastructure.item.model.MarketAreaItem;
import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品上下架表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Service
public class MarketItemOnsaleStrategyMappingDaoImpl extends ServiceImpl<MarketItemOnsaleStrategyMappingMapper, MarketItemOnsaleStrategyMapping> implements MarketItemOnsaleStrategyMappingDao {

    @Override
    public List<MarketItemOnsaleStrategyMapping> listMarketItemOnsaleStrategyByItemIds(Long tenantId, List<Long> itemIds) {
        LambdaQueryWrapper<MarketItemOnsaleStrategyMapping> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItemOnsaleStrategyMapping::getTenantId, tenantId);
        wrapper.in(MarketItemOnsaleStrategyMapping::getItemId, itemIds);
        return list(wrapper);
    }

    @Override
    public void saveOrUpdateByItemIdAndTargetIdAndStrategyType(List<MarketItemOnsaleStrategyMapping> list) {
        getBaseMapper().saveOrUpdateByItemIdAndTargetIdAndStrategyType(list);
    }

    @Override
    public List<MarketItemOnsaleStrategyMapping> listByParam(MarketItemOnsaleStrategyQueryParam param) {
        return list(buildQueryWrapper(param));
    }

    @Override
    public void soldOutByMarketItemId(Long tenantId, Long marketItemId) {
        if(Objects.isNull (tenantId) || Objects.isNull (marketItemId)){
            return;
        }
        LambdaQueryWrapper<MarketItemOnsaleStrategyMapping> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItemOnsaleStrategyMapping::getTenantId, tenantId);
        wrapper.eq(MarketItemOnsaleStrategyMapping::getItemId, marketItemId);
        wrapper.select(MarketItemOnsaleStrategyMapping::getId);
        Set<Long> ids = baseMapper.selectObjs (wrapper).stream ().map (o -> (Long) o).collect (Collectors.toSet ());
        if(CollectionUtil.isNotEmpty (ids)){
            LambdaUpdateWrapper<MarketItemOnsaleStrategyMapping> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(MarketItemOnsaleStrategyMapping::getOnSale, MarketItemOnSaleEnum.OnSaleTypeEnum.SOLD_OUT.getCode ());
            updateWrapper.in(MarketItemOnsaleStrategyMapping::getId, ids);
            update(updateWrapper);
        }
    }

    @Override
    public List<MarketItemOnsaleStrategyMapping> listMarketItemOnsaleStrategyByItemIdAndTargetId(Long tenantId, Long itemId, Long targetId) {
        LambdaQueryWrapper<MarketItemOnsaleStrategyMapping> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItemOnsaleStrategyMapping::getTenantId, tenantId);
        wrapper.eq(MarketItemOnsaleStrategyMapping::getItemId, itemId);
        wrapper.eq(MarketItemOnsaleStrategyMapping::getTargetId, targetId);
        return list(wrapper);
    }

    @Override
    public Set<Long> listOnSaleMarketItem(Long tenantId, List<Long> itemIds,List<Long> targetIds) {
        LambdaQueryWrapper<MarketItemOnsaleStrategyMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MarketItemOnsaleStrategyMapping::getItemId,itemIds);
        wrapper.eq(MarketItemOnsaleStrategyMapping::getTenantId,tenantId);
        wrapper.in(CollectionUtils.isNotEmpty (targetIds),MarketItemOnsaleStrategyMapping::getTargetId,targetIds);
        wrapper.eq(MarketItemOnsaleStrategyMapping::getOnSale,MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode ());
        wrapper.select(MarketItemOnsaleStrategyMapping::getItemId);
        return baseMapper.selectObjs(wrapper).stream().map(o -> (Long) o).collect(Collectors.toSet());
    }

    @Override
    public Set<Long> listMTypeMarketItem(Long tenantId, List<Long> itemIds, List<Long> targetIds) {
        LambdaQueryWrapper<MarketItemOnsaleStrategyMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MarketItemOnsaleStrategyMapping::getItemId,itemIds);
        wrapper.eq(MarketItemOnsaleStrategyMapping::getTenantId,tenantId);
        wrapper.in(CollectionUtils.isNotEmpty (targetIds),MarketItemOnsaleStrategyMapping::getTargetId,targetIds);
        wrapper.eq(MarketItemOnsaleStrategyMapping::getMType,MarketItemOnSaleEnum.MTypeEnum.BIG_CUSTOMER_EXCLUSIVITY.getCode ());
        wrapper.select(MarketItemOnsaleStrategyMapping::getItemId);
        return baseMapper.selectObjs(wrapper).stream().map(o -> (Long) o).collect(Collectors.toSet());
    }


    private LambdaQueryWrapper<MarketItemOnsaleStrategyMapping> buildQueryWrapper(MarketItemOnsaleStrategyQueryParam param) {
        LambdaQueryWrapper<MarketItemOnsaleStrategyMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getItemIds()), MarketItemOnsaleStrategyMapping::getItemId, param.getItemIds());
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MarketItemOnsaleStrategyMapping::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getMType ()), MarketItemOnsaleStrategyMapping::getMType, param.getMType());
        queryWrapper.eq(Objects.nonNull(param.getShowFlag ()), MarketItemOnsaleStrategyMapping::getShowFlag, param.getShowFlag());
        queryWrapper.eq(Objects.nonNull(param.getOnSale ()), MarketItemOnsaleStrategyMapping::getOnSale, param.getOnSale());
        queryWrapper.eq(Objects.nonNull(param.getTargetId ()), MarketItemOnsaleStrategyMapping::getTargetId, param.getTargetId());
        return queryWrapper;
    }
}
