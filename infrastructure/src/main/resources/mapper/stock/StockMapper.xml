<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.item.mapper.StockMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.item.infrastructure.item.model.Stock">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, item_id, amount, update_time, create_time
  </sql>

  <update id="increaseStock">
    update stock set amount = amount + #{addAmount} where id = #{id}
  </update>

  <select id="preUpdateQuery" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from stock
    where tenant_id = #{tenantId} and item_id = #{itemId,jdbcType=BIGINT} for update
  </select>
</mapper>
