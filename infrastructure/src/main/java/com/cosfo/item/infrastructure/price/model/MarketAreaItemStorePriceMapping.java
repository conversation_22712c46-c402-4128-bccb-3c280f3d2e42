package com.cosfo.item.infrastructure.price.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 门店商品定价策略映射
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketAreaItemStorePriceMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 门店Id
     */
    private Long storeId;

    /**
     * 价格定价方式Id
     */
    private Long areaItemMappingId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;


}
