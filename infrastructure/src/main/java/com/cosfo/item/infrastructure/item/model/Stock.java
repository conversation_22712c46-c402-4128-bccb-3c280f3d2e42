package com.cosfo.item.infrastructure.item.model;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 库存表(Stock)实体类
 *
 * <AUTHOR>
 * @since 2023-05-05 14:19:47
 */
@Data
@TableName("stock")
public class Stock implements Serializable {
    private static final long serialVersionUID = -28456272429151309L;
    /**
     * 主键、自增
     */     
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;
    /**
     * skuId
     */     
    private Long skuId;
    /**
     * 库存
     */     
    private Integer amount;
         
    private LocalDateTime updateTime;
         
    private LocalDateTime createTime;
    /**
     * item主键Id
     */     
    private Long itemId;

}

