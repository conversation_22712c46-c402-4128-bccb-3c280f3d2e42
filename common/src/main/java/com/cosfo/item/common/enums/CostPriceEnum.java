package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

public interface CostPriceEnum {
    @Getter
    @AllArgsConstructor
    public enum SysType {
        SUMMERFARM(0, "鲜沐"),

        ;
        private Integer code;
        private String desc;
    }
    @Getter
    @AllArgsConstructor
    public enum ValidType {
        FOREVER(0, "永久"),
        CYCLE(1, "周期"),
        ;
        private Integer code;
        private String desc;
    }
    @Getter
    @AllArgsConstructor
    public enum ProductPriceTypeEnum {

        /**
         * 指定价
         */
        SPECIFIED_PRICE(0, "指定价"),

        /**
         * 鲜沐报价单
         */
        SUMMERFATM_PRICE(1, "鲜沐报价单"),
        /**
         * 鲜沐报价单上浮
         */
        SUMMER_FARM_PRICE_UP(2, "鲜沐商城价(上浮)"),
        /**
         * 鲜沐报价单下浮
         */
        SUMMER_FARM_PRICE_DOWN(3, "鲜沐商城价(下浮)"),
        /**
         * 鲜沐报价单加价
         */
        SUMMER_FARM_PRICE_INCREASE(4, "鲜沐商城价(加价)"),
        /**
         * 鲜沐报价单减价
         */
        SUMMER_FARM_PRICE_DECREASE(5, "鲜沐商城价(减价)"),

        ;
        /**
         * 0.01
         */
        public static final BigDecimal CENTS = BigDecimal.valueOf(0.01);
        /**
         * 状态编码
         */
        private Integer code;
        /**
         * 状态描述
         */
        private String desc;
    }
}