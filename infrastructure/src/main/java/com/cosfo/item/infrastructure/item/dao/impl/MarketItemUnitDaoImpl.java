package com.cosfo.item.infrastructure.item.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import com.cosfo.item.infrastructure.item.model.MarketItemUnit;
import com.cosfo.item.infrastructure.item.mapper.MarketItemUnitMapper;
import com.cosfo.item.infrastructure.item.dao.MarketItemUnitDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 商品单位表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Service
public class MarketItemUnitDaoImpl extends ServiceImpl<MarketItemUnitMapper, MarketItemUnit> implements MarketItemUnitDao {

    @Override
    public List<MarketItemUnit> listByItemIds(List<Long> itemIds, Long tenantId) {
        if(CollectionUtil.isEmpty (itemIds) || ObjectUtil.isEmpty (tenantId)){
            return Collections.emptyList ();
        }
        LambdaQueryWrapper<MarketItemUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MarketItemUnit::getItemId, itemIds);
        queryWrapper.eq(MarketItemUnit::getTenantId, tenantId);
        return list (queryWrapper);
    }

    @Override
    public void removeByItemId(Long tenantId, Long itemId) {
        LambdaQueryWrapper<MarketItemUnit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketItemUnit::getItemId, itemId);
        queryWrapper.eq(MarketItemUnit::getTenantId, tenantId);
        remove (queryWrapper);
    }
}
