package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface MarketItemOnsaleStrategyMappingEnum {
    /**
     * 上架目标类型
     */
    @Getter
    @AllArgsConstructor
    public enum StrategyTypeEnum {

        TENANT(1, "品牌方"),

        STORE(2, "门店"),

        SINGLE_STORE(3, "单店"),

        VIP(4, "大客户"),

        ;
        private Integer code;

        /**
         * 描述
         */
        private String desc;
    }
}
