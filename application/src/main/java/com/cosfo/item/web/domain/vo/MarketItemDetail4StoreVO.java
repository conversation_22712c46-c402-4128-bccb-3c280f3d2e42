package com.cosfo.item.web.domain.vo;

import com.cosfo.item.common.dto.LadderPriceDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/12 13:46
 * @Description:
 */
@Data
public class MarketItemDetail4StoreVO {
    /**
     * market_id
     */
    private Long marketId;
    /**
     * market_item_id
     */
    private Long itemId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 库存
     */
    private Integer stockAmount;
    /**
     * 货源类型
     *
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;
    /**
     * item类型
     *
     * @see com.cofso.item.client.enums.ItemTypeEnum
     */
    private Integer itemType;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * sku
     */
    private Long skuId;
    /**
     * 规格
     */
    private String specification;
    private String specificationUnit;
    /**
     * 品牌
     */
    private Long brandId;
    /**
     * 子商品列表
     * 只有当itemType = 组合品时 才会有
     */
    private List<MarketCombineVO> combineItemRespList;

    /**
     * 销售方式 0、可独售 1、搭售可凑单 2、搭售不可凑单
     */
    private Integer itemSaleMode;

    /**
     * 倍数订货 倍数值
     */
    private Integer buyMultiple;
    /**
     * 倍数订货 是否开启， true = 开启 ；false= 关闭
     */
    private Boolean buyMultipleSwitch;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;


    /**
     * 商品描述 文字
     */
    private String descriptionString;
    /**
     * 品牌名
     */
    private String brandName;

    /**
     * 阶梯价
     * eg：[{"price":197.14,"unit":1}]
     */
    private List<LadderPriceDTO> ladderPrices;
}
