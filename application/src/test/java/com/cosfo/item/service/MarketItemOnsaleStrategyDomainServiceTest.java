package com.cosfo.item.service;

import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import com.cosfo.item.web.domain.service.MarketItemOnsaleStrategyDomainService;
import java.util.LinkedList;
import java.util.List;
import java.util.Random;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class MarketItemOnsaleStrategyDomainServiceTest {

    @Resource
    private MarketItemOnsaleStrategyDomainService marketItemOnsaleStrategyDomainService;

    private Random random = new Random();

    @Test
    public void testInsertBatch() {
        int[] itemIds = new int[]{22642, 22642, 22642, 22644, 22644, 22644, 22645, 22645, 22645, 22944, 22944, 22944, 22945,
            22945, 22945, 22946, 22946, 22946, 22947, 22947, 22947, 22948, 22948, 22948, 22949, 22949, 22949, 23048, 23048, 23048};
        List<MarketItemOnsaleStrategyDTO> listOfOnSaleDTO = new LinkedList<>();
        for (int i = 0; i < itemIds.length; i++) {
            MarketItemOnsaleStrategyDTO strategyDTO = new MarketItemOnsaleStrategyDTO();
            strategyDTO.setItemId((long) itemIds[i % itemIds.length]);
            strategyDTO.setTargetId(41684L);
            strategyDTO.setStrategyType(2);

            strategyDTO.setOnSale(Math.min(1, random.nextInt(2)));//随机的取一半
            listOfOnSaleDTO.add(strategyDTO);
        }
        marketItemOnsaleStrategyDomainService.saveOrUpdateMarketItemOnsaleStrategy(24514L, listOfOnSaleDTO);
    }
}
