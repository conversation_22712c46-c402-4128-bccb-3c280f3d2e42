package com.cosfo.item.infrastructure.item.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 10:17
 * @Description: market查询的通用参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketQueryParam {
    private String title;
    private Long tenantId;
    private Long id;
    private List<Long> categoryIds;
}
