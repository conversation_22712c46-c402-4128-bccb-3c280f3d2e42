package com.cosfo.item.infrastructure.price.model;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品价格变化记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketItemPriceLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 价格目标类型 0-品牌方,1-门店,2-运营区域
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Integer targetType;

    /**
     * 价格目标id
     */
//    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long targetId;

    /**
     * sku主键
     */
    private Long marketItemId;

    /**
     * 货品主键
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long skuId;

    /**
     * 价格
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private BigDecimal price;

    /**
     * 价格规则
     */
    private String priceStrategy;

    /**
     * 底价
     */
    private BigDecimal basePrice;

    /**
     * 操作类型,0-新增,1-修改,2-删除
     */
    private Integer opsType;

    /**
     * 价格规则
     * ItemPriceRuleEnum
     */
    private String priceRule;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
