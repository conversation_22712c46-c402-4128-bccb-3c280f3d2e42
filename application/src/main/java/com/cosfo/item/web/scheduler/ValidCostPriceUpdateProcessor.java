package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.item.infrastructure.price.model.FutureValidCostPrice;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.mq.consumer.binlog.strategy.SummerFarmMajorPriceHandler;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import cn.hutool.core.date.LocalDateTimeUtil;
import java.util.*;

/**
 * 每三分钟执行一次，未来生效报价单补偿
 */
@Component
@Slf4j
public class ValidCostPriceUpdateProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private SummerFarmMajorPriceHandler summerFarmMajorPriceHandler;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始进行ValidCostPriceUpdateProcessor任务");
        List<FutureValidCostPrice> list = costPriceDomianService.listFutureValidCostPrice ();

        if(CollectionUtil.isNotEmpty (list)){
            for (FutureValidCostPrice futureValidCostPrice: list) {
                String skuCode = futureValidCostPrice.getSkuCode ();
                Integer areaNo = futureValidCostPrice.getAreaNo ();
                Long adminId = futureValidCostPrice.getAdminId ();
                String validTime = LocalDateTimeUtil.format(futureValidCostPrice.getValidTime (),"yyyy-MM-dd HH:mm:ss");
                try{
                    summerFarmMajorPriceHandler.handle (areaNo, skuCode, adminId,validTime);
                    costPriceDomianService.removeFutureValidCostPriceById(futureValidCostPrice.getId ());
                }catch (Exception e){
                    log.error("未来生效价格变更 cost_price更新失败，sku={},adminid={},areano={},validTime={}",skuCode,adminId, areaNo, validTime,e);
                }
            }
        }

        return new ProcessResult(true);
    }
}