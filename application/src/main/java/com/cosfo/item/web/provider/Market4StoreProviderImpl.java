package com.cosfo.item.web.provider;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.provider.Market4StoreProvider;
import com.cofso.item.client.req.MarketItemDetailQueryReq;
import com.cofso.item.client.req.MarketItemOnSaleReq;
import com.cofso.item.client.req.MarketItemPageQuery4StoreReq;
import com.cofso.item.client.req.MarketItemSimpleInfoResp;
import com.cofso.item.client.resp.MarketItem4StoreResp;
import com.cofso.item.client.resp.MarketItemDetail4StoreResp;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.cofso.item.client.resp.MarketItemQuery4StoreReq;
import com.cofso.page.PageResp;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnSaleSimpleDTO;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.vo.MarketItem4StoreVO;
import com.cosfo.item.web.domain.vo.MarketItemDetail4StoreVO;
import com.cosfo.item.web.domain.vo.MarketItemSimpleInfoVO;
import com.cosfo.item.web.provider.converter.MarketConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/5/11 18:00
 * @Description:
 */
@DubboService
@Slf4j
public class Market4StoreProviderImpl implements Market4StoreProvider {
    @Autowired
    private ItemDomainService itemDomainService;

    @Override
    public DubboResponse<PageResp<MarketItem4StoreResp>> listAllMarketItem(MarketItemPageQuery4StoreReq req) {
        Page<MarketItem4StoreVO> pageInfo = itemDomainService.listMarketItem4Store(MarketConvert.INSTANCE.convert2QueryDto(req));
        if (Objects.isNull(pageInfo)) {
            return DubboResponse.getOK(PageResp.emptyPage(req.getPageNum(), req.getPageSize()));
        } else {
            return DubboResponse.getOK((PageResp.toPageList(MarketConvert.INSTANCE.convert2MallRespList(pageInfo.getRecords()),
                Math.toIntExact(pageInfo.getTotal()), req.getPageNum(), req.getPageSize())));
        }
    }

    @Override
    public DubboResponse<MarketItemDetail4StoreResp> getMarketItemDetail(MarketItemDetailQueryReq req) {
        MarketItemDetail4StoreVO marketItemDetail4Store = itemDomainService.getMarketItemDetail4Store(MarketConvert.INSTANCE.convert2ItemDetail4Store(req));
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2ItemDetailResp4Store(marketItemDetail4Store));
    }

    @Override
    public DubboResponse<PageResp<MarketItemSimpleInfoResp>> queryMarketItemInfo(MarketItemQuery4StoreReq req) {
        Page<MarketItemSimpleInfoVO> pageInfo = itemDomainService.listMarketItemInfo(MarketConvert.INSTANCE.convert2QueryDTO(req));

        if (Objects.isNull(pageInfo)) {
            return DubboResponse.getOK(PageResp.emptyPage(req.getPageNum(), req.getPageSize()));
        } else {
            return DubboResponse.getOK((PageResp.toPageList(MarketConvert.INSTANCE.convert2SimpleInfoResp(pageInfo.getRecords()),
                Math.toIntExact(pageInfo.getTotal()), req.getPageNum(), req.getPageSize())));
        }
    }

    @Override
    public DubboResponse<List<MarketItemOnSaleSimple4StoreResp>> queryMarketItemOnSaleInfo(MarketItemOnSaleReq req) {
        List<MarketItemOnSaleSimpleDTO> marketItemOnSaleSimple4StoreRespList = itemDomainService.queryMarketItemOnSaleInfo(req);
        List<MarketItemOnSaleSimple4StoreResp> respList = MarketConvert.INSTANCE.convert2OnsaleSimpleResp(marketItemOnSaleSimple4StoreRespList);
        return DubboResponse.getOK(respList);
    }

    @Override
    public DubboResponse<List<MarketItemOnSaleSimple4StoreResp>> queryOnSaleMarketItems(MarketItemOnSaleReq req) {
        List<MarketItemOnSaleSimpleDTO> marketItemOnSaleSimple4StoreRespList = itemDomainService.queryOnSaleMarketItems(req);
        List<MarketItemOnSaleSimple4StoreResp> respList = MarketConvert.INSTANCE.convert2OnsaleSimpleResp(marketItemOnSaleSimple4StoreRespList);
        return DubboResponse.getOK(respList);
    }
}
