package com.cosfo.item.infrastructure.item.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 分类 DTO 传输对象
 * @date 2022/5/10 9:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketClassificationUpdateDTO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 上级id
     */
    private Long parentId;

    /**
     * icon
     */
    private String icon;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 一级分类名
     */
    private String firstClassificationName;

    /**
     * 二级分类名
     */
    private String secondClassificationName;

    /**
     * 分类层级
     */
    private String classificationStr;
}
