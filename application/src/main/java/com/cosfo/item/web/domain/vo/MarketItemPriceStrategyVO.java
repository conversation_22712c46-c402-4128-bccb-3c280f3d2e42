package com.cosfo.item.web.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MarketItemPriceStrategyVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * item id
     */
    private Long itemId;

    /**
     * 0、按成本价百分比上浮 1、按成本价定额上浮 2、固定价 3、按售价百分比下调 4、按售价定额下调
     */
    private Integer strategyType;

    /**
     * 策略值
     */
    private BigDecimal strategyValue;

    /**
     * 策略值
     */
    private String priceStrategyValue;

    /**
     * 价格目标 0-品牌方,1-门店,2-运营区域
     */
    private Integer targetType;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 报价目标Ids
     */
    private List<Long> targetIds;
    /**
     * 倒挂值：固定价 - 最高成本价
     */
    private BigDecimal differenceValue;

    /**
     * 备注
     */
    private String remark;
}
