package com.cosfo.item.infrastructure.price.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cosfo.item.infrastructure.price.model.MarketItemUnfairPriceStrategy;
import com.cosfo.item.infrastructure.price.mapper.MarketItemUnfairPriceStrategyMapper;
import com.cosfo.item.infrastructure.price.dao.MarketItemUnfairPriceStrategyDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 商品倒挂策略表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
@Service
public class MarketItemUnfairPriceStrategyDaoImpl extends ServiceImpl<MarketItemUnfairPriceStrategyMapper, MarketItemUnfairPriceStrategy> implements MarketItemUnfairPriceStrategyDao {

    @Override
    public MarketItemUnfairPriceStrategy getDefaultStrategyValueByTenantId(Long tenantId) {
        LambdaQueryWrapper<MarketItemUnfairPriceStrategy> wrapper = new LambdaQueryWrapper ();
        wrapper.eq (MarketItemUnfairPriceStrategy::getTenantId,tenantId);
        wrapper.eq (MarketItemUnfairPriceStrategy::getDefaultFlag,true);
        return getOne (wrapper);
    }

    @Override
    public MarketItemUnfairPriceStrategy getStrategyValueByItemIdAndTargetType(Long tenantId, Long itemId, Integer targetType) {
        LambdaQueryWrapper<MarketItemUnfairPriceStrategy> wrapper = new LambdaQueryWrapper ();
        wrapper.eq (MarketItemUnfairPriceStrategy::getTenantId,tenantId);
        wrapper.eq (MarketItemUnfairPriceStrategy::getItemId, itemId);
        wrapper.eq (MarketItemUnfairPriceStrategy::getTargetType, targetType);
        return getOne (wrapper);
    }

    @Override
    public List<MarketItemUnfairPriceStrategy> getStrategyValueByItemIsdAndTargetType(Long tenantId, List<Long> itemIds, Integer targetType) {
        LambdaQueryWrapper<MarketItemUnfairPriceStrategy> wrapper = new LambdaQueryWrapper ();
        wrapper.eq (MarketItemUnfairPriceStrategy::getTenantId,tenantId);
        wrapper.in (MarketItemUnfairPriceStrategy::getItemId, itemIds);
        wrapper.eq (MarketItemUnfairPriceStrategy::getTargetType, targetType);
        return list (wrapper);
    }

    @Override
    /**
     * 查询未使用默认放倒挂策略的itemid
     */
    public Set<Long> listItemIdsWithOutDefaultStrategy(Long tenantId) {
        LambdaQueryWrapper<MarketItemUnfairPriceStrategy> wrapper = new LambdaQueryWrapper ();
        wrapper.select (MarketItemUnfairPriceStrategy::getItemId);
        wrapper.eq (MarketItemUnfairPriceStrategy::getTenantId,tenantId);
        wrapper.eq (MarketItemUnfairPriceStrategy::getDefaultFlag,false);
        return baseMapper.selectObjs(wrapper).stream().map(o -> (Long) o).collect(Collectors.toSet());
    }

    @Override
    public List<MarketItemUnfairPriceStrategy> getByDefaultFlagAndStrategyValue(List<Integer> strategyValues, Integer defaultFlag, Integer targetType, Long tenantId) {
        LambdaQueryWrapper<MarketItemUnfairPriceStrategy> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollectionUtils.isEmpty(strategyValues), MarketItemUnfairPriceStrategy::getStrategyValue, strategyValues);
        queryWrapper.eq(Objects.nonNull(defaultFlag), MarketItemUnfairPriceStrategy::getDefaultFlag, defaultFlag);
        queryWrapper.eq(Objects.nonNull(targetType), MarketItemUnfairPriceStrategy::getTargetType, targetType);
        queryWrapper.eq(Objects.nonNull(tenantId), MarketItemUnfairPriceStrategy::getTenantId, tenantId);
        return list(queryWrapper);
    }
}
