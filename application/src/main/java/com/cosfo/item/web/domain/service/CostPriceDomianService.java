package com.cosfo.item.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cofso.preferential.client.resp.ProductSkuCityPreferentialCostPriceResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cosfo.item.common.enums.CostPriceEnum;
import com.cosfo.item.common.enums.CostPriceEnum.ProductPriceTypeEnum;
import com.cosfo.item.common.enums.CostPriceLogEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dao.CostPriceDao;
import com.cosfo.item.infrastructure.price.dao.CostPriceLogDao;
import com.cosfo.item.infrastructure.price.dao.CompensateCostPriceDao;
import com.cosfo.item.infrastructure.price.dao.FutureValidCostPriceDao;
import com.cosfo.item.infrastructure.price.dto.*;
import com.cosfo.item.infrastructure.price.model.CompensateCostPrice;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import com.cosfo.item.infrastructure.price.model.CostPriceLog;
import com.cosfo.item.infrastructure.price.model.FutureValidCostPrice;
import com.cosfo.item.web.domain.dto.CostPriceOptDTO;
import com.cosfo.item.web.domain.dto.MerchantAddressDTO;
import com.cosfo.item.web.domain.dto.MerchantStoreAddressDTO;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.skuPreferential.domain.SkuPreferentialDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.cosfo.item.web.domain.converter.CostPriceConverter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 成本价domain
 */
@Component
@Slf4j
public class CostPriceDomianService {
    @Autowired
    private CostPriceDao costPriceDao;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private CompensateCostPriceDao compensateCostPriceDao;
    @Autowired
    private CostPriceLogDao costPriceLogDao;
    @Autowired
    private FutureValidCostPriceDao futureValidCostPriceDao;
    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Autowired
    private SkuPreferentialDomainService preferentialDomainService;

    @Transactional(rollbackFor = Exception.class)
    public CostPriceOptDTO saveOrUpdateByCityAreaAndTenantId(CostPriceDTO dto) {
        CostPriceOptDTO costPriceOptDTO = new CostPriceOptDTO ();
        CostPriceQueryDTO queryDTO = new CostPriceQueryDTO();
        queryDTO.setTenantId(dto.getTenantId());
        queryDTO.setSkuCodes (Collections.singletonList(dto.getSkuCode()));
        queryDTO.setArea(dto.getArea());
        queryDTO.setCity(dto.getCity());
        queryDTO.setProvince (dto.getProvince ());
        CostPrice costPrice = CostPriceConverter.costPriceDTO2Entity(dto);
        Integer dealType = CostPriceLogEnum.OptType.ADD.getCode();
        List<CostPrice> costPrices = costPriceDao.listByCityAreaAndTenantId(queryDTO);
        if(CollectionUtil.isNotEmpty(costPrices)){
            CostPrice costPriceDb = costPrices.stream().findFirst().get();
            Long id = costPriceDb.getId ();
            if(costPrices.size () > 1){
                Set<Long> deleteIds = costPrices.stream ().filter (e -> !Objects.equals (id, e.getId ())).map (CostPrice::getId).collect (Collectors.toSet ());
                costPriceDao.removeByIds (deleteIds);
            }
            //如果没变化则返回
            if(Objects.equals(costPriceDb.getPrice(),costPrice.getPrice()) &&
                    Objects.equals(costPriceDb.getInvalidTime(),costPrice.getInvalidTime()) &&
                    Objects.equals(costPriceDb.getValidTime(),costPrice.getValidTime())&&
                    Objects.equals(costPriceDb.getValidType(),costPrice.getValidType())){
                costPriceOptDTO.setOptFlag (false);
                costPriceOptDTO.setId (costPriceDb.getId ());
                return costPriceOptDTO;
            }
            costPrice.setId(costPriceDb.getId());
            dealType = CostPriceLogEnum.OptType.UPDATE.getCode();
        }
        costPriceDao.saveOrUpdate(costPrice);
        CostPriceLog log = CostPriceConverter.costPriceDTO2LogEntity(dto,dealType);
        costPriceLogDao.save(log);
        costPriceOptDTO.setOptFlag (true);
        costPriceOptDTO.setId (costPrice.getId ());
        return costPriceOptDTO;
    }
    @Deprecated
    public List<CostPriceVO> listCostPriceByCityAreaAndTenantId(CostPriceQueryDTO dto) {
       List<CostPrice> costPrices = costPriceDao.listByCityAreaAndTenantId(dto);
       if(CollectionUtil.isEmpty(costPrices)){
           return Collections.emptyList();
       }
       return costPrices.stream().map(CostPriceConverter::costPrice2VO).collect(Collectors.toList());
    }


    public List<CostPriceVO> listValidCostPriceBySkuCode(String sku, Long tenantId) {
        if(StringUtils.isEmpty (sku)){
            return Collections.emptyList ();
        }
        List<CostPrice> costPrices = costPriceDao.listValidCostPriceBySkuCode(sku,tenantId);
        if(CollectionUtil.isEmpty(costPrices)){
            return Collections.emptyList();
        }
        return costPrices.stream().map(CostPriceConverter::costPrice2VO).collect(Collectors.toList());
    }

    public List<CostPriceVO> selectProductPricingSupplyPriceDTOBySkuId(Long skuId, String sku, Set<MerchantAddressDTO> addressDTOS, Long tenantId){
        if (CollectionUtil.isEmpty(addressDTOS) || ObjectUtil.isEmpty (skuId) || ObjectUtil.isEmpty (sku)) {
            return Collections.emptyList ();
        }
        List<ProductPricingSupplyDTO> productPricingSupplyDTOS = costPriceDao.selectProductPricingSupplyDTOBySkuId (skuId, addressDTOS.stream ().map (MerchantAddressDTO::getCityId).collect(Collectors.toSet ()),tenantId);
        if (CollectionUtil.isEmpty(productPricingSupplyDTOS)) {
            return Collections.emptyList ();
        }

        //报价单
        Map<Long, ProductPricingSupplyDTO> pricingSupplyDTOMap = productPricingSupplyDTOS.stream().collect(Collectors.toMap(e->Long.valueOf (e.getCityId ()), Function.identity()));

        // 省心定
        Map<Long, ProductSkuCityPreferentialCostPriceResp> preferentialCostPriceMap = preferentialDomainService.queryCityPreferentialCostPriceMap (tenantId,skuId,productPricingSupplyDTOS.stream ().map (e->Long.valueOf (e.getCityId ())).collect (Collectors.toList ()));

        //鲜沐商城价
        Map<String, CostPriceVO> costPriceVOMap = listValidCostPriceBySkuCode (sku, tenantId).stream().collect(Collectors.toMap(CostPriceVO::getAddressKey, Function.identity()));


        return addressDTOS.stream().map (e->{
            ProductPricingSupplyDTO productPricingSupplyDTO = pricingSupplyDTOMap.get (e.getCityId ());
            if(ObjectUtil.isEmpty (productPricingSupplyDTO)){
                return null;
            }
            CostPriceVO resultDto = CostPriceConverter.buildAreaCostPriceDTO(productPricingSupplyDTO,e);
            ProductSkuCityPreferentialCostPriceResp preferentialCostPrice = preferentialCostPriceMap.get (e.getCityId ());
            resultDto.setPrice (getPrice(productPricingSupplyDTO,preferentialCostPrice,costPriceVOMap.get (resultDto.getAddressKey ())));
            return resultDto;
        }).filter (Objects::nonNull).collect(Collectors.toList());

    }

    private BigDecimal getPrice(ProductPricingSupplyDTO productPricingSupplyDTO, ProductSkuCityPreferentialCostPriceResp preferentialCostPrice,CostPriceVO costPriceVO) {
        if(ObjectUtil.isNotEmpty (preferentialCostPrice)){
            return preferentialCostPrice.getPrice ();
        }else{
            Integer type = productPricingSupplyDTO.getType ();
            if (ProductPriceTypeEnum.SPECIFIED_PRICE.getCode().equals(type)) {
                return productPricingSupplyDTO.getPrice ();
                // 报价单是随鲜沐价
            } else {
                if(ObjectUtil.isEmpty (costPriceVO)){
                    return null;
                }

                BigDecimal costPrice = costPriceVO.getPrice ();
                if (ProductPriceTypeEnum.SUMMERFATM_PRICE.getCode().equals(type)) {
                    return costPrice;
                } else if (ProductPriceTypeEnum.SUMMER_FARM_PRICE_UP.getCode().equals(type)) {
                    return NumberUtil.div(NumberUtil.mul(costPrice, NumberUtil.add(BigDecimal.valueOf(100), productPricingSupplyDTO.getStrategyValue())),BigDecimal.valueOf(100), 2);
                } else if (ProductPriceTypeEnum.SUMMER_FARM_PRICE_DOWN.getCode().equals(type)) {
                    BigDecimal div = NumberUtil.div (NumberUtil.mul (costPrice, NumberUtil.sub (BigDecimal.valueOf (100), productPricingSupplyDTO.getStrategyValue ())), BigDecimal.valueOf (100), 2);
                    return div.compareTo(ProductPriceTypeEnum.CENTS) <= 0?costPrice:div;
                } else if (ProductPriceTypeEnum.SUMMER_FARM_PRICE_INCREASE.getCode().equals(type)) {
                    return NumberUtil.add(costPrice, productPricingSupplyDTO.getStrategyValue());
                } else if (ProductPriceTypeEnum.SUMMER_FARM_PRICE_DECREASE.getCode().equals(type)) {
                    // 兜底策略，当报价低于0.01时(如鲜沐商城价变化)，取鲜沐供应价
                    if (NumberUtil.sub(costPrice, productPricingSupplyDTO.getStrategyValue()).setScale(2, RoundingMode.HALF_UP).compareTo(ProductPriceTypeEnum.CENTS) <= 0) {
                        return costPrice;
                    }
                    return NumberUtil.sub(costPrice, productPricingSupplyDTO.getStrategyValue());
                }else{
                    return null;
                }
            }
        }
    }


    /**
     * 按照区域批量查询最高成本价
     *
     * @param skuId
     * @param addressDTOS
     * @param tenantId
     * @return
     */
    public List<CostPriceVO> listCostPriceBySkuId(Long skuId, Set<MerchantAddressDTO> addressDTOS, Long tenantId, Integer goodsType) {
        ProductAgentSkuMappingVO productAgentSkuMappingVO = null;
        if ((Objects.equals(goodsType, GoodsTypeEnum.QUOTATION_TYPE.getCode()))) {
            productAgentSkuMappingVO  = productFacade.getProductMappingBySkuIdAndTenantId(skuId, xmTenantId,xmTenantId);
            if (ObjectUtil.isEmpty(productAgentSkuMappingVO)) {
                return Collections.emptyList ();
            }
            return selectProductPricingSupplyPriceDTOBySkuId(skuId,productAgentSkuMappingVO.getAgentSkuCode(),addressDTOS, tenantId).stream().filter (e->ObjectUtil.isNotNull (e.getPrice ())).collect(Collectors.toList());
        }else if(Objects.equals(goodsType, GoodsTypeEnum.SELF_GOOD_TYPE.getCode())){
            productAgentSkuMappingVO  = productFacade.getProductMappingBySkuIdAndTenantId(skuId, xmTenantId,tenantId);
            if (ObjectUtil.isEmpty(productAgentSkuMappingVO)) {
                return Collections.emptyList ();
            }
            return listValidCostPriceBySkuCode(productAgentSkuMappingVO.getAgentSkuCode(), tenantId);
        }
        return Collections.emptyList ();
    }

    public List<CostPriceVO>    listCostPriceBySkuIds(Set<Long> skuIds, MerchantStoreAddressDTO addressDTO, Long tenantId) {
        List<ProductAgentSkuMappingVO> productAgentSkuMappingVOs = productFacade.listProductMappingBySkuIdsAndAgentTenantId(skuIds,tenantId);
        if (CollectionUtil.isEmpty(productAgentSkuMappingVOs)) {
            return Collections.emptyList ();
        }
        CostPriceQueryDTO queryDTO = new CostPriceQueryDTO();
        queryDTO.setTenantId(tenantId);
        queryDTO.setSkuIds (productAgentSkuMappingVOs.stream ().map (ProductAgentSkuMappingVO::getAgentSkuId).collect(Collectors.toList()));
        queryDTO.setArea(addressDTO.getArea());
        queryDTO.setCity(addressDTO.getCity());
        queryDTO.setValidFlag (true);
        List<CostPrice> costPrices = costPriceDao.listByCityAreaAndTenantId (queryDTO);
        if (CollectionUtil.isEmpty(costPrices)) {
            return Collections.emptyList ();
        }
        Map<Long, Long> idMap = productAgentSkuMappingVOs.stream().collect(Collectors.toMap(ProductAgentSkuMappingVO::getAgentSkuId, ProductAgentSkuMappingVO::getSkuId));

        List<CostPriceVO> result = costPrices.stream ().map (e->{
            CostPriceVO costPriceVO = CostPriceConverter.costPrice2VO (e);
            costPriceVO.setFtSkuId (idMap.get (e.getSkuId ()));
            return costPriceVO;
        }).collect (Collectors.toList ());
        return result;
    }
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByCityAreaAndTenantId(CostPriceDTO dto) {
        CostPriceQueryDTO queryDTO = new CostPriceQueryDTO();
        queryDTO.setTenantId(dto.getTenantId());
        queryDTO.setSkuCodes (Collections.singletonList(dto.getSkuCode()));
        queryDTO.setArea(dto.getArea());
        queryDTO.setCity(dto.getCity());
        List<CostPrice> costPrices = costPriceDao.listByCityAreaAndTenantId(queryDTO);
        if(CollectionUtil.isNotEmpty(costPrices)){
            CostPrice costPrice = costPrices.stream().findFirst().get();
            costPriceDao.removeById(costPrice.getId());

            CostPriceLog log = CostPriceConverter.costPriceDTO2LogEntity(dto,CostPriceLogEnum.OptType.DELETE.getCode());
            log.setSkuId(costPrice.getSkuId());
            log.setPrice (BigDecimal.ZERO);
            costPriceLogDao.save(log);
            return true;
        }
        return false;
    }

    /**
     * 返回是否有操作相关的costprice
     * @param sku
     * @param tenantId
     * @param area
     * @param city
     * @param province
     * @param summerFarmCostPriceVO
     * @return
     */
    public boolean synchronize4Summerfarm(String sku, Long tenantId, String area, String city, String province, SummerFarmCostPriceVO summerFarmCostPriceVO) {
        CostPriceDTO dto = new CostPriceDTO();
        dto.setSysType(CostPriceEnum.SysType.SUMMERFARM.getCode());
        dto.setSkuCode(sku);
        dto.setTenantId(tenantId);

        dto.setArea(area);
        dto.setCity(city);
        dto.setProvince (province);
        if (ObjectUtil.isNull(summerFarmCostPriceVO) || ObjectUtil.isNull(summerFarmCostPriceVO.getPrice())) {
           return deleteByCityAreaAndTenantId(dto);
        } else {
            dto.setSkuId(summerFarmCostPriceVO.getSkuId());
            dto.setValidTime(summerFarmCostPriceVO.getValidTime());
            dto.setInvalidTime(summerFarmCostPriceVO.getInvalidTime());
            dto.setValidType(summerFarmCostPriceVO.getValidType());
            dto.setPrice(summerFarmCostPriceVO.getPrice());
            CostPriceOptDTO costPriceOptDTO = saveOrUpdateByCityAreaAndTenantId (dto);
            return costPriceOptDTO.getOptFlag ();
        }
    }

    public List<CostPriceRangeVO> queryCostPriceRange(List<CostPriceQueryRangeDTO> dtos, Long tenantId) {
        List<CostPriceRangeVO> result = new ArrayList();
        Map<Long, List<String>> skuCityMap = dtos.stream().collect(Collectors.toMap(CostPriceQueryRangeDTO::getSkuId,
                x->{ if(CollectionUtil.isEmpty (x.getCitys ())) {
                        return Collections.emptyList ();
                    } else {
                        return x.getCitys ();
                    }
                 })
        );
        Set<Long> skuIds = dtos.stream ().map (CostPriceQueryRangeDTO::getSkuId).collect (Collectors.toSet ());
        Set<String> citys = new HashSet<> ();
        for (CostPriceQueryRangeDTO dto : dtos) {
            if(CollectionUtil.isNotEmpty (dto.getCitys ())){
                citys.addAll (dto.getCitys ());
            }
        }
        List<CostPrice> costPrices =  costPriceDao.listValidByCitysSkuIdsTenantId (skuIds,citys,tenantId);
        Map<Long, List<CostPrice>> skuCityCostPriceMap = costPrices.stream().collect(Collectors.groupingBy(CostPrice::getSkuId));
        skuCityCostPriceMap.forEach ((skuId,cityPriceList)->{
            List<String> cityList = skuCityMap.get (skuId);
            CostPriceRangeVO vo = new CostPriceRangeVO ();
            vo.setSkuId (skuId);
            List<CostPrice> list = cityPriceList.stream ().filter (e -> ObjectUtil.isNotNull (e.getPrice ())).collect (Collectors.toList ());
            if(CollectionUtil.isNotEmpty (cityList)) {
                list = list.stream ().filter (e ->cityList.contains (e.getCity ())).collect (Collectors.toList ());
            }
            if (CollectionUtil.isNotEmpty (list)) {
                vo.setMaxPrice (list.stream ().map (CostPrice::getPrice).distinct ().max (BigDecimal::compareTo).get ());
                vo.setMinPrice (list.stream ().map (CostPrice::getPrice).distinct ().min (BigDecimal::compareTo).get ());
            }
            result.add (vo);
        });
        return result;
    }

    /**
     * 查询所有失效的价格不接受外部调用
     */
    public List<CostPrice> listInvalidCostPrice() {
        return costPriceDao.listInvalidCostPrice ();
    }
    /**
     * 查询所有补偿价格
     */
    public List<CompensateCostPrice> listCompenstateCostPriceByIds(List<Long> ids) {
        return compensateCostPriceDao.listCompenstateCostPriceByIds (ids);
    }
    /**
     * 删除补偿价格
     */
    public void removeCompenstateCostPriceById(Long id) {
        compensateCostPriceDao.removeById (id);
    }

    public void saveCompenstateCostPrice(String sku, Long tenantId, String area, String city, String province, String errorLog) {
        try {
            CompensateCostPrice compensateCostPrice = new CompensateCostPrice ();
            compensateCostPrice.setTenantId(tenantId);
            compensateCostPrice.setSkuCode(sku);
            compensateCostPrice.setArea(area);
            compensateCostPrice.setProvince(province);
            compensateCostPrice.setCity(city);
            compensateCostPrice.setErrorLog(errorLog);
            compensateCostPriceDao.save (compensateCostPrice);
        }catch (Exception e){
            log.error ("保存补偿价格失败sku={},tenantId={},area={},city={},province={},errorLog={}",sku,tenantId,area,city,province,errorLog,e);
        }
    }

    /**
     * 保存未来生效的大客户报价单
     * @param areaNo
     * @param sku
     * @param tenantId
     * @param validTime
     */
    public void saveFutureValidCostPrice(Integer areaNo, String sku, Long tenantId,Long adminId, LocalDateTime validTime) {
        FutureValidCostPrice futureValidCostPrice = new FutureValidCostPrice ();
        futureValidCostPrice.setTenantId(tenantId);
        futureValidCostPrice.setAdminId (adminId);
        futureValidCostPrice.setSkuCode(sku);
        futureValidCostPrice.setAreaNo(areaNo);
        futureValidCostPrice.setValidTime(validTime);
        futureValidCostPriceDao.save (futureValidCostPrice);
    }

    public List<FutureValidCostPrice> listFutureValidCostPrice() {
       return futureValidCostPriceDao.listFutureValidCostPrice();
    }

    public void removeFutureValidCostPriceById(Long id) {
        futureValidCostPriceDao.removeById (id);
    }

    /**
     * 自营品更新， 删除未更新/新增 的货品的costprice
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCostPrice4SelfGoods(Long tenantId,Set<Long> agentSkuIds, List<Long> ids) {
        if(CollectionUtil.isEmpty (agentSkuIds)) {
            return;
        }
        List<CostPrice> list = costPriceDao.listBySkuIdsWithoutIds(tenantId,agentSkuIds,ids);
        if(CollectionUtil.isNotEmpty (list)){
            log.info ("deleteCostPrice4SelfGoods,size={}",list.size ());
            costPriceDao.removeByIds(list.stream().map (CostPrice::getId).collect(Collectors.toList()));
            List<CostPriceLog> logs = list.stream ().map (e -> {
                CostPriceLog log = CostPriceConverter.costPrice2LogEntity (e, CostPriceLogEnum.OptType.DELETE.getCode ());
                log.setPrice (BigDecimal.ZERO);
                return log;
            }).collect (Collectors.toList ());
            costPriceLogDao.saveBatch (logs);
        }else{
            log.info ("deleteCostPrice4SelfGoods=null");
        }
    }
}
