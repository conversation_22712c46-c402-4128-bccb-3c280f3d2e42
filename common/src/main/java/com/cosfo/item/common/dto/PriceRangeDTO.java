package com.cosfo.item.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 16:07
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PriceRangeDTO {

    /**
     * 最大值
     */
    private BigDecimal minPrice;

    /**
     * 最小值
     */
    private BigDecimal maxPrice;

    /**
     * 区间
     */
    private String priceStr;

    /**
     * 商品 id
     */
    private Long marketItemId;
}
