package com.cosfo.item.infrastructure.price.mapper;

import com.cosfo.item.infrastructure.price.model.MarketItemPriceLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 商品价格变化记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Mapper
public interface MarketItemPriceLogMapper extends BaseMapper<MarketItemPriceLog> {

    void batchInsert(@Param("collection") Collection<MarketItemPriceLog> collection);
}
