package com.cosfo.item.web.skuPreferential.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: fansongsong
 * @Date: 2024-02-22
 * @Description:
 */
@Data
public class ProductSkuPreferentialCostPriceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    private Long id;

    /**
     * 租户 id
     */
    private Long tenantId;

    /**
     * sku id
     */
    private Long skuId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 可用数量
     */
    private Integer availableQuantity;

    /**
     * 生效开始时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 删除标记 0未删除，1删除
     */
    private Integer deleted;

}
