package com.cosfo.item.infrastructure.offline.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.infrastructure.offline.model.SelfGoodsCostPrice;
import com.cosfo.item.infrastructure.offline.mapper.SelfGoodsCostPriceMapper;
import com.cosfo.item.infrastructure.offline.dao.SelfGoodsCostPriceDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 自营货品成本价格表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Service
@DS("offline")
public class SelfGoodsCostPriceDaoImpl extends ServiceImpl<SelfGoodsCostPriceMapper, SelfGoodsCostPrice> implements SelfGoodsCostPriceDao {

    @Override
    public Page<SelfGoodsCostPrice> pageByTenantId(Long tenantId,int pageSize, Integer pageNum) {
        LambdaQueryWrapper<SelfGoodsCostPrice> wrapper = new LambdaQueryWrapper ();
        wrapper.eq (ObjectUtil.isNotNull (tenantId),SelfGoodsCostPrice::getTenantId,tenantId);
        return page(new Page<>(pageNum,pageSize),wrapper);
    }
}
