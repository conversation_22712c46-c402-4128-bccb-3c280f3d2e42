package com.cosfo.item.infrastructure.item.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/8 16:11
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketClassificationQueryParam {
    private List<Long> ids;
    private Long tenantId;
    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * id
     */
    private Long id;
}
