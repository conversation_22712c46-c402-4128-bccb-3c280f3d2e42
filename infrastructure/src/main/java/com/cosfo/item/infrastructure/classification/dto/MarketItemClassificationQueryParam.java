package com.cosfo.item.infrastructure.classification.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/5 11:03
 * @Description:
 */
@Data
@Builder
public class MarketItemClassificationQueryParam {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * market_id
     */
    private Long marketId;
    private List<Long> marketIds;

    /**
     * 分类id
     */
    private Long classificationId;

    /**
     * classificationIds
     */
    private List<Long> classificationIds;
}
