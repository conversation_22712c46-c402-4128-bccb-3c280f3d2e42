package com.cosfo.item.web.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2023/5/17 10:48
 * @Description:
 */
@Data
public class ProductPricingSupplyCityMappingVO {
    /**
     * primary key
     */
    private Long id;

    /**
     * 报价单主键Id
     */
    private Long productPricingSupplyId;

    /**
     * 城市Id
     */
    private Long cityId;

    /**
     * 报价方式 0、指定价  1,随鲜沐商城价
     */
    private Integer type;

    /**
     * 0、成本供价 1、报价单供价
     */
    private Integer supplyType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;

    /**
     * 删除标记 0未删除，1删除
     */
    private Integer deleted;

    /**
     * 品牌方租户
     */
    private Long tenantId;

    /**
     * 供应商租户
     */
    private Long supplyTenantId;

    /**
     * 供应商商品skuId
     */
    private Long supplySkuId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 最大价格
     */
    private BigDecimal minPrice;

    /**
     * 最小价格
     */
    private BigDecimal maxPrice;
}
