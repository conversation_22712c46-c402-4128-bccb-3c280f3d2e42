package com.cosfo.item.web.domain.vo;

import lombok.Data;

/**
 * @author: monna.chen
 * @Date: 2023/5/5 13:56
 * @Description:
 */
@Data
public class ProductSkuVO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 鲜沐sku
     */
    private String sku;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * spu id
     */
    private Long spuId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 品牌Id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 主标题
     */
    private String title;

    /**
     * 图片
     */
    private String mainPicture;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 产地
     */
    private String origin;

    /**
     * 储存区域 0、常温 1、冷藏 2、冷冻
     */
    private Integer storageLocation;
    /**
     * 存储温度
     */
    private String storageTemperature;
    /**
     * 保质期
     */
    private Integer guaranteePeriod;
    /**
     * 0 天 1 月 2 年
     */
    private Integer guaranteeUnit;

    /**
     * 类目Id
     */
    private Long firstCategoryId;
    /**
     * 一级类目
     */
    private String firstCategory;
    /**
     * 二级类目Id
     */
    private Long secondCategoryId;
    /**
     * 二级类目
     */
    private String secondCategory;
    /**
     * 三级类目Id
     */
    private Long thirdCategoryId;
    /**
     * 三级类目
     */
    private String thirdCategory;
}
