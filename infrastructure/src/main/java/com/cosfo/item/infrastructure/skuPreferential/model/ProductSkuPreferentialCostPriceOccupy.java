package com.cosfo.item.infrastructure.skuPreferential.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 省心定库存占用表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Getter
@Setter
@TableName("product_sku_preferential_cost_price_occupy")
public class ProductSkuPreferentialCostPriceOccupy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户 id
     */
    private Long tenantId;

    /**
     * 省心定 id
     */
    private Long skuPreferentialCostPriceId;

    /**
     * sku编码
     */
    private Long skuId;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 使用数量
     */
    private Integer occupyQuantity;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;


}
