package com.cosfo.item.common.enums;

import cn.hutool.core.stream.CollectorUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.compress.utils.Lists;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cosfo.item.common.enums.MarketItemPriceStrategyEnum.StrategyTypeEnum.*;

public interface MarketItemPriceStrategyEnum {
    @Getter
    @AllArgsConstructor
    public enum TargetTypeEnum {

        TENANT(0, "品牌方"),

        STORE(1, "门店"),

        AREA_NO(2, "运营区域"),
        /**
         * 门店分组
         */
        STORE_GROUP(3, "门店分组"),

        NULL (-1, "错误");
        ;
        private Integer code;

        /**
         * 描述
         */
        private String desc;

        public static TargetTypeEnum of(Integer code) {
            for (TargetTypeEnum e: values()) {
                if (e.getCode().equals(code)) {
                    return e;
                }
            }
            return TargetTypeEnum.NULL;
        }
    }
    @Getter
    @AllArgsConstructor
    public enum StrategyTypeEnum {
        /**
         * 按成本价百分比上浮
         */
        COST_PRICE_ADD_PERCENTAGE(0, "按成本价百分比上浮"),

        COST_PRICE_ADD_FIXED(1,"按成本价定额上浮"),

        ASSIGN(2,"固定价"),

        SALE_PRICE_REDUCE_PERCENTAGE(3,"按按组合品总价百分比下调"),

        SALE_PRICE_REDUCE_FIXED(4,"按按组合品总价定额下调"),

        SALE_PRICE_TOTAL(5,"按组合品总价固定价格"),

        NULL (-1, "错误");
        /**
         * 状态编码
         */
        private Integer code;
        /**
         * 状态描述
         */
        private String desc;
        //需要成本价的策略
        public static List<Integer> WITH_COSTPRICE_STRATEGY_LIST = Stream.of(COST_PRICE_ADD_PERCENTAGE.getCode (), COST_PRICE_ADD_FIXED.getCode ()).collect(Collectors.toList());

        public static StrategyTypeEnum of(Integer code) {
            for (StrategyTypeEnum strategyTypeEnum: values()) {
                if (strategyTypeEnum.getCode().equals(code)) {
                    return strategyTypeEnum;
                }
            }
            return StrategyTypeEnum.NULL;
        }

        /**
         * 根据item的价格类型，获取价格策略的策略类型
         *
         * @param priceTypeCode
         * @return
         */
        public static Integer getCodeByPriceType(Integer priceTypeCode) {
            if (priceTypeCode.equals(MarketItemEnum.PriceTypeEnum.COMBINE_REDUCE_FIXED.getCode())) {
                return MarketItemPriceStrategyEnum.StrategyTypeEnum.SALE_PRICE_REDUCE_FIXED.getCode();
            } else if (priceTypeCode.equals(MarketItemEnum.PriceTypeEnum.COMBINE_REDUCE_PERCENTAGE.getCode())) {
                return MarketItemPriceStrategyEnum.StrategyTypeEnum.SALE_PRICE_REDUCE_PERCENTAGE.getCode();
            } else if (priceTypeCode.equals(MarketItemEnum.PriceTypeEnum.COMBINE_TOTAL.getCode())) {
                return MarketItemPriceStrategyEnum.StrategyTypeEnum.SALE_PRICE_TOTAL.getCode();
            } else {
                return null;
            }
        }
    }


}
