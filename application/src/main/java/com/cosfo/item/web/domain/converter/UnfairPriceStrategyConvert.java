package com.cosfo.item.web.domain.converter;

import com.cofso.item.client.req.MarketItemUnfairPriceStrategyReq;
import com.cosfo.item.web.domain.dto.MarketItemUnfairPriceStrategyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/8/7
 */
@Mapper
public interface UnfairPriceStrategyConvert {
    UnfairPriceStrategyConvert INSTANCE = Mappers.getMapper(UnfairPriceStrategyConvert.class);

    MarketItemUnfairPriceStrategyDTO convertTO(MarketItemUnfairPriceStrategyReq marketItemUnfairPriceStrategyReq);
}
