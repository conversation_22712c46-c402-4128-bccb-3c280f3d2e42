package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.infrastructure.price.dao.*;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.producer.MqProducer;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 定时任务，每日凌晨23：00 检索出明天 生效/失效的报价单 发送延迟消息
 */
@Component
@Slf4j
public class InitFeatureInvaidSupplyPriceProceessor extends XianMuJavaProcessorV2 {
    @Autowired
    private CostPriceDao costPriceDao;

    @Autowired
    private MqProducer mqProducer;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        List<ProductPricingMessageDTO> productPricingMessageDTOS = new ArrayList<> ();
        //        查询 明天失效的报价单 发送消息
        List<ProductPricingMessageDTO> ends = costPriceDao.selectFutureEndTimeProductPricingSupply ();
        log.info ("productPricingMessageDTOS -end not null,productPricingMessageDTOS={}" , JSON.toJSONString (ends));
        if(CollectionUtil.isNotEmpty (ends)){
            productPricingMessageDTOS.addAll (ends);
        }
//        查询 明天生效的报价单 发送消息
        List<ProductPricingMessageDTO> starts = costPriceDao.selectFutureStartTimeProductPricingSupply ();
        log.info ("productPricingMessageDTOS -start not null,productPricingMessageDTOS={}" , JSON.toJSONString (starts));
        if(CollectionUtil.isNotEmpty (starts)){
            productPricingMessageDTOS.addAll (starts);
        }

        if(CollectionUtil.isNotEmpty (productPricingMessageDTOS)) {
            productPricingMessageDTOS.stream ().filter (e -> ObjectUtil.isNotNull (e.getDealTime ())).forEach (productPricingMessageDTO -> {
                LocalDateTime time = productPricingMessageDTO.getDealTime ();
                mqProducer.sendStartDeliver (RocketMqConstant.Topic.SUPPLY_PRICE, RocketMqConstant.Tag.SUPPLY_PRICE, JSON.toJSON (productPricingMessageDTO), time.plusSeconds (1));
            });
        }
        return new ProcessResult(true);
    }
}
