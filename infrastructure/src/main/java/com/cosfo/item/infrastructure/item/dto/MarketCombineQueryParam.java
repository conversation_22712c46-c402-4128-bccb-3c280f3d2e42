package com.cosfo.item.infrastructure.item.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 18:20
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketCombineQueryParam {
    /**
     * 组合包itemID
     */
    private Long itemId;
    private List<Long> itemIds;

    /**
     * 子itemid
     */
    private Long subItemId;
    private List<Long> subItemIds;

    /**
     * 组合包标题
     */
    private String combineMarketTitle;

    /**
     * 组合包market编码
     */
    private Long combineMarketId;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;

}
