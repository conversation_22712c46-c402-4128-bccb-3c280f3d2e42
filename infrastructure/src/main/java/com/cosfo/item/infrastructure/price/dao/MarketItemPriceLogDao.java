package com.cosfo.item.infrastructure.price.dao;

import com.cosfo.item.infrastructure.price.model.MarketItemPriceLog;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.Collection;

/**
 * <p>
 * 商品价格变化记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface MarketItemPriceLogDao extends IService<MarketItemPriceLog> {

    void batchInsert(Collection<MarketItemPriceLog> priceLogCollection);
}
