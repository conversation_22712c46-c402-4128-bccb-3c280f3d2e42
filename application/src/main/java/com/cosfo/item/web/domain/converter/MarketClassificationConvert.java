package com.cosfo.item.web.domain.converter;

import com.cofso.item.client.req.MarketClassificationAddReq;
import com.cofso.item.client.req.MarketClassificationQueryReq;
import com.cofso.item.client.req.MarketClassificationUpdateReq;
import com.cofso.item.client.resp.MarketClassificationResp;
import com.cofso.item.client.resp.MarketClassificationTreeResp;
import com.cosfo.item.infrastructure.classification.model.MarketClassification;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationAddDTO;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationUpdateDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationQueryDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationTreeDTO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/8
 */
public class MarketClassificationConvert {

    /**
     * 转化为MarketClassificationAddDTO
     *
     * @param marketClassificationAddReq
     * @return
     */
    public static MarketClassificationAddDTO convertToMarketClassificationAddDTO(MarketClassificationAddReq marketClassificationAddReq){
        if (marketClassificationAddReq == null) {
            return null;
        }
        MarketClassificationAddDTO marketClassificationAddDTO = new MarketClassificationAddDTO();
        marketClassificationAddDTO.setTenantId(marketClassificationAddReq.getTenantId());
        marketClassificationAddDTO.setName(marketClassificationAddReq.getName());
        marketClassificationAddDTO.setParentId(marketClassificationAddReq.getParentId());
        marketClassificationAddDTO.setIcon(marketClassificationAddReq.getIcon());
        marketClassificationAddDTO.setSort(marketClassificationAddReq.getSort());
        marketClassificationAddDTO.setFirstClassificationName(marketClassificationAddReq.getFirstClassificationName());
        marketClassificationAddDTO.setSecondClassificationName(marketClassificationAddReq.getSecondClassificationName());
        marketClassificationAddDTO.setClassificationStr(marketClassificationAddReq.getClassificationStr());
        return marketClassificationAddDTO;
    }

    /**
     * 转化为MarketClassification
     *
     * @param marketClassificationAddDTO
     * @return
     */
    public static MarketClassification convertToMarketClassification(MarketClassificationAddDTO marketClassificationAddDTO){
        if (marketClassificationAddDTO == null) {
            return null;
        }
        MarketClassification marketClassification = new MarketClassification();
        marketClassification.setTenantId(marketClassificationAddDTO.getTenantId());
        marketClassification.setName(marketClassificationAddDTO.getName());
        marketClassification.setIcon(marketClassificationAddDTO.getIcon());
        marketClassification.setParentId(marketClassificationAddDTO.getParentId());
        marketClassification.setSort(marketClassificationAddDTO.getSort());
        return marketClassification;
    }

    /**
     * 转化为MarketClassificationUpdateDTO
     *
     * @param marketClassificationUpdateReq
     * @return
     */
    public static MarketClassificationUpdateDTO convertToMarketClassificationUpdateDTO(MarketClassificationUpdateReq marketClassificationUpdateReq){
        if (marketClassificationUpdateReq == null) {
            return null;
        }

        MarketClassificationUpdateDTO marketClassificationUpdateDTO = new MarketClassificationUpdateDTO();
        marketClassificationUpdateDTO.setId(marketClassificationUpdateReq.getId());
        marketClassificationUpdateDTO.setTenantId(marketClassificationUpdateReq.getTenantId());
        marketClassificationUpdateDTO.setName(marketClassificationUpdateReq.getName());
        marketClassificationUpdateDTO.setParentId(marketClassificationUpdateReq.getParentId());
        marketClassificationUpdateDTO.setIcon(marketClassificationUpdateReq.getIcon());
        marketClassificationUpdateDTO.setSort(marketClassificationUpdateReq.getSort());
        marketClassificationUpdateDTO.setFirstClassificationName(marketClassificationUpdateReq.getFirstClassificationName());
        marketClassificationUpdateDTO.setSecondClassificationName(marketClassificationUpdateReq.getSecondClassificationName());
        marketClassificationUpdateDTO.setClassificationStr(marketClassificationUpdateReq.getClassificationStr());
        return marketClassificationUpdateDTO;
    }

    /**
     * 转化为 marketClassificationUpdateDTO
     *
     * @param marketClassificationUpdateDTO
     * @return
     */
    public static MarketClassification convertToMarketClassification(MarketClassificationUpdateDTO marketClassificationUpdateDTO){
        if (marketClassificationUpdateDTO == null) {
            return null;
        }

        MarketClassification marketClassification = new MarketClassification();
        marketClassification.setId(marketClassificationUpdateDTO.getId());
        marketClassification.setTenantId(marketClassificationUpdateDTO.getTenantId());
        marketClassification.setName(marketClassificationUpdateDTO.getName());
        marketClassification.setIcon(marketClassificationUpdateDTO.getIcon());
        marketClassification.setParentId(marketClassificationUpdateDTO.getParentId());
        marketClassification.setSort(marketClassificationUpdateDTO.getSort());
        return marketClassification;
    }

    /**
     * 转化为MarketClassificationTreeDTO
     *
     * @param marketClassification
     * @return
     */
    public static MarketClassificationTreeDTO convertToMarketClassificationTreeDTO(MarketClassification marketClassification){
        if (marketClassification == null) {
            return null;
        }

        MarketClassificationTreeDTO marketClassificationTreeDTO = new MarketClassificationTreeDTO();
        marketClassificationTreeDTO.setId(marketClassification.getId());
        marketClassificationTreeDTO.setName(marketClassification.getName());
        marketClassificationTreeDTO.setParentId(marketClassification.getParentId());
        marketClassificationTreeDTO.setIcon(marketClassification.getIcon());
        marketClassificationTreeDTO.setSort(marketClassification.getSort());
        return marketClassificationTreeDTO;
    }

    /**
     * 转化为MarketClassificationTreeRespList
     *
     * @param marketClassificationTreeDTOList
     * @return
     */
    public static List<MarketClassificationTreeResp> convertToMarketClassificationTreeRespDTO(List<MarketClassificationTreeDTO> marketClassificationTreeDTOList){

        if (marketClassificationTreeDTOList == null) {
            return Collections.emptyList();
        }
        List<MarketClassificationTreeResp> marketClassificationTreeRespList = new ArrayList<>();
        for (MarketClassificationTreeDTO marketClassificationTreeDTO : marketClassificationTreeDTOList) {
            marketClassificationTreeRespList.add(toMarketClassificationTreeResp(marketClassificationTreeDTO));
        }
        return marketClassificationTreeRespList;
    }

    public static MarketClassificationTreeResp toMarketClassificationTreeResp(MarketClassificationTreeDTO marketClassificationTreeDTO) {
        if (marketClassificationTreeDTO == null) {
            return null;
        }
        MarketClassificationTreeResp marketClassificationTreeResp = new MarketClassificationTreeResp();
        marketClassificationTreeResp.setId(marketClassificationTreeDTO.getId());
        marketClassificationTreeResp.setTenantId(marketClassificationTreeDTO.getTenantId());
        marketClassificationTreeResp.setName(marketClassificationTreeDTO.getName());
        marketClassificationTreeResp.setParentId(marketClassificationTreeDTO.getParentId());
        marketClassificationTreeResp.setIcon(marketClassificationTreeDTO.getIcon());
        marketClassificationTreeResp.setSort(marketClassificationTreeDTO.getSort());
        List<MarketClassificationTreeDTO> childList = marketClassificationTreeDTO.getChildList();
        if(!CollectionUtils.isEmpty(childList)) {
            marketClassificationTreeResp.setChildList(convertToMarketClassificationTreeRespDTO(childList));
        }

        return marketClassificationTreeResp;
    }

    public static MarketClassificationResp convertTOMarketClassificationResp(MarketClassificationDTO marketClassificationDTO ){
        if (marketClassificationDTO == null) {
            return null;
        }

        MarketClassificationResp marketClassificationResp = new MarketClassificationResp();
        marketClassificationResp.setId(marketClassificationDTO.getId());
        marketClassificationResp.setTenantId(marketClassificationDTO.getTenantId());
        marketClassificationResp.setName(marketClassificationDTO.getName());
        marketClassificationResp.setParentId(marketClassificationDTO.getParentId());
        marketClassificationResp.setIcon(marketClassificationDTO.getIcon());
        marketClassificationResp.setSort(marketClassificationDTO.getSort());
        marketClassificationResp.setFirstClassificationName(marketClassificationDTO.getFirstClassificationName());
        marketClassificationResp.setSecondClassificationName(marketClassificationDTO.getSecondClassificationName());
        marketClassificationResp.setClassificationStr(marketClassificationDTO.getClassificationStr());
        return marketClassificationResp;
    }

    public static List<MarketClassificationResp> convertTOMarketClassificationRespList(List<MarketClassificationDTO> marketClassificationDTOS){

        if (marketClassificationDTOS == null) {
            return Collections.emptyList();
        }
        List<MarketClassificationResp> marketClassificationRespList = new ArrayList<>();
        for (MarketClassificationDTO marketClassificationDTO : marketClassificationDTOS) {
            marketClassificationRespList.add(convertTOMarketClassificationResp(marketClassificationDTO));
        }
        return marketClassificationRespList;
    }

    public static MarketClassificationQueryDTO convertToMarketClassificationQueryDTO(MarketClassificationQueryReq marketClassificationQueryReq){
        if (marketClassificationQueryReq == null) {
            return null;
        }

        MarketClassificationQueryDTO marketClassificationQueryDTO = new MarketClassificationQueryDTO();
        marketClassificationQueryDTO.setId(marketClassificationQueryReq.getId());
        marketClassificationQueryDTO.setParentId(marketClassificationQueryReq.getParentId());
        marketClassificationQueryDTO.setTenantId(marketClassificationQueryReq.getTenantId());
        marketClassificationQueryDTO.setName(marketClassificationQueryReq.getName());
        marketClassificationQueryDTO.setMarketId(marketClassificationQueryReq.getMarketId());
        marketClassificationQueryDTO.setClassificationIds(marketClassificationQueryReq.getClassificationIds());
        return marketClassificationQueryDTO;
    }

    public static List<MarketClassificationDTO> convertToMarketClassificationDTOList(List<MarketClassification> marketClassifications){
        if (marketClassifications == null) {
            return Collections.emptyList();
        }
        List<MarketClassificationDTO> marketClassificationDTOList = new ArrayList<>();
        for (MarketClassification marketClassification : marketClassifications) {
            marketClassificationDTOList.add(toMarketClassificationDTO(marketClassification));
        }
        return marketClassificationDTOList;
    }

    public static MarketClassificationDTO toMarketClassificationDTO(MarketClassification marketClassification) {
        if (marketClassification == null) {
            return null;
        }
        MarketClassificationDTO marketClassificationDTO = new MarketClassificationDTO();
        marketClassificationDTO.setId(marketClassification.getId());
        marketClassificationDTO.setTenantId(marketClassification.getTenantId());
        marketClassificationDTO.setName(marketClassification.getName());
        marketClassificationDTO.setParentId(marketClassification.getParentId());
        marketClassificationDTO.setIcon(marketClassification.getIcon());
        marketClassificationDTO.setSort(marketClassification.getSort());
        return marketClassificationDTO;
    }

    public List<MarketClassificationResp> convertToMarketClassificationRespList(List<MarketClassificationDTO> marketClassificationDTOS){
        if (marketClassificationDTOS == null) {
            return Collections.emptyList();
        }
        List<MarketClassificationResp> marketClassificationRespList = new ArrayList<>();
        for (MarketClassificationDTO marketClassificationDTO : marketClassificationDTOS) {
            marketClassificationRespList.add(convertTOMarketClassificationResp(marketClassificationDTO));
        }
        return marketClassificationRespList;
    }
}
