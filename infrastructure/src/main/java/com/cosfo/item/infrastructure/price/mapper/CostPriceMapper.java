package com.cosfo.item.infrastructure.price.mapper;

import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingSupplyDTO;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 成本价格表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@Mapper
public interface CostPriceMapper extends BaseMapper<CostPrice> {
    /**
     * 查询sku对应城市报价单信息
     *
     * @param tenantId
     * @param cityIds
     * @param skuId
     * @return
     */
    List<ProductPricingSupplyDTO> selectProductPricingSupplyDTOBySkuId(@Param("tenantId") Long tenantId,
                                                                       @Param("cityIds") Set<Long> cityIds,
                                                                       @Param("skuId") Long skuId);
    /**
     * 查询结束时间是今天明天的报价单
     */
    List<ProductPricingMessageDTO> selectFutureEndTimeProductPricingSupply();
    /**
     * 查询开始时间是今天明天的报价单
     */
    List<ProductPricingMessageDTO> selectFutureStartTimeProductPricingSupply();
    /**
     * 报价单查询skuid
     *
     * @return
     */
    ProductPricingMessageDTO selectSkuIdsByProductPricingSupplyId(@Param("productPricingSupplyId") Long productPricingSupplyId);


    void saveOrUpdateByDuplicateKey(CostPrice costPrice);
}
