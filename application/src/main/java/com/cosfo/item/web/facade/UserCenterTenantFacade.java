package com.cosfo.item.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.item.common.enums.BusinessInformationTypeEnum;
import com.cosfo.item.common.enums.TenantEnums;
import com.cosfo.item.web.domain.converter.TenantMapperConvert;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.businessInfo.provider.BusinessInformationQueryProvider;
import net.xianmu.usercenter.client.businessInfo.req.BusinessInformationQueryReq;
import net.xianmu.usercenter.client.businessInfo.resp.BusinessInformationResultResp;
import net.xianmu.usercenter.client.merchant.provider.MerchantAddressQueryProvider;
import net.xianmu.usercenter.client.tenant.provider.TenantQueryProvider;
import net.xianmu.usercenter.client.tenant.req.TenantQueryReq;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserCenterTenantFacade {

    @DubboReference
    private TenantQueryProvider tenantQueryProvider;

    @DubboReference
    private BusinessInformationQueryProvider queryProvider;

    @DubboReference
    private MerchantAddressQueryProvider merchantAddressQueryProvider;

    /**
     * 查询所有租户数据
     *
     * @param tenantQueryDTO
     * @return
     */
    public List<TenantResp> list(com.cosfo.manage.client.tenant.req.TenantQueryReq tenantQueryDTO) {
        TenantQueryReq tenantQueryReq = new TenantQueryReq();
        tenantQueryReq.setAdminId(tenantQueryDTO.getAdminId());
        tenantQueryReq.setId(tenantQueryDTO.getTenantId());
        tenantQueryReq.setTenantIdList(tenantQueryDTO.getTenantIds());
        tenantQueryReq.setType(TenantEnums.type.BRAND_PARTY.getCode());
        List<TenantResultResp> tenants = getTenantsByQuery(tenantQueryReq);

        if (CollectionUtils.isEmpty(tenants)) {
            return Collections.EMPTY_LIST;
        }

        // 批量查询公司名称
        List<Long> tenantIds = tenants.stream().map(TenantResultResp::getId).collect(Collectors.toList());
        List<BusinessInformationResultResp> businessInformationResultResps = batchQueryByTenantIds(tenantIds);
        Map<Long, BusinessInformationResultResp> tenantCompanyMap = businessInformationResultResps.stream().collect(Collectors.toMap(BusinessInformationResultResp::getTenantId, item -> item, (v1, v2) -> v1));
        List<TenantResp> tenantDTOS = tenants.stream().map(tenant -> {
            TenantResp tenantResp = TenantMapperConvert.respToTenantDto(tenant);
            BusinessInformationResultResp businessInformationResultResp = tenantCompanyMap.get(tenant.getId());
            if (Objects.nonNull(businessInformationResultResp)) {
                tenantResp.setCompanyName(businessInformationResultResp.getCompanyName());
            }

            return tenantResp;
        }).collect(Collectors.toList());
        return tenantDTOS;
    }

    /**
     * 查询指定租户下所有的省、市、区
     * @param
     * @return
     */
    public List<String> listAddress(Long tenantId){
        DubboResponse<List<String>> response = merchantAddressQueryProvider.getConcatAddress(tenantId);
        if (!response.isSuccess()) {
            throw new BizException("查询指定租户下所有的省、市、区失败");
        }
        return response.getData();
    }

    /**
     * 根据租户列表批量查询
     *
     * @param tenantIds
     * @return
     */
    public List<BusinessInformationResultResp> batchQueryByTenantIds(List<Long> tenantIds) {
        BusinessInformationQueryReq businessInformationQueryReq = new BusinessInformationQueryReq();
        businessInformationQueryReq.setType(BusinessInformationTypeEnum.BRAND_USER_TYPE.getCode());
        businessInformationQueryReq.setBizIdList(tenantIds);
        List<BusinessInformationResultResp> businessInfos = getBusinessInfos(businessInformationQueryReq);
        if (CollectionUtil.isEmpty(businessInfos)) {
            return Collections.emptyList();
        }
        return businessInfos;
    }

    /**
     * 根据查询条件获取租户信息列表
     *
     * @param req
     * @return
     */
    public List<TenantResultResp> getTenantsByQuery(TenantQueryReq req) {
        DubboResponse<List<TenantResultResp>> response = tenantQueryProvider.getTenants(req);
        if (!response.isSuccess()) {
            throw new BizException("获取租户信息失败");
        }

        return response.getData();
    }

    /**
     * 根据指定参数查询工商信息列表
     *
     * @param queryReq
     * @return
     */
    public List<BusinessInformationResultResp> getBusinessInfos(BusinessInformationQueryReq queryReq) {
        DubboResponse<List<BusinessInformationResultResp>> response = queryProvider.getBusinessInfos(queryReq);
        if (!response.isSuccess()) {
            throw new BizException("根据指定参数查询工商信息列表失败");
        }

        return response.getData();
    }
}
