package com.cosfo.item.web;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.req.MarketItemOnsaleInput;
import com.cofso.item.client.req.MarketItemPriceInput;
import com.cofso.item.client.resp.ItemPriceDetailResp;
import com.cofso.item.client.resp.PriceDetailResp;
import com.cosfo.item.infrastructure.item.dao.MarketAreaItemDao;
import com.cosfo.item.infrastructure.item.dao.MarketItemOnsaleStrategyMappingDao;
import com.cosfo.item.infrastructure.item.model.MarketAreaItem;
import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;
import com.cosfo.item.infrastructure.price.dao.*;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.infrastructure.price.model.*;
import com.cosfo.item.web.domain.dto.CombineMarketQueryDTO;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.service.MarketCombineDomainService;
import com.cosfo.item.web.domain.service.MarketItemOnsalePriceDealService;
import com.cosfo.item.web.domain.service.PriceDomianService;
import com.cosfo.item.web.domain.vo.CombineMarketDetailVO;
import com.cosfo.item.web.domain.vo.ItemPriceDetailVO;
import com.cosfo.item.web.domain.vo.PriceDetailVO;
import com.cosfo.item.web.mq.consumer.OrderSelfItemOnsaleListener;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.strategy.CosfoCostPriceHandler;
import com.cosfo.item.web.mq.consumer.binlog.strategy.CosfoMarketItemPriceStrategyHandler;
import com.cosfo.item.web.mq.consumer.binlog.strategy.CosfoMarketCombineItemMappingHandler;
import com.cosfo.item.web.mq.consumer.binlog.strategy.CosfoProductPricingSupplyCityMappingHandler;
import com.cosfo.item.web.provider.converter.ItemPricePrividerConverter;
import com.cosfo.item.web.provider.converter.MarketConvert;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ItemPriceTest {

    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Autowired
    private MarketAreaItemMappingDao marketAreaItemMappingDao;
    @Autowired
    private MarketAreaItemStorePriceMappingDao marketAreaItemStorePriceMappingDao;
    @Autowired
    private MarketItemPriceStrategyDao marketItemPriceStrategyDao;
    @Autowired
    private MarketItemPriceStrategyMappingDao marketItemPriceStrategyMappingDao;
    @Autowired
    private MarketItemOnsaleStrategyMappingDao marketItemOnsaleStrategyMappingDao;
    @Autowired
    private MarketAreaItemDao marketAreaItemDao;
    @Autowired
    private PriceDomianService priceDomianService;
    @Autowired
    private MarketItemPriceLogDao marketItemPriceLogDao;
    @Autowired
    private CosfoMarketItemPriceStrategyHandler cosfoMarketItemPriceStrategyHandler;
    @Autowired
    private CosfoMarketCombineItemMappingHandler CosfoMarketCombineItemMappingHandler;
    @Autowired
    private CosfoProductPricingSupplyCityMappingHandler cosfoProductPricingSupplyCityMappingHandler;
    @Autowired
    private OrderSelfItemOnsaleListener orderSelfItemOnsaleListener;
    @Autowired
    private CosfoCostPriceHandler ccosfoCostPriceHandler;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketCombineDomainService marketCombineDomainService;

    @Test
    void orderSelfItemOnsaleListener() {
//        ItemChangeMessageDTO dto = new ItemChangeMessageDTO();
//        dto.setTenantId(1003L);
//        dto.setMarketItemId(2322L);
//        orderSelfItemOnsaleListener.process(dto);
        HashMap<Long, Integer> map = new HashMap<> ();
        map.put (52035L,5);
        map.put (47186L,6);
        map.put (47288L,1);
        map.put (46459L,1);
        map.put (52430L,1);
        map.put (52431L,4);
        //        ArrayList<Long> li = new ArrayList<> ();
//        li.add (51959L);
        Map<Long, PriceDetailVO> priceDetailVOMap = priceDomianService.listItemPriceDetailByItemIdsWithQuantity (24514L,4688L, 1,map,false);

//        priceDomianService.listItemPriceDetailByItemIds4CombineItem (2L, 4290L,li);
//        priceDomianService.listItemPriceDetailByItemIds4CombineItem (2L, 4290L,map);
//        CombineMarketQueryDTO q = new CombineMarketQueryDTO ();
//        q.setCombineMarketId(2040909L);
//        q.setTenantId(2L);
//        q.setPageNum(1);
//        q.setPageSize(1);
//        CombineMarketDetailVO combineMarketDetailVO = marketCombineDomainService.combineDetail (q);
//        System.out.println (priceDetailVOMap);
    }

    @Test
    void saveOrUpdateByItemIdAndTargetIdAndStrategyType() {
        List<MarketItemOnsaleStrategyMapping> list = new ArrayList<>();
        MarketItemOnsaleStrategyMapping e = new MarketItemOnsaleStrategyMapping();
        e.setTenantId(2L);
        e.setItemId(1838L);
        e.setTargetId(2L);
        e.setOnSale(1);
        e.setStrategyType(2);
        list.add(e);
        MarketItemOnsaleStrategyMapping e1 = new MarketItemOnsaleStrategyMapping();
        e1.setTenantId(2L);
        e1.setItemId(1838L);
        e1.setTargetId(1L);
        e1.setOnSale(1);
        e1.setStrategyType(2);
        list.add(e1);
        marketItemOnsaleStrategyMappingDao.saveOrUpdateByItemIdAndTargetIdAndStrategyType(list);
    }

    @Test
    void saveOrUpdateBatchMarketItemPriceAndOnsale() {
//       Map<Long, PriceDetailResp> reult = new HashMap<> ();
//       priceDetailVOMap.forEach ((key,value)-> reult.put (key, ItemPricePrividerConverter.priceDetailVO2Resp(value)));
//       ArrayList<Long> list = new ArrayList<> ();
//       list.add (2138L);
//       List<ItemPriceDetailVO> itemPriceDetailVOS = priceDomianService.listItemPriceDetailByItemIds4CombineItem (2L, 4065L,list );
//       List<ItemPriceDetailResp> c = itemPriceDetailVOS.stream ().map (ItemPricePrividerConverter::itemPriceDetailVO2Resp).collect (Collectors.toList ());
//       System.out.println (c);

//        ArrayList<Long> list = new ArrayList<>();
//        list.add(985L);
//        Map<Long, PriceDetailVO> priceDetailVOMap = priceDomianService.listItemPriceDetailByItemIds(1003L, 4321L, 1, list);
//        System.out.println(priceDetailVOMap);

//       ItemChangeMessageDTO dto = new ItemChangeMessageDTO ();
//       dto.setMarketItemId (23757L);
//       dto.setTenantId (1024L);
//       marketItemOnsalePriceDealService.saveOrUpdateBatchMarketItemPrice (dto);
    }

    @Test
    void costpriceChangeTest() {
        String mockMsg = "{\"id\":179,\"database\":\"cosfodb\",\"table\":\"cost_price\",\"pkNames\":[\"id\"],\"isDdl\":false,\"type\":\"UPDATE\",\"es\":1687327732000,\"ts\":1687327733074,\"sql\":\"\",\"sqlType\":{\"id\":-5,\"tenant_id\":-5,\"sku_id\":-5,\"sku_code\":12,\"area\":12,\"province\":12,\"city\":12,\"price\":3,\"valid_time\":93,\"invalid_time\":93,\"valid_type\":-6,\"sys_type\":-6,\"create_time\":93,\"update_time\":93},\"mysqlType\":{\"id\":\"bigint\",\"tenant_id\":\"bigint\",\"sku_id\":\"bigint\",\"sku_code\":\"varchar(50)\",\"area\":\"varchar(50)\",\"province\":\"varchar(50)\",\"city\":\"varchar(50)\",\"price\":\"decimal(10,2)\",\"valid_time\":\"datetime\",\"invalid_time\":\"datetime\",\"valid_type\":\"tinyint\",\"sys_type\":\"tinyint\",\"create_time\":\"datetime\",\"update_time\":\"datetime\"},\"data\":[{\"id\":\"2680\",\"tenant_id\":\"1003\",\"sku_id\":\"4248\",\"sku_code\":\"858225423081\",\"area\":\"余杭区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"88.88\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 13:44:30\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2681\",\"tenant_id\":\"700\",\"sku_id\":\"4248\",\"sku_code\":\"858225423081\",\"area\":\"西湖区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"88.88\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 13:44:31\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2682\",\"tenant_id\":\"700\",\"sku_id\":\"4248\",\"sku_code\":\"858225423081\",\"area\":\"拱墅区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"88.88\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 13:44:31\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2683\",\"tenant_id\":\"700\",\"sku_id\":\"4248\",\"sku_code\":\"858225423081\",\"area\":\"上城区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"88.88\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 13:44:31\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2684\",\"tenant_id\":\"700\",\"sku_id\":\"4248\",\"sku_code\":\"858225423081\",\"area\":\"余杭区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"88.88\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 13:44:31\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2685\",\"tenant_id\":\"1003\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"临安区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:21:30\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2686\",\"tenant_id\":\"1003\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"建德市\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:21:30\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2687\",\"tenant_id\":\"1003\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"西湖区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:21:30\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2688\",\"tenant_id\":\"1003\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"上城区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:21:30\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2689\",\"tenant_id\":\"1003\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"余杭区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:21:30\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2690\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"南城街道\",\"province\":\"广东省\",\"city\":\"东莞市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2691\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"栾川县\",\"province\":\"河南省\",\"city\":\"洛阳市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2692\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"鹿城区\",\"province\":\"浙江省\",\"city\":\"温州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2693\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"渑池县\",\"province\":\"河南省\",\"city\":\"三门峡市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2694\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"富阳区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2695\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"淳安县\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2696\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"上城区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2697\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"建德市\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2698\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"西湖区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2699\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"临安区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2700\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"临平区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2701\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"余杭区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2702\",\"tenant_id\":\"2\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"拱墅区\",\"province\":\"浙江省\",\"city\":\"杭州市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-01 16:26:23\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2703\",\"tenant_id\":\"2\",\"sku_id\":\"1944\",\"sku_code\":\"312470607364\",\"area\":\"顺德区\",\"province\":\"广东省\",\"city\":\"佛山市\",\"price\":\"25.83\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:11:31\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2704\",\"tenant_id\":\"1003\",\"sku_id\":\"70\",\"sku_code\":\"19335603756\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"9.80\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2705\",\"tenant_id\":\"1003\",\"sku_id\":\"1989\",\"sku_code\":\"296352351055\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"50.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2706\",\"tenant_id\":\"1003\",\"sku_id\":\"3613\",\"sku_code\":\"858225423247\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"100.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2707\",\"tenant_id\":\"1003\",\"sku_id\":\"3688\",\"sku_code\":\"858613164572\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"1000.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2708\",\"tenant_id\":\"1003\",\"sku_id\":\"4248\",\"sku_code\":\"858225423081\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"88.88\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2709\",\"tenant_id\":\"1003\",\"sku_id\":\"6498\",\"sku_code\":\"296460720581\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"100.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2710\",\"tenant_id\":\"1003\",\"sku_id\":\"7273\",\"sku_code\":\"858641441132\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"57.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2711\",\"tenant_id\":\"1003\",\"sku_id\":\"7327\",\"sku_code\":\"296874446782\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"100.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2712\",\"tenant_id\":\"1003\",\"sku_id\":\"9184\",\"sku_code\":\"970007067212\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"1.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2713\",\"tenant_id\":\"1003\",\"sku_id\":\"11791\",\"sku_code\":\"970411025372\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"1.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2714\",\"tenant_id\":\"1003\",\"sku_id\":\"11778\",\"sku_code\":\"596768581603\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"65.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:36\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2715\",\"tenant_id\":\"1003\",\"sku_id\":\"11817\",\"sku_code\":\"858355228303\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"23.78\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:37\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2716\",\"tenant_id\":\"1003\",\"sku_id\":\"16632\",\"sku_code\":\"6153867337\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"60.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:37\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2717\",\"tenant_id\":\"1003\",\"sku_id\":\"17002\",\"sku_code\":\"1052185771243\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"20.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:37\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2718\",\"tenant_id\":\"1003\",\"sku_id\":\"18263\",\"sku_code\":\"1052535102021\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"1.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:37\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2719\",\"tenant_id\":\"1003\",\"sku_id\":\"18290\",\"sku_code\":\"1052430613641\",\"area\":\"路北区\",\"province\":\"河北省\",\"city\":\"唐山市\",\"price\":\"1.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-08 14:35:37\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2720\",\"tenant_id\":\"2\",\"sku_id\":\"70\",\"sku_code\":\"19335603756\",\"area\":\"仙居县\",\"province\":\"浙江省\",\"city\":\"台州市\",\"price\":\"9.80\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-09 11:59:41\",\"update_time\":\"2023-06-21 14:08:52\"},{\"id\":\"2721\",\"tenant_id\":\"2\",\"sku_id\":\"99\",\"sku_code\":\"5027780384\",\"area\":\"仙居县\",\"province\":\"浙江省\",\"city\":\"台州市\",\"price\":\"100.0\",\"valid_time\":\"2000-12-31 00:00:00\",\"invalid_time\":\"2049-12-31 00:00:00\",\"valid_type\":\"0\",\"sys_type\":\"0\",\"create_time\":\"2023-06-09 11:59:41\",\"update_time\":\"2023-06-21 14:08:52\"}],\"old\":[{\"price\":\"97.77\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"97.77\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"97.77\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"97.77\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"97.77\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"28.41\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"10.78\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"55.0\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"110.0\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"1100.0\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"97.77\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"110.0\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"62.70\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"110.0\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"1.10\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"1.10\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"71.50\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"26.16\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"66.0\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"22.0\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"1.10\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"1.10\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"10.78\",\"update_time\":\"2023-06-21 13:43:02\"},{\"price\":\"110.0\",\"update_time\":\"2023-06-21 13:43:02\"}]}";

        String a = "{\n" +
            "    \"id\": 23383000,\n" +
            "    \"database\": \"cosfodb\",\n" +
            "    \"table\": \"cost_price\",\n" +
            "    \"pkNames\": [\n" +
            "        \"id\"\n" +
            "    ],\n" +
            "    \"isDdl\": null,\n" +
            "    \"type\": \"UPDATE\",\n" +
            "    \"es\": 1685522655000,\n" +
            "    \"ts\": null,\n" +
            "    \"sql\": null,\n" +
            "    \"sqlType\": null,\n" +
            "    \"mysqlType\": null,\n" +
            "    \"data\": [\n" +
            "        {\n" +
            "            \"tenant_id\": \"13\",\n" +
            "            \"area\": \"彭山区\",\n" +
            "            \"create_time\": \"2023-03-31 21:02:55.0\",\n" +
            "            \"city\": \"眉山市\",\n" +
            "            \"sku_id\": \"10456\",\n" +
            "            \"sys_type\": \"0\",\n" +
            "            \"update_time\": \"2023-05-31 16:44:15.0\",\n" +
            "            \"province\": \"四川省\",\n" +
            "            \"price\": \"58.00\",\n" +
            "            \"valid_type\": \"1\",\n" +
            "            \"invalid_time\": \"2024-02-29 00:00:00.0\",\n" +
            "            \"id\": \"10838\",\n" +
            "            \"valid_time\": \"2023-02-27 00:00:00.0\",\n" +
            "            \"sku_code\": \"656148758\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"old\": [\n" +
            "        {\n" +
            "            \"update_time\": \"2023-05-30 11:07:10.0\",\n" +
            "            \"price\": \"64.00\"\n" +
            "        }\n" +
            "    ]\n" +
            "}";

        DtsModelEvent dtsModelEvent = JSON.parseObject(mockMsg, DtsModelEvent.class);

        ccosfoCostPriceHandler.tableDml(dtsModelEvent);
    }

    @Test
    void initMarketItemPriceOnSaleStratary() {
        MarketItemOnsaleInput onSaleInput = JSON.parseObject("{\"mType\":0,\"onSale\":0,\"show\":1,\"strategyType\":3,\"targetId\":1001,\"tenantId\":1}",
            MarketItemOnsaleInput.class);
        MarketItemPriceInput priceInput = JSON.parseObject(
            "{\"strategyType\":2,\"strategyValue\":6.27,\"targetIds\":[1001],\"targetType\":2,\"updateTime\":\"2023-03-06T15:17:08\"}", MarketItemPriceInput.class);
        itemDomainService.initMarketItemPriceOnSaleStratary(2L, MarketConvert.INSTANCE.convert2OnsaleStrategyDTO(onSaleInput),
            MarketConvert.INSTANCE.convert2PriceStrategyDTO(priceInput));
    }

    @Test
    void saveOrUpdateBatchPriceLog() {
        MarketItemPriceLog log = new MarketItemPriceLog();
//       log.setTargetType(1);
        log.setSkuId(999L);
        log.setPrice(null);
        log.setOpsType(1);
        log.setTargetId(999L);

        log.setMarketItemId(999L);
        log.setTenantId(999L);
        log.setPriceStrategy("shoudongcceshi");
        log.setBasePrice(new BigDecimal(100));

        MarketItemPriceLog log2 = new MarketItemPriceLog();
        log2.setTargetType(1);
//       log2.setSkuId(999L);
        log2.setPrice(null);
        log2.setOpsType(1);
        log2.setTargetId(999L);

        log2.setMarketItemId(999L);
        log2.setTenantId(999L);
        log2.setPriceStrategy("shoudongcceshi");
        log2.setBasePrice(new BigDecimal(100));

        MarketItemPriceLog log3 = new MarketItemPriceLog();
        log3.setTargetType(1);
        log3.setSkuId(999L);
        log3.setPrice(null);
//       log3.setOpsType(1);
        log3.setTargetId(999L);

        log3.setMarketItemId(999L);
        log3.setTenantId(999L);
        log3.setPriceStrategy("shoudongcceshi");
        log3.setBasePrice(new BigDecimal(100));
        List<MarketItemPriceLog> marketItemPriceLogs = Arrays.asList(log, log2, log3);
        marketItemPriceLogDao.batchInsert(marketItemPriceLogs);
        System.out.println(1);
    }

    @Test
    void binlog() {

        String a = "{\"id\":53771,\"database\":\"cosfodb\",\"table\":\"product_pricing_supply_city_mapping\",\"pkNames\":[\"id\"],\"isDdl\":false,\"type\":\"INSERT\",\"es\":1684734903000,\"ts\":1684734903477,\"sql\":\"\",\"sqlType\":{\"id\":-5,\"product_pricing_supply_id\":-5,\"city_id\":-5,\"type\":-6,\"supply_type\":-6,\"price\":3,\"start_time\":93,\"end_time\":93,\"create_time\":93,\"update_time\":93,\"deleted\":-6},\"mysqlType\":{\"id\":\"bigint unsigned\",\"product_pricing_supply_id\":\"bigint\",\"city_id\":\"bigint\",\"type\":\"tinyint\",\"supply_type\":\"tinyint\",\"price\":\"decimal(10,2)\",\"start_time\":\"datetime\",\"end_time\":\"datetime\",\"create_time\":\"datetime\",\"update_time\":\"datetime\",\"deleted\":\"tinyint\"},\"data\":[{\"id\":\"2034339\",\"product_pricing_supply_id\":\"1092\",\"city_id\":\"330100\",\"type\":\"0\",\"supply_type\":\"1\",\"price\":\"300.0\",\"start_time\":\"2023-05-23 00:00:00\",\"end_time\":\"2023-05-31 00:00:00\",\"create_time\":\"2023-05-22 13:55:03\",\"update_time\":null,\"deleted\":\"0\"}],\"old\":null}";

        DtsModelEvent parse = JSON.parseObject(a, DtsModelEvent.class);
        cosfoProductPricingSupplyCityMappingHandler.tableDml(parse);
    }

    @Test
    void InitItemPriceStrategyProceessor() {
        MarketAreaItemMapping e = marketAreaItemMappingDao.getById(40L);

        MarketItemPriceStrategy marketItemPriceStrategy = new MarketItemPriceStrategy();
        marketItemPriceStrategy.setTenantId(e.getTenantId());
        Long areaItemId = e.getAreaItemId();
        MarketAreaItem byId = marketAreaItemDao.getById(areaItemId);
        marketItemPriceStrategy.setItemId(byId.getItemId());
        marketItemPriceStrategy.setStrategyType(e.getType());
        marketItemPriceStrategy.setStrategyValue(e.getMappingNumber());
        marketItemPriceStrategy.setTargetType(e.getStorePriceType());
        marketItemPriceStrategyDao.save(marketItemPriceStrategy);
        if (e.getStorePriceType() == 1) {
            List<MarketAreaItemStorePriceMapping> mapping = marketAreaItemStorePriceMappingDao.getByPId(e.getId());
            List<MarketItemPriceStrategyMapping> collect = mapping.stream().map(m -> {
                MarketItemPriceStrategyMapping ma = new MarketItemPriceStrategyMapping();
                ma.setTenantId(m.getTenantId());
                ma.setItemPriceStrategyId(marketItemPriceStrategy.getId());
                ma.setTargetId(m.getStoreId());
                return ma;
            }).collect(Collectors.toList());
            marketItemPriceStrategyMappingDao.saveBatch(collect);
        } else {
            MarketItemPriceStrategyMapping ma = new MarketItemPriceStrategyMapping();
            ma.setTenantId(e.getTenantId());
            ma.setItemPriceStrategyId(marketItemPriceStrategy.getId());
            ma.setTargetId(e.getTenantId());
            marketItemPriceStrategyMappingDao.save(ma);
        }
    }
}
