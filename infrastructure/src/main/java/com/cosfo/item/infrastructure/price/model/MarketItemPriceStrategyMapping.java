package com.cosfo.item.infrastructure.price.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品item价格策略映射
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketItemPriceStrategyMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户Id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 商品item价格策略id
     */
    private Long itemPriceStrategyId;

    /**
     * 报价目标Id
     */
    private Long targetId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;


}
