package com.cosfo.item.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Splitter;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class AddressService {
    public static Set<String> filterAddress(List<String> addresses,Set<String> areasXMca,Set<String> areasXMc){
        if (CollectionUtil.isEmpty (addresses)) {
            return Collections.emptySet ();
        }
        Set<String> addressResult = new HashSet<> ();
        for(String  address :addresses ){
            List<String> pcaList = Splitter.on ("-").splitToStream (address).collect (Collectors.toList ());
            String ca = pcaList.get (1) + "-" +pcaList.get (2);
            if(areasXMca.contains (ca)) {
                addressResult.add (address);
            }else{
                String c = pcaList.get (1);
                if(areasXMc.contains (c)) {
                    addressResult.add (address);
                }
            }
        }
        return addressResult;
    }
}
