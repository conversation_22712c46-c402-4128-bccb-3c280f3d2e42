package com.cosfo.item.infrastructure.price.dao.impl;

import com.cosfo.item.infrastructure.price.model.MarketItemPriceLog;
import com.cosfo.item.infrastructure.price.mapper.MarketItemPriceLogMapper;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceLogDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Iterators;
import java.util.Collection;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商品价格变化记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class MarketItemPriceLogDaoImpl extends ServiceImpl<MarketItemPriceLogMapper, MarketItemPriceLog> implements MarketItemPriceLogDao {

    @Override
    public void batchInsert(Collection<MarketItemPriceLog> priceLogCollection) {
        if (CollectionUtils.isEmpty(priceLogCollection)) {
            return;
        }
        Iterators.partition(priceLogCollection.iterator(), DEFAULT_BATCH_SIZE).forEachRemaining(subCollection -> {
            getBaseMapper().batchInsert(subCollection);
        });
    }
}
