package com.cosfo.item.web.provider;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.*;
import com.cofso.page.PageResp;
import com.cosfo.item.web.domain.dto.MarketItemCommonQueryDTO;
import com.cosfo.item.web.domain.dto.MarketPageQueryDTO;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.service.MarketDomainService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.provider.converter.MarketConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 13:50
 * @Description:
 */
@DubboService
@Slf4j
public class MarketProviderImpl implements MarketProvider {
    @Resource
    private MarketDomainService marketDomainService;
    @Resource
    private ItemDomainService itemDomainService;

    /**
     * market page
     * 包含item 详情
     * @param req
     * @return
     */
    @Override
    public DubboResponse<PageResp<MarketResp>> queryMarketListPage(MarketQueryReq req) {
        MarketPageQueryDTO queryDto = MarketConvert.INSTANCE.convert2QueryDto(req);
        Page<MarketPageInfoVO> pageInfo = marketDomainService.queryMarketListPage(queryDto);
        if (Objects.isNull(pageInfo)) {
            return DubboResponse.getOK(PageResp.emptyPage(queryDto.getPageNum(), queryDto.getPageSize()));
        } else {
            return DubboResponse.getOK(PageResp.toPageList(MarketConvert.INSTANCE.covert2MarketResps(pageInfo.getRecords()),
                Math.toIntExact(pageInfo.getTotal()), queryDto.getPageNum(), queryDto.getPageSize()));
        }
    }

    /**
     * item page
     * @param req
     * @return
     */
    @Override
    @Deprecated
    public DubboResponse<PageResp<MarketItemInfoResp>> queryMarketItemList(MarketItemCommonQueryReq req) {
        MarketItemCommonQueryDTO queryDTO = MarketConvert.INSTANCE.convert2QueryDto(req);
        MarketItemInfoQueryFlagReq marketItemInfoQueryFlagReq = new MarketItemInfoQueryFlagReq ();
        marketItemInfoQueryFlagReq.setClassificationIdFlag(false);
        marketItemInfoQueryFlagReq.setPriceRangeFlag(true);
        marketItemInfoQueryFlagReq.setStockFlag(true);
        marketItemInfoQueryFlagReq.setCategoryIdFlag(true);

        Page<MarketItemVO> pageInfo = itemDomainService.queryMarketItemList(queryDTO);
        return DubboResponse.getOK(PageResp.toPageList(MarketConvert.INSTANCE.convert2Items(pageInfo.getRecords()),
                Math.toIntExact(pageInfo.getTotal()), queryDTO.getPageNum(), queryDTO.getPageSize()));
    }

    /**
     * 根据market id 查询market，不含item
     * 这里返回的MarketDetailResp应该是MarketInfoResp类型。
     * @param req
     * @return
     */
    @Override
    public DubboResponse<MarketDetailResp> getMarketDetail(MarketDetailQueryReq req) {
        MarketInfoVO marketDetail = marketDomainService.getMarketDetailById (req.getTenantId (),req.getMarketId ());
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2MarketDetail(marketDetail));
    }

    /**
     * 根据xm pid  查询market，不含item
     * @param tenantId
     * @param outId
     * @return
     */
    @Override
    public DubboResponse<MarketInfoResp> getMarketInfoByOutId(Long tenantId,Long outId){
        MarketInfoVO marketInfo = marketDomainService.getMarketInfoByOutId(tenantId,outId);
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2MarketInfo(marketInfo));
    }

    /**
     * 根据xm pids  查询market，不含item
     * @param tenantId
     * @param outIds
     * @return
     */
    @Override
    public DubboResponse<List<MarketInfoResp>> listMarketInfoByOutIds(Long tenantId, List<Long> outIds) {
        List<MarketInfoVO> marketInfos = marketDomainService.listMarketInfoByOutIds(tenantId,outIds);
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2MarketInfoList(marketInfos));
    }









    /**
     * 根据 itemCodecost询item 所有信息
     * @param tenantId
     * @param itemCode
     * @return
     */
    @Override
    public DubboResponse<MarketItemInfoResp> getMarketItemDetailByItemCode(Long tenantId, String itemCode) {
        MarketItemVO marketItemDetail = itemDomainService.getMarketItemDetailByItemCode (tenantId, itemCode);
        log.info ("tenantId={}, itemCode={},it={}",tenantId, itemCode,JSON.toJSONString (marketItemDetail));
        MarketItemInfoResp marketItemInfoResp = MarketConvert.INSTANCE.convert2Item (marketItemDetail);
        log.info ("tenantId={}, itemCode={},it={}",tenantId, itemCode, JSON.toJSONString (marketItemInfoResp));
        return DubboResponse.getOK(marketItemInfoResp);
    }

    /**
     * 根据 itemid 查询item 所有信息
     * @param req
     * @return
     */
    @Override
    public DubboResponse<MarketItemInfoResp> getMarketItemDetail(MarketDetailQueryReq req) {
        MarketItemVO marketItemDetail = itemDomainService.getMarketItemDetailById (req.getTenantId (),req.getMarketItemId ());
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Item(marketItemDetail));
    }
    /**
     * 根据 itemCode 查询商品item大概信息 - 只有marketitem表的信息
     * @param tenantId
     * @param itemCode
     * @return
     */
    @Override
    public DubboResponse<MarketItemInfoResp> getMarketItemInfoByItemCode(Long tenantId, String itemCode) {
        MarketItemVO itemVO = itemDomainService.getMarketItemInfoByItemCode(tenantId,itemCode);
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Item(itemVO));
    }
    /**
     * 根据 itemCodes 查询商品item大概信息 - 只有marketitem表的信息
     * @param tenantId
     * @return
     */
    @Override
    public DubboResponse<List<MarketItemInfoResp>> listMarketItemInfoByItemCodes(Long tenantId, List<String>itemCodes) {
        List<MarketItemVO> itemVOs = itemDomainService.listMarketItemInfoByItemCodes(tenantId,itemCodes);
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Items(itemVOs));
    }











    @Override
    public DubboResponse<PageResp<MarketItemInfoResp>> listItemDetailByItemCodes(MarketItemDetail4XmQueryReq queryReq) {
        Page<MarketItemVO> pageInfo = itemDomainService.listItemDetailByItemCodes(MarketConvert.INSTANCE.convert2Dto(queryReq));
        if (Objects.isNull(pageInfo)) {
            return DubboResponse.getOK(PageResp.emptyPage(queryReq.getPageNum(), queryReq.getPageSize()));
        } else {
            return DubboResponse.getOK(PageResp.toPageList(MarketConvert.INSTANCE.convert2Items(pageInfo.getRecords()),
                    Math.toIntExact(pageInfo.getTotal()), queryReq.getPageNum(), queryReq.getPageSize()));
        }
    }



    @Override
    public DubboResponse<AddMarketResp> addMarket(MarketInputReq req) {
        AddMarketVO addMarketVO = marketDomainService.addMarket(MarketConvert.INSTANCE.convert2MarketDTO(req));
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Resp(addMarketVO));
    }

    @Override
    public DubboResponse<Boolean> updateMarket(MarketInputReq req) {
        marketDomainService.updateMarket(MarketConvert.INSTANCE.convert2MarketDTO(req));
        return DubboResponse.getOK(Boolean.TRUE);
    }


    @Override
    public DubboResponse<Long> addMarketItem(MarketItemInputReq req) {
        Long itemId = itemDomainService.saveItem(MarketConvert.INSTANCE.convert2MarketItemDTO(req));
        return DubboResponse.getOK(itemId);
    }

    @Override
    public DubboResponse<Boolean> updateMarketItem(MarketItemInputReq req) {
        itemDomainService.updateMarketItem(MarketConvert.INSTANCE.convert2MarketItemDTO(req));
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> initMarketItem(MarketItemInputReq marketItemInputReq) {
        itemDomainService.initMarketItem(MarketConvert.INSTANCE.convert2MarketItemDTO(marketItemInputReq));
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> initMarketItemPriceOnSaleStratary(Long outId,MarketItemOnsaleInput onSaleInput,MarketItemPriceInput priceInput) {
        itemDomainService.initMarketItemPriceOnSaleStratary(outId,MarketConvert.INSTANCE.convert2OnsaleStrategyDTO(onSaleInput),MarketConvert.INSTANCE.convert2PriceStrategyDTO(priceInput));
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> deleteMarket(MarketDeleteReq req) {
        marketDomainService.deleteMarket(MarketConvert.INSTANCE.convert2DeleteDto(req));
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> deleteMarketItem(MarketDeleteReq req) {
        itemDomainService.deleteMarketItem(MarketConvert.INSTANCE.convert2DeleteDto(req));
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<Boolean> changeOnSale(MarketItemOnSaleInputReq req) {
        itemDomainService.changeOnSale(MarketConvert.INSTANCE.convert2Dto(req));
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<BatchOnSaleResultResp> batchChangOnSale(MarketItemOnSaleInputReq req) {
        BatchOnSaleResultVO resultVO = itemDomainService.batchChangOnSale(MarketConvert.INSTANCE.convert2Dto(req));
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Resp(resultVO));
    }

    @Override
    public DubboResponse<BatchOnSaleResultResp> batchChangOnSaleIncludeAllStatus(MarketItemOnSaleInputReq soldOut, MarketItemOnSaleInputReq onSale) {
        BatchOnSaleResultVO resultVO = itemDomainService.batchChangOnSaleIncludeAllStatus(MarketConvert.INSTANCE.convert2Dto(soldOut),MarketConvert.INSTANCE.convert2Dto(onSale));
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Resp(resultVO));
    }

    @Override
    public DubboResponse<MarketItemInfoResp> queryItemPriceStrategy(MarketDetailQueryReq req) {
        MarketItemVO itemVO = itemDomainService.queryItemPriceStrategy(req.getMarketItemId (),req.getTenantId ());
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Item(itemVO));
    }

    @Override
    public DubboResponse<Boolean> updatePriceStrategy(MarketItemPriceStrategyReq req) {
        itemDomainService.updatePriceStrategy(req,true);
        return DubboResponse.getOK(Boolean.TRUE);
    }
    @Override
    @Deprecated
    public DubboResponse<List<MarketItemPriceStrategyUpdateResultResp>> batchUpdatePriceStrategy(List<MarketItemPriceStrategyReq> reqs) {
        List<MarketItemPriceStrategyUpdateResultVO> vos = itemDomainService.batchUpdatePriceStrategy (reqs);
        return DubboResponse.getOK(MarketConvert.INSTANCE.marketItemPriceStrategyUpdateResultVO2respList(vos));
    }
    @Override
    public DubboResponse<List<MarketItemPriceStrategyUpdateResultResp>> batchUpdateTenantTargetTypePriceStrategy(List<MarketItemPriceStrategyReq> reqs) {
        List<MarketItemPriceStrategyUpdateResultVO> vos = itemDomainService.batchUpdateTenantTargetTypePriceStrategy (reqs);
        return DubboResponse.getOK(MarketConvert.INSTANCE.marketItemPriceStrategyUpdateResultVO2respList(vos));
    }

    @Override
    public DubboResponse<List<MarketItemInfoResp>> queryMarketItemInfoBySkuIds(Long tenantId, Collection<Long> skuIds) {
        List<MarketItemVO> itemVOs = itemDomainService.listMarketItemInfoBySkuIds(tenantId,skuIds);
        return DubboResponse.getOK(MarketConvert.INSTANCE.convert2Items(itemVOs));
    }

    @Override
    public DubboResponse<PageResp<MarketItemInfoResp>> querySimpleMarketItemList(MarketItemCommonQueryReq req) {
        MarketItemCommonQueryDTO queryDTO = MarketConvert.INSTANCE.convert2QueryDto(req);
        Page<MarketItemVO> pageInfo = itemDomainService.querySimpleMarketItemList(queryDTO);
        return DubboResponse.getOK(PageResp.toPageList(MarketConvert.INSTANCE.convert2Items(pageInfo.getRecords()),
                Math.toIntExact(pageInfo.getTotal()), queryDTO.getPageNum(), queryDTO.getPageSize()));
    }
    @Override
    public DubboResponse<Set<String>> queryBrandNameByTenantId(Long tenantId) {
        if(tenantId == null){
            return DubboResponse.getOK (Collections.emptySet ());
        }
        return DubboResponse.getOK (marketDomainService.queryBrandNameByTenantId(tenantId));
    }

}
