package com.cosfo.item.web.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PriceStrategyRangeVO {
    /**
     * 按成本价百分比上浮  最小值
     */
    private BigDecimal costPriceAddPercentageMin;
    /**
     * 按成本价百分比上浮  最大值
     */
    private BigDecimal costPriceAddPercentageMax;
    /**
     * 按成本价定额上浮   最小值
     */
    private BigDecimal costPriceAddFixedMin;
    /**
     * 按成本价定额上浮   最大值
     */
    private BigDecimal costPriceAddFixedMax;
    /**
     * 固定价   最小值
     */
    private BigDecimal assignMin;
    /**
     * 固定价   最大值
     */
    private BigDecimal assignMax;
}
