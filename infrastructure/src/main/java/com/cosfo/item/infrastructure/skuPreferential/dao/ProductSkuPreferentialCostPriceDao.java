package com.cosfo.item.infrastructure.skuPreferential.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.common.skuPreferential.dto.ProductSkuPreferentialBasicDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPrice;
import com.cosfo.item.infrastructure.skuPreferential.param.ProductSkuPreferentialQueryParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 省心定报价 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
public interface ProductSkuPreferentialCostPriceDao extends IService<ProductSkuPreferentialCostPrice> {

    Page<ProductSkuPreferentialCostPrice> pagePreferentialCostPriceByParam(ProductSkuPreferentialQueryParam param);

    List<ProductSkuPreferentialCostPrice> listPreferentialCostPriceByParam(ProductSkuPreferentialQueryParam param);

    ProductSkuPreferentialBasicDTO queryBasicData(Long tenantId, List<Long> skuIds, Boolean availableFlag, Boolean waitValid);

    Boolean updateDeletedStatus(List<Long> productSkuPreferentialIds, Long tenantId, Long skuId);

    Set<Long> queryAvailableSkuIdByTenantId(Long tenantId);

    List<ProductPricingMessageDTO> selectFutureEndTime();

    List<ProductPricingMessageDTO> selectFutureStartTime();

    Integer optAvailableQuantity(Long id, Integer optQuantity);
}
