<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.classification.mapper.MarketClassificationMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.item.infrastructure.classification.model.MarketClassification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, `name`, icon, parent_id, sort, create_time, update_time
  </sql>
  <select id="selectMaxSort" resultType="java.lang.Integer">
    select ifnull(max(sort), 0)
    from market_classification
    where parent_id = #{parentId}
    and tenant_id = #{tenantId}
  </select>
</mapper>
