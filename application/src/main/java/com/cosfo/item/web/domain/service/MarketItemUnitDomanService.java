package com.cosfo.item.web.domain.service;


import com.cosfo.item.infrastructure.item.dao.MarketItemUnitDao;
import com.cosfo.item.infrastructure.item.model.MarketItemUnit;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategyMapping;
import com.cosfo.item.web.domain.converter.MarketItemPriceStrategyConverter;
import com.cosfo.item.web.domain.converter.MarketItemUnitConvert;
import com.cosfo.item.web.domain.dto.MarketItemUnitDTO;
import com.cosfo.item.web.domain.vo.MarketItemUnitVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MarketItemUnitDomanService {

    @Autowired
    private MarketItemUnitDao marketItemUnitDao;

    public Map<Long, List<MarketItemUnitVO>> getUnitMap(List<Long> itemIds, Long tenantId) {
        List<MarketItemUnit> list = marketItemUnitDao.listByItemIds(itemIds,tenantId);
        return MarketItemUnitConvert.instance.MarketItemUnit2VOList(list).stream ().collect (Collectors.groupingBy (MarketItemUnitVO::getItemId));
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateMarketItemUnitByItemId(Long tenantId,Long itemId, List<MarketItemUnitDTO> marketItemUnitList) {
        List<MarketItemUnit> list = MarketItemUnitConvert.instance.dto2VOEntity(marketItemUnitList);
        if(CollectionUtils.isNotEmpty(list)) {
            marketItemUnitDao.removeByItemId (tenantId,itemId);
        }
        //2、新增
        list.forEach (e->{
            e.setTenantId (tenantId);
            e.setItemId (itemId);
        });
        marketItemUnitDao.saveBatch (list);
    }
}
