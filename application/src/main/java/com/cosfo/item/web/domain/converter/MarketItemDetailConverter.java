package com.cosfo.item.web.domain.converter;

import com.alibaba.fastjson.JSON;
import com.cosfo.item.infrastructure.item.dto.MarketItemLabelQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import com.cosfo.item.infrastructure.item.model.MarketItemLabel;
import com.cosfo.item.web.domain.dto.MarketItemLabelDTO;
import com.cosfo.item.web.domain.dto.MarketItemLabelQueryDTO;
import com.cosfo.item.web.domain.vo.MarketItemDetailVO;
import com.cosfo.item.web.domain.vo.MarketItemLabelVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName MarketItemDetailConverter
 * @Description
 * <AUTHOR>
 * @Date 17:04 2024/4/28
 * @Version 1.0
 **/
public class MarketItemDetailConverter {


    public static List<MarketItemDetailVO> marketItemDetailsToVO(List<MarketItemDetail> marketItemDetails) {
        List<MarketItemDetailVO> marketItemDetailVOS = new ArrayList<>(marketItemDetails.size());
        marketItemDetails.forEach(marketItemDetail -> {
            MarketItemDetailVO marketItemDetailVO = new MarketItemDetailVO();
            marketItemDetailVO.setOutId(marketItemDetail.getOutId());
            marketItemDetailVO.setItemLabel(marketItemDetail.getItemLabel());
            marketItemDetailVOS.add(marketItemDetailVO);
        });
        return marketItemDetailVOS;
    }

    public static MarketItemLabelQueryParam convertMarketItemLabel2Param(MarketItemLabelQueryDTO marketItemLabelQueryDTO) {
        MarketItemLabelQueryParam param = new MarketItemLabelQueryParam();
        param.setLabelName(marketItemLabelQueryDTO.getLabelName());
        param.setTenantId(marketItemLabelQueryDTO.getTenantId());
        return param;
    }

    public static List<MarketItemLabelVO> convert2MarketItemLabelVOList(List<MarketItemLabel> marketItemLabels) {
        List<MarketItemLabelVO> marketItemLabelVOS = new ArrayList<>(marketItemLabels.size());
        marketItemLabels.forEach(marketItemLabel -> marketItemLabelVOS.add(convert2MarketItemLabelVO(marketItemLabel)));
        return marketItemLabelVOS;
    }

    public static MarketItemLabelVO convert2MarketItemLabelVO(MarketItemLabel marketItemLabel) {
        MarketItemLabelVO marketItemLabelVO = new MarketItemLabelVO();
        marketItemLabelVO.setId(marketItemLabel.getId());
        marketItemLabelVO.setLabelName(marketItemLabel.getLabelName());
        marketItemLabelVO.setLabelStatus(marketItemLabel.getLabelStatus());
        marketItemLabelVO.setTenantId(marketItemLabel.getTenantId());
        return marketItemLabelVO;
    }
}
