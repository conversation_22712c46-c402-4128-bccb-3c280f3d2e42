package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.item.infrastructure.item.dao.MarketAreaItemDao;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketAreaItem;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dao.MarketAreaItemMappingDao;
import com.cosfo.item.infrastructure.price.dao.MarketAreaItemStorePriceMappingDao;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyDao;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyMappingDao;
import com.cosfo.item.infrastructure.price.model.MarketAreaItemMapping;
import com.cosfo.item.infrastructure.price.model.MarketAreaItemStorePriceMapping;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategyMapping;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class InitItemPriceStrategyProceessor extends XianMuJavaProcessorV2 {
    @Autowired
    private MarketAreaItemMappingDao marketAreaItemMappingDao;
    @Autowired
    private MarketAreaItemStorePriceMappingDao marketAreaItemStorePriceMappingDao;
    @Autowired
    private MarketItemPriceStrategyDao marketItemPriceStrategyDao;
    @Autowired
    private MarketItemPriceStrategyMappingDao marketItemPriceStrategyMappingDao;
    @Autowired
    private MarketAreaItemDao marketAreaItemDao;

    @Autowired
    private MarketItemDao marketItemDao;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始进行InitItemPriceStrategyProceessor任务,instanceParameters={}",JSON.toJSONString (context.getInstanceParameters()));
        List<MarketAreaItemMapping> list = null;
        if(ObjectUtil.isNull (context) || ObjectUtil.isNull (context.getInstanceParameters ())){
            return new ProcessResult(true);
        }else {
            String con = context.getInstanceParameters ();
            //all数据初始化 全部数据
            if(con.equals ("all")){
                list = marketAreaItemMappingDao.list ();
            }else{
                String timeRegex = "^((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29))\\s+([0-1]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";
                if(!Pattern.matches (timeRegex, con)) {
                    log.error ("InitItemPriceStrategyProceessor 参数格式不正确,con={}",con);
                    return new ProcessResult(true);
                }
                List<MarketItem> marketItems = marketItemDao.listByUpdateTime (con);
                if(CollectionUtil.isNotEmpty (marketItems)){
                    List<Long> marketItemIds = marketItems.stream ().map (MarketItem::getId).collect (Collectors.toList ());

                    List<MarketItemPriceStrategy> marketItemPriceStrategies = marketItemPriceStrategyDao.listByItemIds (null,marketItemIds);
                    if(CollectionUtil.isNotEmpty (marketItemPriceStrategies)){
                        List<Long> marketItemPriceStrategieIds = marketItemPriceStrategies.stream ().map (MarketItemPriceStrategy::getId).collect (Collectors.toList ());
                        List<MarketItemPriceStrategyMapping> marketItemPriceStrategyMappings = marketItemPriceStrategyMappingDao.listByStrategyIds (marketItemPriceStrategieIds);
                        marketItemPriceStrategyDao.removeByIds (marketItemPriceStrategieIds);
                        marketItemPriceStrategyMappingDao.removeByIds (marketItemPriceStrategyMappings.stream ().map (MarketItemPriceStrategyMapping::getId).collect (Collectors.toList ()));
                    }

                    Set<Long> areaItemIds = marketAreaItemDao.listIdsByItemIds (marketItemIds);
                    list = marketAreaItemMappingDao.listByAreaItemIds (areaItemIds);
                }
            }
            if(CollectionUtil.isNotEmpty (list)){
                for (MarketAreaItemMapping e :list) {
                    try {
                        MarketItemPriceStrategy marketItemPriceStrategy = new MarketItemPriceStrategy ();
                        marketItemPriceStrategy.setTenantId (e.getTenantId ());
                        Long areaItemId = e.getAreaItemId ();
                        MarketAreaItem byId = marketAreaItemDao.getById (areaItemId);
                        marketItemPriceStrategy.setItemId (byId.getItemId ());
                        marketItemPriceStrategy.setStrategyType (e.getType ());
                        marketItemPriceStrategy.setStrategyValue (e.getMappingNumber ());
                        marketItemPriceStrategy.setTargetType (e.getStorePriceType ());
                        marketItemPriceStrategyDao.save (marketItemPriceStrategy);
                        if(e.getStorePriceType () == 1) {
                            List<MarketAreaItemStorePriceMapping> mapping = marketAreaItemStorePriceMappingDao.getByPId (e.getId ());
                            List<MarketItemPriceStrategyMapping> collect = mapping.stream ().map (m -> {
                                MarketItemPriceStrategyMapping ma = new MarketItemPriceStrategyMapping ();
                                ma.setTenantId (m.getTenantId ());
                                ma.setItemPriceStrategyId (marketItemPriceStrategy.getId ());
                                ma.setTargetId (m.getStoreId ());
                                return ma;
                            }).collect (Collectors.toList ());
                            marketItemPriceStrategyMappingDao.saveBatch (collect);
                        }else{
                            MarketItemPriceStrategyMapping ma = new MarketItemPriceStrategyMapping ();
                            ma.setTenantId (e.getTenantId ());
                            ma.setItemPriceStrategyId (marketItemPriceStrategy.getId ());
                            ma.setTargetId (e.getTenantId ());
                            marketItemPriceStrategyMappingDao.save (ma);
                        }
                    }catch (Exception exception){
                        log.error ("InitItemPriceStrategyProceessor error,MarketAreaItemMapping={}" , JSON.toJSONString (e),exception);
                    }
                }
            }
        }
        return new ProcessResult(true);
    }

}
