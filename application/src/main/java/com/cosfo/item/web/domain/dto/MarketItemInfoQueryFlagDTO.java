package com.cosfo.item.web.domain.dto;

import lombok.Data;

@Data
public class MarketItemInfoQueryFlagDTO {

    //是否返回 商品前台分类
    private boolean classificationIdFlag = false;

    //是否返回 售价区间
    private boolean priceRangeFlag = false;

    //是否返回 价格策略
//    private Boolean priceStrategyFlag;

    //是否返回 库存
    private boolean stockFlag = false;

    //是否返回 倒挂策略
    private boolean categoryIdFlag = false;

    //是否返回 单位
    private boolean unitFlag = false;

    //是否返回 倒挂策略
//    private Boolean unfairPriceStrategyFlag;
}
