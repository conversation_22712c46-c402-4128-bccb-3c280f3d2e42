package com.cosfo.item.infrastructure.item.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品上下架表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketItemOnsaleStrategyMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     * UNIQUE KEY `idx_tenantid_itemid_targetid_strategytype`
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 是否大客户专享
     */
    private Integer mType;

    /**
     * 是否展示
     */
    private Integer showFlag;

    /**
     * 商品id
     * UNIQUE KEY `idx_tenantid_itemid_targetid_strategytype`
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long itemId;

    /**
     * 上下架目标id
     * UNIQUE KEY `idx_tenantid_itemid_targetid_strategytype`
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long targetId;

    /**
     * 0下架 1上架
     */
    private Integer onSale;

    /**
     * 上架目标类型 1按租户,2 按门店 3单店 4大客户
     * UNIQUE KEY `idx_tenantid_itemid_targetid_strategytype`
     */
    private Integer strategyType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
