package com.cosfo.item.infrastructure.item.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品扩展表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * marketid
     */
    private Long marketId;

    /**
     * 从截单开始的售后时间
     */
    private Integer afterSaleTime;

    /**
     * 售后原因
     */
    private String afterSaleReason;

    /**
     * 退款原因,拍多/拍错/不想要
     */
    private String refundReason;

    /**
     * 描述
     */
    private String description;

    /**
     * 广告语
     */
    private String slogan;

    /**
     * 外部id
     */
    private Long outId;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;



}
