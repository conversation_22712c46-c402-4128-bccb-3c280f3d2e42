package com.cosfo.item.web.domain.converter;

import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.vo.MarketItemVO;

public class ItemConverter {
    public static MarketItemVO marketItem2VO(MarketItem e) {
        MarketItemVO vo = new MarketItemVO();
        vo.setId(e.getId());
        vo.setTenantId(e.getTenantId());
        vo.setSkuId(e.getSkuId());
        vo.setTitle(e.getTitle());
        vo.setSubTitle(e.getSubTitle());
        vo.setOrigin(e.getOrigin());
        vo.setMainPicture(e.getMainPicture());
        vo.setDetailPicture(e.getDetailPicture());
        vo.setSpecification(e.getSpecification());
        vo.setSpecificationUnit(e.getSpecificationUnit());
        vo.setMarketId(e.getMarketId());
        vo.setBrandId(e.getBrandId());
        vo.setBrandName(e.getBrandName());
        vo.setSupplierName(e.getSupplierName());
        vo.setItemCode(e.getItemCode());
        vo.setWeightNotes(e.getWeightNotes());
        vo.setSupplierId(e.getSupplierId());
        vo.setMaxAfterSaleAmount(e.getMaxAfterSaleAmount());
        vo.setAfterSaleUnit(e.getAfterSaleUnit());
        vo.setNoGoodsSupplyPrice(e.getNoGoodsSupplyPrice ());
        vo.setGoodsType(e.getGoodsType ());
        vo.setItemType(e.getItemType ());
        vo.setOnSale(e.getOnSale ());
        return vo;
    }
    public static ItemChangeMessageDTO marketItemVO2MsgDTO(MarketItemVO e) {
        ItemChangeMessageDTO dto = new ItemChangeMessageDTO();
        dto.setMarketItemId (e.getId ());
        dto.setNoGoodsSupplyPrice(e.getNoGoodsSupplyPrice ());
        dto.setTenantId(e.getTenantId ());
        dto.setSkuId(e.getSkuId ());
        dto.setGoodsType(e.getGoodsType ());
        dto.setItemType(e.getItemType ());
        dto.setOnSale(e.getOnSale ());
        return dto;
    }

    public static ItemChangeMessageDTO marketItem2MsgDTO(MarketItem e) {
        ItemChangeMessageDTO dto = new ItemChangeMessageDTO();
        dto.setMarketItemId (e.getId ());
        dto.setNoGoodsSupplyPrice(e.getNoGoodsSupplyPrice ());
        dto.setTenantId(e.getTenantId ());
        dto.setSkuId(e.getSkuId ());
        dto.setGoodsType(e.getGoodsType ());
        dto.setItemType(e.getItemType ());
        dto.setOnSale(e.getOnSale ());
        return dto;
    }
    public static MarketItem itemChangeMessageDTO2marketItem(ItemChangeMessageDTO e) {
        MarketItem marketItem = new MarketItem();
        marketItem.setId (e.getMarketItemId ());
        marketItem.setNoGoodsSupplyPrice(e.getNoGoodsSupplyPrice ());
        marketItem.setTenantId(e.getTenantId ());
        marketItem.setSkuId(e.getSkuId ());
        marketItem.setGoodsType(e.getGoodsType ());
        marketItem.setItemType(e.getItemType ());
        marketItem.setOnSale(e.getOnSale ());
        return marketItem;
    }
}
