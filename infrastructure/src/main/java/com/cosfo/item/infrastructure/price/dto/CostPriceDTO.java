package com.cosfo.item.infrastructure.price.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CostPriceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 货品主键
     */
    private Long skuId;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 区域
     */
    private String area;

    /**
     * 城市
     */
    private String city;

    /**
     * 省
     */
    private String province;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 生效时间
     */
    private LocalDateTime validTime;

    /**
     * 失效时间
     */
    private LocalDateTime invalidTime;

    /**
     * 生效类型,0-永久,1-周期
     */
    private Integer validType;

    /**
     * 价格来源
     */
    private Integer sysType;
}
