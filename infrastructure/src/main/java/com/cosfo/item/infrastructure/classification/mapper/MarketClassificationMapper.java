package com.cosfo.item.infrastructure.classification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cosfo.item.infrastructure.classification.model.MarketClassification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商品分类表(MarketClassification)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-04 16:37:52
 */
@Mapper
public interface MarketClassificationMapper extends BaseMapper<MarketClassification>{

    /**
     * 查询该一级分类的子类目个数
     * @param parentId
     * @return
     */
    Integer selectMaxSort(@Param("parentId") Long parentId,@Param("tenantId") Long tenantId);
}

