<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.item.mapper.MarketItemMapper">

    <select id="queryMarketItemOnSaleInfo"
            resultType="com.cosfo.item.infrastructure.item.dto.MarketItemOnSaleSimpleDTO">
        select distinct sku_id skuId, on_sale onSale
        from
        market_item
        <where>
            delete_flag = 1
            and sku_id in
            <foreach collection="skuIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            <if test="onSale != null">
                and on_sale = #{onSale}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="queryOnSaleMarketItems" resultType="com.cosfo.item.infrastructure.item.dto.MarketItemOnSaleSimpleDTO">
        select id, sku_id skuId, on_sale onSale
        from
        market_item
        <where>
            delete_flag = 1
            <if test="onSale != null">
                and on_sale = #{onSale}
            </if>
            <if test="tenantId != null">
                and tenant_id = #{tenantId}
            </if>
        </where>
    </select>
</mapper>
