package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

public interface MarketItemEnum {
    @Getter
    @AllArgsConstructor
    enum GoodsType{
        /**
         * 虚拟货品
         */
        VIRTUAL(0,"无货"),
        /**
         * 报价货品
         */
        QUOTATION(1,"报价货品"),
        /**
         * 自营货品
         */
        SELF_SUPPORT(2,"自营货品"),
        /**
         * 组合货品
         */
        COMBINE(3,"组合货品");

        /**
         * 类型编码
         */
        private Integer code;
        /**
         * 类型描述
         */
        private String desc;

        /**
         * 根据code获取
         *
         * @param code
         * @return
         */
        public static GoodsType getByCode(Integer code){
            for(GoodsType goodsType: GoodsType.values()){
                if(goodsType.getCode().equals(code)){
                    return goodsType;
                }
            }

            return null;
        }
        public static final List<Integer> ALL_ITEM_TYPE = Arrays.asList(MarketItemEnum.GoodsType.SELF_SUPPORT.getCode(), MarketItemEnum.GoodsType.VIRTUAL.getCode(),
                MarketItemEnum.GoodsType.QUOTATION.getCode());
        public static List<Integer> getThirdDeliveryCodes() {
            return Arrays.asList(SELF_SUPPORT.getCode(), QUOTATION.getCode());
        }
    }
    @Getter
    @AllArgsConstructor
    public enum DeleteFlagEnum {

        DELETED(0, "已删除"),

        NORMAL(1, "正常使用"),

        ;

        /**
         * flag
         */
        private Integer flag;

        /**
         * 描述
         */
        private String desc;
    }

    @Getter
    @AllArgsConstructor
    public enum ItemTypeEnum {
        /**
         * 实物商品
         */
        PHYSICAL_ITEM(0, "实物商品"),
        /**
         * 虚拟商品
         */
        VIRTUAL_ITEM(1, "虚拟商品"),
        /**
         * 组合品
         */
        COMBINE_ITEM(2, "组合品");

        private final Integer code;
        private final String desc;

        public static ItemTypeEnum getTypeByCode(Integer code) {
            for (ItemTypeEnum type : ItemTypeEnum.values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }

            return null;
        }

        public static String getDescByCode(Integer code) {
            for (ItemTypeEnum type : ItemTypeEnum.values()) {
                if (type.code.equals(code)) {
                    return type.getDesc();
                }
            }

            return null;
        }

        public final static List<Integer> NON_COMBINE_CODES = Arrays.asList(MarketItemEnum.ItemTypeEnum.PHYSICAL_ITEM.getCode(), MarketItemEnum.ItemTypeEnum.VIRTUAL_ITEM.getCode());
    }

    @Getter
    @AllArgsConstructor
    public enum PriceTypeEnum {

        /**
         * 所有门店展示并统一定价
         */
        ALL_STORE_SHOW_UNITY(0, "所有门店展示并统一定价"),

        ALL_STORE_SHOW_DIFF(1, "所有门店展示单差异化定价"),

        PART_STORE_SHOW_DIFF(2, "部分门店展示且差异化定价"),

        COMBINE_REDUCE_FIXED(3, "组合品按总价下调固定额度"),

        COMBINE_REDUCE_PERCENTAGE(4, "组合品按总价下调百分比"),

        COMBINE_TOTAL(5, "组合品总价"),
        ;
        /**
         * 状态编码
         */
        private Integer code;
        /**
         * 状态描述
         */
        private String desc;


        public static PriceTypeEnum getTypeByCode(Integer code) {
            for (PriceTypeEnum type : PriceTypeEnum.values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }

            return null;
        }

        public static String getDescByCode(Integer code) {
            for (PriceTypeEnum type : PriceTypeEnum.values()) {
                if (type.code.equals(code)) {
                    return type.getDesc();
                }
            }

            return null;
        }
    }
}
