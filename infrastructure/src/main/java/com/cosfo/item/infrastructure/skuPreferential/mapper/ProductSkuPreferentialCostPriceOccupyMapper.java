package com.cosfo.item.infrastructure.skuPreferential.mapper;

import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceOccupy;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 省心定库存占用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Mapper
public interface ProductSkuPreferentialCostPriceOccupyMapper extends BaseMapper<ProductSkuPreferentialCostPriceOccupy> {

    Integer optOccupyQuantity(@Param("id")Long id, @Param("optQuantity")Integer optQuantity);
}
