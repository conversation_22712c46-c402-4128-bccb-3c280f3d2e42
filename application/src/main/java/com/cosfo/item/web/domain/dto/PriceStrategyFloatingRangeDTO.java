package com.cosfo.item.web.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PriceStrategyFloatingRangeDTO {
    private Long tenantId;
    /**
     * 商品ids
     */
    private List<Long> marketItemIds;
    /**
     * 按成本价百分比上浮   浮动值
     */
    private BigDecimal costPriceAddPercentageRange;
    /**
     * 按成本价百分比上浮   加=1 减=-1
     */
    private Integer costPriceAddPercentageOptype;
    /**
     * 按成本价定额上浮   浮动值
     */
    private BigDecimal costPriceAddFixedRange;
    /**
     * 按成本价定额上浮   加=1 减=-1
     */
    private Integer costPriceAddFixedOptype;
    /**
     * 固定价   浮动值
     */
    private BigDecimal assignRange;
    /**
     * 固定价   加=1 减=-1
     */
    private Integer assignOptype;
}
