package com.cosfo.item.provider;

import com.alibaba.fastjson.JSONObject;
import com.cofso.item.client.provider.Market4StoreProvider;
import com.cofso.item.client.req.MarketItemOnSaleReq;
import com.cofso.item.client.resp.MarketItemOnSaleSimple4StoreResp;
import com.google.common.collect.Lists;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2023-05-25
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class Market4StoreProviderTest {

    @DubboReference
    private Market4StoreProvider market4StoreProvider;


    @Test
    public void queryMarketItemOnSaleInfo(){
        MarketItemOnSaleReq req = new MarketItemOnSaleReq();
        req.setTenantId(2L);
        req.setSkuIds(Lists.newArrayList(18743L,18741L));
        DubboResponse<List<MarketItemOnSaleSimple4StoreResp>> response = market4StoreProvider.queryMarketItemOnSaleInfo(req);
        System.out.println(JSONObject.toJSONString(response));
    }

    @Test
    public void queryOnSaleMarketItems(){
        MarketItemOnSaleReq req = new MarketItemOnSaleReq();
        req.setTenantId(2L);
        req.setOnSale(1);
        DubboResponse<List<MarketItemOnSaleSimple4StoreResp>> response = market4StoreProvider.queryOnSaleMarketItems(req);
        System.out.println(JSONObject.toJSONString(response));
    }

}
