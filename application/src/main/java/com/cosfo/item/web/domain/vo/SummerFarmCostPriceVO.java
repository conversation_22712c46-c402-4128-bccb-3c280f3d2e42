package com.cosfo.item.web.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SummerFarmCostPriceVO implements Serializable {

    /**
     * 货品主键
     */
    private Long skuId;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 生效时间
     */
    private LocalDateTime validTime;

    /**
     * 失效时间
     */
    private LocalDateTime invalidTime;

    /**
     * 生效类型,0-永久,1-周期
     */
    private Integer validType;
}
