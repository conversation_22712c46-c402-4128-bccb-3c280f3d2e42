package com.cosfo.item.web.domain.converter;

import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/6 14:23
 * @Description:
 */
@Mapper
public interface MarketOnSaleConvert {
    MarketOnSaleConvert instance = Mappers.getMapper(MarketOnSaleConvert.class);

    List<MarketItemOnsaleStrategyDTO> convert2Dtos(List<MarketItemOnsaleStrategyDTO> dtos);
}
