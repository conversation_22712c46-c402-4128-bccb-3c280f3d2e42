package com.cosfo.item.infrastructure.skuPreferential.dao;

import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceMapping;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 省心定报价城市关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
public interface ProductSkuPreferentialCostPriceMappingDao extends IService<ProductSkuPreferentialCostPriceMapping> {

    List<ProductSkuPreferentialCostPriceMapping> listByCostPriceIds(Long tenantId, List<Long> skuPreferentialIds);

    List<ProductSkuPreferentialCostPriceMapping> queryBySkuIdAndCityIds(Long tenantId, Long skuId, List<Long> cityIds);
    List<ProductSkuPreferentialCostPriceMapping> queryBySkuIdsAndCityId(Long tenantId, Long cityId, Set<Long> skuIds);

    Boolean updateDeletedStatus(List<Long> productSkuPreferentialIds, Long tenantId, Long skuId);

    Boolean updateDeletedStatusByCityIds(List<Long> cityIds, Long tenantId, Long skuId);

    boolean batchSave(List<ProductSkuPreferentialCostPriceMapping> mappingList);

}
