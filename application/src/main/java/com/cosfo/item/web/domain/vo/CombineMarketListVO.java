package com.cosfo.item.web.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2023/5/8 10:47
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CombineMarketListVO {

    /**
     * 组合包编码
     */
    private Long combineMarketId;
    /**
     * 组合包标题
     */
    private String combineMarketTitle;
    /**
     * 二级分类Id
     */
    private Long secondClassificationId;
    /**
     * 二级分类
     */
    private String secondClassificationName;
    /**
     * 一级分类Id
     */
    private Long firstClassificationId;
    /**
     * 一级分类名称
     */
    private String firstClassificationName;

    /**
     * 0 下架 1 上架
     */
    private Integer onSale;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 最后编辑人
     */
    private Long editUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
