package com.cosfo.item.infrastructure.classification.model;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 商品分类关联表(MarketItemClassification)实体类
 *
 * <AUTHOR>
 * @since 2023-05-04 16:42:59
 */
@Data
@TableName("market_item_classification")
public class MarketItemClassification implements Serializable {
    private static final long serialVersionUID = -69633514173958973L;
    /**
     * id主键
     */     
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;
    /**
     * 分类id
     */     
    private Long classificationId;
    /**
     * item id
     */     
    private Long itemId;
    /**
     * 创建时间
     */     
    private LocalDateTime createTime;
    /**
     * 更新时间
     */     
    private LocalDateTime updateTime;
    /**
     * market主键Id
     */     
    private Long marketId;

}

