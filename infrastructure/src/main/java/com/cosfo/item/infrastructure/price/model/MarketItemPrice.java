package com.cosfo.item.infrastructure.price.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 商品价格表(MarketItemPrice)实体类
 *
 * <AUTHOR>
 * @since 2023-04-28 15:45:04
 */
@Data
@TableName("market_item_price")
public class MarketItemPrice implements Serializable {
    private static final long serialVersionUID = 563382782518070025L;
    /**
     * 主键id
     */     
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;
    /**
     * sku主键
     */     
    private Long marketItemId;
    /**
     * 价格目标 0-品牌方,1-门店,2-运营区域,3-门店分组
     */     
    private Integer targetType;
    /**
     * 价格目标类型id
     */     
    private Long targetId;
    /**
     * 价格
     */     
    private BigDecimal price;
    /**
     * 价格规则
     */
    private String priceStrategy;
    /**
     * 底价
     */
    private BigDecimal basePrice;
    /**
     * 创建时间
     */     
    private LocalDateTime createTime;
    /**
     * 更新时间
     */     
    private LocalDateTime updateTime;

}

