package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CosfoMarketItemUnfairPriceStrategyHandler implements DbTableDmlStrategy {

    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Autowired
    private UnfairPriceStrategyDomainService unfairPriceStrategyDomainService;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.MARKET_ITEM_UNFAIR_PRICE_STRATEGY;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();

                Long tenantId = Long.valueOf (dataMap.get ("tenant_id"));
                Long itemId = Long.valueOf (dataMap.get ("item_id"));
                Integer defaultFlag = Integer.valueOf (dataMap.get ("default_flag"));

                Map<String, String> oldMap = pair.getValue ();
                if(oldMap.containsKey ("strategy_value")){
                    handleItemPrice (tenantId,itemId,defaultFlag);
                }
            }
        }else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.DELETE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue();
                Long tenantId = Long.valueOf (oldMap.get ("tenant_id"));
                Long itemId = Long.valueOf (oldMap.get ("item_id"));
                Integer defaultFlag = Integer.valueOf (oldMap.get ("default_flag"));

                MarketItemUnfairPriceStrategyEnum.StrategyValueEnum defaultStrategyValue = unfairPriceStrategyDomainService.getDefaultStrategyValueByTenantId (tenantId);
                if(!Objects.equals (defaultStrategyValue.getCode (), defaultFlag)) {
                    handleItemPrice (tenantId, itemId, defaultFlag);
                }
            }
        }
    }

    private void handleItemPrice(Long tenantId,Long itemId,Integer defaultFlag) {
        List<MarketItemVO> marketItemVOS = null;
        if(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.N.getCode().equals (defaultFlag)){
            MarketItemVO marketItem = itemDomainService.getMarketItemDetailById (tenantId, itemId);
            if(ObjectUtil.isNotNull (marketItem)) {
                marketItemVOS = new ArrayList<> ();
                marketItemVOS.add (marketItem);
            }
        }else {
            marketItemVOS = itemDomainService.listMarketItemByTenantIdAndTypes (tenantId, Arrays.asList (MarketItemEnum.GoodsType.QUOTATION.getCode (), MarketItemEnum.GoodsType.VIRTUAL.getCode (), MarketItemEnum.GoodsType.SELF_SUPPORT.getCode ()));
            Set<Long> itemIds = unfairPriceStrategyDomainService.listItemIdsWithOutDefaultStrategy(tenantId);
            //过滤使用默认放倒挂策略的商品
            if(CollectionUtil.isNotEmpty (marketItemVOS) && CollectionUtil.isNotEmpty (itemIds)){
                marketItemVOS = marketItemVOS.stream ().filter (e -> !itemIds.contains (e.getId ())).collect (Collectors.toList ());
            }
        }
        if(CollectionUtil.isEmpty (marketItemVOS)) {
            return;
        }
        for (MarketItemVO marketItemVO: marketItemVOS) {
            ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }
}
