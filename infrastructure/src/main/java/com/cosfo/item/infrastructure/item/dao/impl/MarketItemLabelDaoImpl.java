package com.cosfo.item.infrastructure.item.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.common.enums.MarketItemLabelStatusEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemLabelDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemLabelQueryParam;
import com.cosfo.item.infrastructure.item.mapper.MarketItemLabelMapper;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import com.cosfo.item.infrastructure.item.model.MarketItemLabel;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商品标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
public class MarketItemLabelDaoImpl extends ServiceImpl<MarketItemLabelMapper, MarketItemLabel> implements MarketItemLabelDao {

    @Override
    public List<MarketItemLabel> selectByNameAndTenantId(String labelName, Long tenantId) {
        LambdaQueryWrapper<MarketItemLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketItemLabel::getLabelName, labelName).eq(MarketItemLabel::getTenantId, tenantId);
        return list(queryWrapper);
    }

    @Override
    public List<MarketItemLabel> queryMarketItemLabel(MarketItemLabelQueryParam param) {
        LambdaQueryWrapper<MarketItemLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketItemLabel::getLabelStatus, MarketItemLabelStatusEnum.ENABLE.getType());
        if (param.getLabelName() != null) {
            queryWrapper.eq(MarketItemLabel::getLabelName, param.getLabelName());
        }
        if (param.getTenantId() != null) {
            queryWrapper.eq(MarketItemLabel::getTenantId, param.getTenantId());
        }
        return list(queryWrapper);
    }

    @Override
    public List<MarketItemLabel> getByIds(List<Long> ids) {
        LambdaQueryWrapper<MarketItemLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(ids), MarketItemLabel::getId, ids);
        return list(queryWrapper);
    }
}
