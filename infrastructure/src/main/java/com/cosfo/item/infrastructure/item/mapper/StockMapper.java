package com.cosfo.item.infrastructure.item.mapper;

import com.cosfo.item.infrastructure.item.model.Stock;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 库存表(Stock)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-05 14:19:47
 */
@Mapper
public interface StockMapper extends BaseMapper<Stock>{

    /**
     * 更新前查询
     *
     * @return
     */
    Stock preUpdateQuery(@Param("tenantId") Long tenantId, @Param("itemId") Long itemId);

    /**
     * 更新库存数量
     * @param id 主键
     * @param addAmount 增加数量
     */
    Integer increaseStock(@Param("id") Long id, @Param("addAmount") Integer addAmount);
}

