package com.cosfo.item.web.domain.converter;

import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cosfo.item.infrastructure.price.model.MarketItemUnfairPriceStrategy;

public class MarketItemUnfairPriceStrategyConvert {
    public static MarketItemUnfairPriceStrategy buildDefaultUnfairPriceStrategy(Long tenantId){
        MarketItemUnfairPriceStrategy e = new MarketItemUnfairPriceStrategy ();
        e.setTenantId(tenantId);
        e.setItemId(-1L);
        e.setDefaultFlag(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.Y.getCode ());
        e.setTargetType(MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode ());
        e.setStrategyValue(MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.USE_COST_PRICE.getCode ());
        return e;
    }
}
