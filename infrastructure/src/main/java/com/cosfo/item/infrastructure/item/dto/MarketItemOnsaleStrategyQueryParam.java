package com.cosfo.item.infrastructure.item.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/8 16:38
 * @Description:
 */
@Data
@Builder
public class MarketItemOnsaleStrategyQueryParam {
    private List<Long> itemIds;
    private Long tenantId;

    /**
     * 是否大客户专享
     */
    private Integer mType;

    /**
     * 是否展示
     */
    private Integer showFlag;
    /**
     * 0下架 1上架
     */
    private Integer onSale;

    /**
     * 上架目标类型 1按租户,2 按门店 3单店 4大客户
     * UNIQUE KEY `idx_tenantid_itemid_targetid_strategytype`
     */
    private Integer strategyType;

    /**
     * area no
     */
    private Long targetId;
}
