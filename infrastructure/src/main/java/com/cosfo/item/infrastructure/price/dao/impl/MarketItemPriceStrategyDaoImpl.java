package com.cosfo.item.infrastructure.price.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.infrastructure.price.mapper.MarketItemPriceStrategyMapper;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategyMapping;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 商品item价格策略表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class MarketItemPriceStrategyDaoImpl extends ServiceImpl<MarketItemPriceStrategyMapper, MarketItemPriceStrategy> implements MarketItemPriceStrategyDao {

    @Override
    public List<MarketItemPriceStrategy> listByItemIds(Long tenantId, List<Long> itemIds) {
        LambdaQueryWrapper<MarketItemPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull (tenantId),MarketItemPriceStrategy::getTenantId,tenantId);
        wrapper.in(MarketItemPriceStrategy::getItemId,itemIds);
        return list (wrapper);
    }

    @Override
    public List<MarketItemPriceStrategy> listByItemIdsAndStrategyType(Long tenantId, List<Long> itemIds, Integer strategyType) {
        LambdaQueryWrapper<MarketItemPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull (tenantId),MarketItemPriceStrategy::getTenantId,tenantId);
        wrapper.eq(ObjectUtil.isNotNull (strategyType),MarketItemPriceStrategy::getStrategyType,strategyType);
        wrapper.in(MarketItemPriceStrategy::getItemId,itemIds);
        return list (wrapper);
    }

    @Override
    public List<MarketItemPriceStrategy> getByItemIdAndTargetType(Long tenantId, Long marketItemId, Integer targetType) {
        LambdaQueryWrapper<MarketItemPriceStrategy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull (tenantId),MarketItemPriceStrategy::getTenantId,tenantId);
        wrapper.eq(MarketItemPriceStrategy::getItemId,marketItemId);
        wrapper.eq(MarketItemPriceStrategy::getTargetType,targetType);
        return list (wrapper);
    }

    @Override
    public List<MarketItemPriceStrategy> listByItemIdsAndStrategyTypeAndTarget(Long tenantId, List<Long> itemIds, Integer strategyType, Long targetId, Integer targetType) {
        if(targetId == null){
            return Collections.emptyList ();
        }
        MPJLambdaWrapper<MarketItemPriceStrategy> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(MarketItemPriceStrategy.class);
        queryWrapper.leftJoin(MarketItemPriceStrategyMapping.class, MarketItemPriceStrategyMapping::getItemPriceStrategyId, MarketItemPriceStrategy::getId);
        queryWrapper.eq(MarketItemPriceStrategy::getTenantId, tenantId)
                .eq(MarketItemPriceStrategy::getStrategyType, strategyType)
                .eq(MarketItemPriceStrategyMapping::getTargetId, targetId) // 连接表的条件
                .eq(MarketItemPriceStrategy::getTargetType, targetType)
                .in(MarketItemPriceStrategy::getItemId, itemIds);
        return list (queryWrapper);
    }
}
