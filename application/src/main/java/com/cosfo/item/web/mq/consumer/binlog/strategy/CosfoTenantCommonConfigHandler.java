package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.lang.Pair;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import com.cosfo.item.web.skuPreferential.domain.SkuPreferentialDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class CosfoTenantCommonConfigHandler implements DbTableDmlStrategy {

    @Autowired
    private SkuPreferentialDomainService skuPreferentialDomainService;
    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.TENANT_COMMON_CONFIG;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();

                Long tenantId = Long.valueOf (dataMap.get ("tenant_id"));
                String configKey = dataMap.get ("config_key");
                if(StringUtils.equals (configKey,"save_worry")) {
                    handleItemPrice (tenantId);
                }
            }
        }
    }

    private void handleItemPrice(Long tenantId) {
        Set<Long> skuIds = skuPreferentialDomainService.queryAvailableSkuIdByTenantId (tenantId);
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(tenantId,skuIds);
        if (CollectionUtils.isEmpty(marketItems)) {
            log.warn("没有找到marketItemVOS：{}", tenantId);
            return;
        }
        for (MarketItem e : marketItems) {
            ItemChangeMessageDTO dto = ItemConverter.marketItem2MsgDTO (e);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }
}
