package com.cosfo.item.web.facade.converter;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.web.domain.vo.ProvinceCityAreaVO;
import com.cosfo.item.web.domain.vo.SummerFarmCostPriceVO;
import com.cosfo.item.web.domain.vo.TenantVO;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import net.summerfarm.mall.client.resp.MallPrice4SaasResp;
import net.summerfarm.mall.client.saas.model.AddressInfoVO;
import net.summerfarm.mall.client.saas.resp.AddressInfoResp;

public class SummerfarmMallConverter {
    public static SummerFarmCostPriceVO mallPrice4SaasResp2VO(MallPrice4SaasResp resp) {
        SummerFarmCostPriceVO vo = new SummerFarmCostPriceVO();
        vo.setSkuId(resp.getSkuId());
        vo.setSkuCode(resp.getSkuCode());
        vo.setPrice(resp.getPrice());
        vo.setValidTime(resp.getValidTime());
        vo.setInvalidTime(resp.getInvalidTime());
        vo.setValidType(resp.getValidType());
        return vo;
    }

    public static ProvinceCityAreaVO addressInfoResp2VO(AddressInfoVO resp) {
        ProvinceCityAreaVO vo = new ProvinceCityAreaVO();
        vo.setProvince(resp.getProvinceName());
        vo.setCity(resp.getCityName());
        vo.setArea(resp.getAreaName());
        return vo;
    }
}
