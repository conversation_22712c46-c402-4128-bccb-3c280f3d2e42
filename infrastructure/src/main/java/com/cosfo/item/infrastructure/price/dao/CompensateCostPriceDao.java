package com.cosfo.item.infrastructure.price.dao;

import com.cosfo.item.infrastructure.price.model.CompensateCostPrice;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 成本价格表补偿表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface CompensateCostPriceDao extends IService<CompensateCostPrice> {

    List<CompensateCostPrice>  listCompenstateCostPriceByIds(List<Long> ids);
}
