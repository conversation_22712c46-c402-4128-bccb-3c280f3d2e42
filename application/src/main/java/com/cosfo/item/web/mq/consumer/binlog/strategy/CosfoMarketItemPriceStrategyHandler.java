package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.*;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class CosfoMarketItemPriceStrategyHandler implements DbTableDmlStrategy {

    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;

    @Autowired
    private MarketItemDao marketItemDao;

    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.MARKET_ITEM_PRICE_STRATEGY;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        Set<Long> result = new HashSet<> ();
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                Long itemId = Long.valueOf (map.get ("item_id"));
                result.add (itemId);
            });
        }else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                Map<String, String> oldMap = pair.getValue ();

                Long itemId = Long.valueOf (dataMap.get ("item_id"));

                if (oldMap.containsKey ("strategy_value")) {
                    result.add (itemId);
                }
            }
        }
        if(CollectionUtil.isNotEmpty (result)){
            List<MarketItem> marketItems = marketItemDao.listByIds (result);
            marketItems.forEach(item -> {
                ItemChangeMessageDTO dto = ItemConverter.marketItem2MsgDTO (item);
                marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
            });
        }
    }
}
