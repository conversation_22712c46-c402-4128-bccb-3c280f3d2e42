<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.skuPreferential.mapper.ProductSkuPreferentialCostPriceOccupyMapper">

    <update id="optOccupyQuantity">
        update product_sku_preferential_cost_price_occupy set
            occupy_quantity = occupy_quantity + #{optQuantity}
        where id = #{id}
          and occupy_quantity + #{optQuantity} >= 0
    </update>
</mapper>
