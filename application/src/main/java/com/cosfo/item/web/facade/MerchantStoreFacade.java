package com.cosfo.item.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.manage.client.merchant.MerchantStoreProvider;
import com.cosfo.manage.client.merchant.resp.MerchantStoreAddressResp;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.merchant.provider.MerchantStoreGroupQueryProvider;
import net.xianmu.usercenter.client.merchant.resp.MerchantStoreGroupResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import com.cosfo.item.web.facade.converter.MerchantStoreConverter;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MerchantStoreFacade {

    /**
     * 该查询包含common_location_city相关数据
     * 因此仍调用manage接口，未迁移到用户中心
     */
    @DubboReference
    private MerchantStoreProvider storeProvider;

    @DubboReference
    private MerchantStoreGroupQueryProvider merchantStoreGroupQueryProvider;

    private final LoadingCache<Long, List<MerchantStoreAddressVO>> STORE_CACHE = CacheBuilder.newBuilder()
        .maximumSize(100)
        .recordStats()
        .expireAfterWrite(Duration.ofSeconds(10L))//10 seconds
        .build(new CacheLoader<Long, List<MerchantStoreAddressVO>>() {
            @Override
            public List<MerchantStoreAddressVO> load(Long tenantId) throws Exception {
                return batchQueryStoreAddressInternal(tenantId);
            }
        });

    private List<MerchantStoreAddressVO> batchQueryStoreAddressInternal(Long tenantId) {
        log.info("从数据库中获取租户的所有店铺地址:{}", tenantId);
        DubboResponse<List<MerchantStoreAddressResp>> response = storeProvider.batchQueryStoreAddress(tenantId);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        List<MerchantStoreAddressResp> data = response.getData();
        return data.stream().map(MerchantStoreConverter::merchantStoreAddressResp2VO).collect(Collectors.toList());
    }

    public List<MerchantStoreAddressVO> batchQueryStoreAddressFromCache(Long tenantId) {
        try {
            log.info("从缓存中获取租户的所有店铺地址:{}", tenantId);
            return STORE_CACHE.get(tenantId);
        } catch (Exception e) {
            log.warn ("从缓存中获取店铺地址失败:{}", tenantId, e);
            return batchQueryStoreAddressInternal(tenantId);
        }
    }

    public List<MerchantStoreAddressVO> batchQueryStoreAddressByIds(Long tenantId, List<Long> storeIds) {
        DubboResponse<List<MerchantStoreAddressResp>> response = storeProvider.batchQueryStoreAddressByIds(tenantId, storeIds);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        List<MerchantStoreAddressResp> data = response.getData();
        return data.stream().map(e -> MerchantStoreConverter.merchantStoreAddressResp2VO(e)).collect(Collectors.toList());
    }

    public MerchantStoreAddressVO queryStoreAddressInternal(String key) {
        String[] split = key.split ("-");
        DubboResponse<MerchantStoreAddressResp> response = storeProvider.queryStoreAddress(Long.parseLong (split[0]), Long.parseLong (split[1]));
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        MerchantStoreAddressResp data = response.getData();
        return MerchantStoreConverter.merchantStoreAddressResp2VO(data);
    }
    private final LoadingCache<String, MerchantStoreAddressVO> STORE_DETAIL_CACHE = CacheBuilder.newBuilder()
            .maximumSize(200)
            .recordStats()
            .expireAfterWrite(Duration.ofSeconds(60L))//1 分钟
            .build(new CacheLoader<String,MerchantStoreAddressVO>() {
                @Override
                public MerchantStoreAddressVO load(String key) throws Exception {
                    return queryStoreAddressInternal(key);
                }
            });

    public MerchantStoreAddressVO queryStoreAddress(Long tenantId, Long storeId) {
        try {
            log.info("从缓存中获取租户的单个店铺地址:{}", storeId);
            return STORE_DETAIL_CACHE.get(tenantId + "-" + storeId);
        } catch (Exception e) {
            log.warn ("从缓存中获取单个店铺地址失败:{}", tenantId, e);
            return queryStoreAddressInternal(tenantId + "-" + storeId);
        }
    }
    /**
     * 根据分组id列表查询门店id
     *
     * @param tenantId
     * @param groupIdList
     * @return
     */
    public Map<Long,List<Long>> getGroupByStoreGroupIds(Long tenantId, List<Long> groupIdList) {
        if(CollectionUtil.isEmpty (groupIdList)){
            return Collections.emptyMap ();
        }
        DubboResponse<List<MerchantStoreGroupResultResp>> response = merchantStoreGroupQueryProvider.getGroupByStoreGroupIds(tenantId, groupIdList);
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        List<MerchantStoreGroupResultResp> data = response.getData ();
        if(CollectionUtil.isNotEmpty (data)){
           return data.stream().collect(Collectors.groupingBy(MerchantStoreGroupResultResp::getMerchantStoreGroupId, Collectors.mapping(MerchantStoreGroupResultResp::getStoreId, Collectors.toList())));
        }
        return Collections.emptyMap ();
    }
    /**
     * 根据门店id查询门店分组id
     *
     * @param tenantId
     * @return
     */
    public Long getGroupIdByStoreId(Long tenantId, Long storeId) {
        DubboResponse<List<MerchantStoreGroupResultResp>> response = merchantStoreGroupQueryProvider.getGroupByStoreIds (tenantId, Collections.singletonList (storeId));
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        List<MerchantStoreGroupResultResp> data = response.getData ();
        if(CollectionUtil.isNotEmpty (data)){
           return data.get (0).getMerchantStoreGroupId ();
        }
        return null;
    }
}
