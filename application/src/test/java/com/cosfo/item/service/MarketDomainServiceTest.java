package com.cosfo.item.service;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemParam;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.web.domain.dto.MarketItemDTO;
import com.cosfo.item.web.domain.dto.MarketPageQueryDTO;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.service.MarketDomainService;
import com.cosfo.item.web.domain.vo.MarketPageInfoVO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

/**
 *
 * @author: xiaowk
 * @time: 2023/5/16 上午11:21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MarketDomainServiceTest {

    @Autowired
    private MarketDomainService marketDomainService;
    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private ItemDomainService itemDomainService;

    @Test
    public void testqueryMarketListPage(){
        MarketPageQueryDTO queryDTO = new MarketPageQueryDTO();
        queryDTO.setTenantId(2L);
        queryDTO.setPageNum(1);
        queryDTO.setPageSize(1000);
        queryDTO.setDeleteFlag(1);
        queryDTO.setOnSale(1);

        Page<MarketPageInfoVO> pageVO = marketDomainService.queryMarketListPage(queryDTO);
    }

    @Test
    public void testQuery1000(){
        List<Long> ids = Arrays.asList(22182L);
        Long byidStart = System.currentTimeMillis();
        List<MarketItem> marketItems = marketItemDao.listByIds(ids);
        long byidend = System.currentTimeMillis();

        Long byParamStart = System.currentTimeMillis();
        List<MarketItem> marketItems1 = marketItemDao.listByParam(MarketItemParam.builder().itemIds(ids).build());
        Long byParamEnd = System.currentTimeMillis();

        System.out.println("byId: size:" + marketItems.size() + "耗时：" + (byidend - byidStart));
        System.out.println("byPram: size:" + marketItems1.size() + "耗时：" + (byParamEnd - byParamStart));


    }

    @Test
    public void testSaveLabel() {
        MarketItemDTO marketItemDTO = new MarketItemDTO();
        marketItemDTO.setItemCode("6554523627");
        marketItemDTO.setTenantId(1L);
        marketItemDTO.setOutId(23660L);
        marketItemDTO.setItemLabel("[{\"id\":1,\"labelName\":\"特价品\"},{\"id\":2,\"labelName\":\"临保品\"},{\"id\":7,\"labelName\":\"测试\"}]");
        itemDomainService.saveItemLabel(marketItemDTO);
    }
}
