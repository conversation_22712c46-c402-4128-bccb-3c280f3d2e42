package com.cosfo.item.infrastructure.classification.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.classification.dto.MarketItemClassificationQueryParam;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;

import java.util.List;

/**
 * 商品分类关联表(MarketItemClassification)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-04 16:43:03
 */
public interface MarketItemClassificationDao extends IService<MarketItemClassification> {

    List<MarketItemClassification> listByParam(MarketItemClassificationQueryParam param);
}
