package com.cosfo.item.web.domain.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.common.enums.CostPriceEnum;
import com.cosfo.item.common.enums.DataSynchronizationInformationEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.offline.dao.DataSynchronizationInformationDao;
import com.cosfo.item.infrastructure.offline.dao.SelfGoodsCostPriceDao;
import com.cosfo.item.infrastructure.offline.model.DataSynchronizationInformation;
import com.cosfo.item.infrastructure.offline.model.SelfGoodsCostPrice;
import com.cosfo.item.infrastructure.price.dto.CostPriceDTO;
import com.cosfo.item.web.domain.dto.CostPriceOptDTO;
import com.cosfo.item.web.domain.vo.MarketItemVO;
import com.cosfo.item.web.domain.vo.ProductAgentSkuMappingVO;
import com.cosfo.item.web.domain.vo.TenantVO;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.TenantFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SelfGoodsCostPriceSycnService {
    @Autowired
    private MqProducer mqProducer;
    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private SelfGoodsCostPriceDao selfGoodsCostPriceDao;
    @Autowired
    private DataSynchronizationInformationDao dataSynchronizationInformationDao;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private TenantFacade tenantFacade;
    public void sycnSelfGoodsCostPriceFromOffline(Long tenantId) {
        //先查询信号表
        DataSynchronizationInformation syncInfo = dataSynchronizationInformationDao.selectByTName(DataSynchronizationInformationEnum.TName.SELF_GOODS_COST_PRICE.getName ());
        //判断时间是否前一天的数据
        Integer dateFlag = syncInfo.getDateFlag();
        String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now().minusDays(1));
        //信号表时间不是前一天，则跳过同步处理
        if (!Objects.equals(dateStr, String.valueOf(dateFlag))) {
            log.warn("【自营商品成本价】离线表标签数据非当天最新数据，稍后重试！");
            mqProducer.sendDelay (RocketMqConstant.Topic.TOPIC_SELF_GOODS_COST_PRICE, RocketMqConstant.Tag.SELF_GOODS_COST_PRICE, String.valueOf (tenantId), new Long(5 * 60 * 1000));
            return;
        }
        List<Long> ids = new ArrayList<> ();
        boolean hasNext = true;
        int pageIndex = 1;
        List<TenantVO> tenantVOS = tenantFacade.listAllTenant ();
        Page<SelfGoodsCostPrice> page;
        while(hasNext){
            page = selfGoodsCostPriceDao.pageByTenantId(tenantId,500,pageIndex);
            page.getRecords().forEach(e-> {
                CostPriceDTO dto = new CostPriceDTO();
                dto.setSysType(CostPriceEnum.SysType.SUMMERFARM.getCode());
                dto.setSkuCode(e.getSkuCode ());
                dto.setTenantId(e.getTenantId ());
                dto.setArea(e.getArea ());
                dto.setCity(e.getCity ());
                dto.setProvince (e.getProvince ());
                dto.setSkuId(e.getSkuId());
                dto.setInvalidTime(LocalDateTime.of(2050, Month.DECEMBER,31,00,00,00));
                dto.setValidTime(LocalDateTime.of(2000, Month.DECEMBER,31,00,00,00));
                dto.setValidType(CostPriceEnum.ValidType.FOREVER.getCode ());
                dto.setPrice(e.getPrice());
                try {
                    CostPriceOptDTO costPriceOptDTO = costPriceDomianService.saveOrUpdateByCityAreaAndTenantId (dto);
                    ids.add (costPriceOptDTO.getId ());
                }catch (Exception exception){
                    log.error ("自营货品成本价异步保存错误,dto={}",JSON.toJSONString (dto),exception);
                }
            });
            hasNext = page.hasNext();
            pageIndex = pageIndex + 1;
        }
        //按照 租户 把不存在的的自营品成本价删除
        for (TenantVO tenant : tenantVOS) {
            List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemByTenantIdAndType (tenant.getId (), MarketItemEnum.GoodsType.SELF_SUPPORT.getCode ());
            if (!ObjectUtil.isEmpty (marketItemVOS)) {
                Set<Long> skuIds = marketItemVOS.stream ().filter (e -> ObjectUtil.isNotNull (e.getSkuId ())).map (MarketItemVO::getSkuId).collect (Collectors.toSet ());
                List<ProductAgentSkuMappingVO> skuMappingVOS = productFacade.listProductMappingBySkuIdsAndTenantId(skuIds, tenant.getId());
                Set<Long> agentSkuIds = skuMappingVOS.stream ().map (ProductAgentSkuMappingVO::getAgentSkuId).collect (Collectors.toSet ());
                costPriceDomianService.deleteCostPrice4SelfGoods (tenant.getId (),agentSkuIds,ids);
            }
        }
    }
}
