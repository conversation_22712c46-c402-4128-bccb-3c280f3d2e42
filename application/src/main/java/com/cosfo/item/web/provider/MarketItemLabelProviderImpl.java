package com.cosfo.item.web.provider;

import com.cofso.item.client.provider.MarketItemLabelProvider;
import com.cofso.item.client.req.MarketItemLabelInsertReq;
import com.cofso.item.client.req.MarketItemLabelQueryReq;
import com.cofso.item.client.resp.MarketItemLabelResp;
import com.cosfo.item.web.domain.service.MarketItemLabelDomainService;
import com.cosfo.item.web.domain.vo.MarketItemLabelVO;
import com.cosfo.item.web.provider.converter.MarketItemLabelConverter;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName MarketItemLabelProvider
 * @Description
 * <AUTHOR>
 * @Date 18:04 2024/5/13
 * @Version 1.0
 **/
@DubboService
@Slf4j
public class MarketItemLabelProviderImpl implements MarketItemLabelProvider {

    @Resource
    private MarketItemLabelDomainService marketItemLabelDomainService;

    @Override
    public DubboResponse<MarketItemLabelResp> saveMarketItemLabel(@Valid MarketItemLabelInsertReq marketItemLabelInsertReq) {
        MarketItemLabelVO marketItemLabelVO = marketItemLabelDomainService.saveMarketItemLabel(MarketItemLabelConverter.marketItemLabelInsertReqToDTO(marketItemLabelInsertReq));
        return DubboResponse.getOK(MarketItemLabelConverter.marketItemLabelVOToResp(marketItemLabelVO));
    }

    @Override
    public DubboResponse<List<MarketItemLabelResp>> queryMarketItemLabel(MarketItemLabelQueryReq marketItemLabelQueryReq) {
        List<MarketItemLabelVO> marketItemLabelVOS = marketItemLabelDomainService.queryMarketItemLabel(MarketItemLabelConverter.marketItemLabelQueryReqToDTO(marketItemLabelQueryReq));
        return DubboResponse.getOK(MarketItemLabelConverter.marketItemLabelVOSToResp(marketItemLabelVOS));
    }
}
