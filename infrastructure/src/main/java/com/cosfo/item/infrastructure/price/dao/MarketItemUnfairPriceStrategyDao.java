package com.cosfo.item.infrastructure.price.dao;

import com.cosfo.item.infrastructure.price.model.MarketItemUnfairPriceStrategy;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 商品倒挂策略表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 */
public interface MarketItemUnfairPriceStrategyDao extends IService<MarketItemUnfairPriceStrategy> {

    /**
     * 查询默认 价格倒挂 策略
     * @param tenantId
     * @return
     */
    MarketItemUnfairPriceStrategy getDefaultStrategyValueByTenantId(Long tenantId);

    /**
     * 查询某商品的  价格倒挂 策略
     * @param tenantId
     * @param itemId
     * @param targetType
     * @return
     */
    MarketItemUnfairPriceStrategy getStrategyValueByItemIdAndTargetType(Long tenantId, Long itemId, Integer targetType);
    List<MarketItemUnfairPriceStrategy> getStrategyValueByItemIsdAndTargetType(Long tenantId, List<Long> itemIds, Integer targetType);

    /**
     *
     *
     * @param strategyValues
     * @param defaultFlag
     * @return
     */
    List<MarketItemUnfairPriceStrategy> getByDefaultFlagAndStrategyValue(List<Integer> strategyValues, Integer defaultFlag, Integer targetType, Long tenantId);

    /**
     * 查询未使用默认放倒挂策略的itemid
     * @param tenantId
     */
    Set<Long> listItemIdsWithOutDefaultStrategy(Long tenantId);
}
