package com.cosfo.item;

import com.alibaba.schedulerx.SchedulerxAutoConfigure;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * 加载指定类测试
 * @author: xiaowk
 * @date: 2023/4/16 上午3:10
 */
//@SpringBootApplication
@EnableAutoConfiguration(exclude = {SchedulerxAutoConfigure.class,DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"com.cosfo", "net.xianmu.authentication"},
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = { Application.class }),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.cosfo\\.manage\\.common\\.task..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.cosfo\\.manage\\..*\\.controller.*")
        })
@DubboComponentScan(basePackages = "com.cosfo.manage.**.provider")
public class TestApplication {

}