package com.cosfo.item.web.provider;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.provider.CombineMarketProvider;
import com.cofso.item.client.req.CombineAddReq;
import com.cofso.item.client.req.CombineMarketQueryInputReq;
import com.cofso.item.client.resp.CombineItemResp;
import com.cofso.item.client.resp.CombineMarketDetailResp;
import com.cofso.item.client.resp.CombineMarketListResp;
import com.cofso.page.PageResp;
import com.cosfo.item.web.domain.dto.CombineMarketQueryDTO;
import com.cosfo.item.web.domain.service.MarketCombineDomainService;
import com.cosfo.item.web.domain.vo.CombineMarketDetailVO;
import com.cosfo.item.web.domain.vo.CombineMarketListVO;
import com.cosfo.item.web.domain.vo.MarketCombineVO;
import com.cosfo.item.web.provider.converter.CombineMarketConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 10:18
 * @Description:
 */
@DubboService
@Slf4j
public class CombineMarketProviderImpl implements CombineMarketProvider {

    @Resource
    private MarketCombineDomainService marketCombineDomainService;


    @Override
    public DubboResponse<List<CombineItemResp>> getMarketItemDetail(Long marketItemId) {
        List<MarketCombineVO> marketCombineVOs = marketCombineDomainService.queryCombineSubItemList(marketItemId);
        return DubboResponse.getOK(CombineMarketConvert.INSTANCE.convert2CombineItems(marketCombineVOs));
    }

    @Override
    public DubboResponse<PageResp<CombineMarketListResp>> combineList(CombineMarketQueryInputReq inputReq) {
        CombineMarketQueryDTO queryDTO = CombineMarketConvert.INSTANCE.convert2QueryDto(inputReq);
        Page<CombineMarketListVO> marketListVOPage = marketCombineDomainService.combineList(queryDTO);
        if (Objects.isNull(marketListVOPage)) {
            return DubboResponse.getOK(PageResp.emptyPage(queryDTO.getPageNum(), queryDTO.getPageNum()));
        } else {
            return DubboResponse.getOK(PageResp.toPageList(CombineMarketConvert.INSTANCE.convert2Resps(marketListVOPage.getRecords()),
                Math.toIntExact(marketListVOPage.getTotal()), queryDTO.getPageNum(), queryDTO.getPageSize()));
        }
    }

    @Override
    public DubboResponse<CombineMarketDetailResp> combineDetail(CombineMarketQueryInputReq inputReq) {
        CombineMarketDetailVO combineMarketDetailVO = marketCombineDomainService.combineDetail(CombineMarketConvert.INSTANCE.convert2QueryDto(inputReq));
        return DubboResponse.getOK(CombineMarketConvert.INSTANCE.convert2Resp(combineMarketDetailVO));
    }

    @Override
    public DubboResponse<Long> combineAdd(CombineAddReq req) {
        Long id = marketCombineDomainService.combineAdd(CombineMarketConvert.INSTANCE.convert2Dto(req));
        return DubboResponse.getOK(id);
    }

    @Override
    public DubboResponse<Boolean> combineUpdate(CombineAddReq req) {
        marketCombineDomainService.combineUpdate(CombineMarketConvert.INSTANCE.convert2Dto(req));
        return DubboResponse.getOK(Boolean.TRUE);
    }
}
