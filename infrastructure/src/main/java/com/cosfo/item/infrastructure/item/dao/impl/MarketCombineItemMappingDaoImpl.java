package com.cosfo.item.infrastructure.item.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.item.dao.MarketCombineItemMappingDao;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.mapper.MarkertCombineItemMappingMapper;
import com.cosfo.item.infrastructure.item.model.MarketCombineItemMapping;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 组合品映射表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class MarketCombineItemMappingDaoImpl extends ServiceImpl<MarkertCombineItemMappingMapper, MarketCombineItemMapping> implements MarketCombineItemMappingDao {

    @Override
    public List<MarketCombineItemMapping> listByParam(MarketCombineQueryParam param) {
        return list(buildQueryWrapper(param));
    }

    @Override
    public Set<Long> listItemIdsByCombineItemId(Long tenantId, Long combineItemId) {
        if (Objects.isNull(combineItemId)) {
            return Collections.emptySet();
        }
        LambdaQueryWrapper<MarketCombineItemMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketCombineItemMapping::getTenantId, tenantId);
        queryWrapper.eq(MarketCombineItemMapping::getCombineItemId, combineItemId);
        queryWrapper.select(MarketCombineItemMapping::getItemId);
        return baseMapper.selectObjs(queryWrapper).stream().map(o -> (Long) o).collect(Collectors.toSet());


    }

    private LambdaQueryWrapper<MarketCombineItemMapping> buildQueryWrapper(MarketCombineQueryParam param) {
        LambdaQueryWrapper<MarketCombineItemMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MarketCombineItemMapping::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getSubItemId()), MarketCombineItemMapping::getCombineItemId, param.getSubItemId());
        queryWrapper.eq(Objects.nonNull(param.getItemId()), MarketCombineItemMapping::getItemId, param.getItemId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getItemIds()), MarketCombineItemMapping::getItemId, param.getItemIds());

        return queryWrapper;
    }
}
