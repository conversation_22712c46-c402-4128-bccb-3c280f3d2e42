package com.cosfo.item.web.domain.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: monna.chen
 * @Date: 2023/5/4 14:09
 * @Description: 商品详情 market + market_item
 */
@Data
public class MarketInfoDTO {
    /**
     * 商品主键
     */
    private Long marketId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 主标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 分类
     */
    private Long classificationId;
    /**
     * 后台类目
     */
    private Long categoryId;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 详情图
     */
    private String detailPicture;

    /**
     * 规格
     */
    private MarketItemDTO marketItem;

    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;


    /**
     * 鲜沐ID
     */
    private Long outId;

    /**
     * 从截单开始的售后时间
     */
    private Integer afterSaleTime;

    /**
     * 售后原因
     */
    private String afterSaleReason;

    /**
     * 退款原因,拍多/拍错/不想要
     */
    private String refundReason;

    /**
     * 描述
     */
    private String description;

    /**
     * 广告语
     */
    private String slogan;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 商品描述 文字
     */
    private String descriptionString;

    /**
     * create time
     */
    private LocalDateTime createTime;
    /**
     * update time
     */
    private LocalDateTime updateTime;

}
