package com.cosfo.item.web.facade.converter;

import com.cosfo.item.web.domain.vo.SummerFarmCostPriceVO;
import net.summerfarm.client.resp.product.saas.MallPrice4SaasResp;

public class SfManageConverter {
    public static SummerFarmCostPriceVO mallPrice4SaasResp2VO(MallPrice4SaasResp resp) {
        SummerFarmCostPriceVO vo = new SummerFarmCostPriceVO();
        vo.setSkuId(resp.getSkuId());
        vo.setSkuCode(resp.getSkuCode());
        vo.setPrice(resp.getPrice());
        vo.setValidTime(resp.getValidTime());
        vo.setInvalidTime(resp.getInvalidTime());
        vo.setValidType(resp.getValidType());
        return vo;
    }

}
