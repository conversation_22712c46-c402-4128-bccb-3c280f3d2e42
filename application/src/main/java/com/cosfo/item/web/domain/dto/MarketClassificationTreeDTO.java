package com.cosfo.item.web.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/10 23:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MarketClassificationTreeDTO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 名称
     */
    private String name;

    /**
     * 父类id
     */
    private Long parentId;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 子级分类
     */
    private List<MarketClassificationTreeDTO> childList;
}
