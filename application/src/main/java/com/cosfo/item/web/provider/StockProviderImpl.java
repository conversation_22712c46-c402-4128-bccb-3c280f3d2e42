package com.cosfo.item.web.provider;

import com.cofso.item.client.enums.StockRecordType;
import com.cofso.item.client.provider.StockProvider;
import com.cofso.item.client.resp.StockResp;
import com.cosfo.item.web.domain.converter.StockConvert;
import com.cosfo.item.web.domain.dto.StockDTO;
import com.cosfo.item.web.domain.service.StockDomainService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/16
 */
@DubboService
@Slf4j
public class StockProviderImpl implements StockProvider {
    @Resource
    private StockDomainService stockDomainService;

    @Override
    public DubboResponse<Boolean> increaseSelfStock(Long tenantId, StockRecordType recordType, Long itemId, Integer addAmount, String recordNo) {
        stockDomainService.increaseSelfStock(tenantId, recordType, itemId, addAmount, recordNo);
        return DubboResponse.getOK(Boolean.TRUE);
    }

    @Override
    public DubboResponse<List<StockResp>> batchQuery(Long tenantId, List<Long> itemIds) {
        List<StockDTO> stockDTOS = stockDomainService.batchQuery(tenantId, itemIds);
        List<StockResp> stockResps = StockConvert.convertStockRespList(stockDTOS);
        return DubboResponse.getOK(stockResps);
    }
}
