package com.cosfo.item.web.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/5 13:57
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductQueryDTO {


    /**
     * 鲜沐 SKU ID
     */
    private List<Long> summerfarmSkuIds;

    /**
     * sku_id
     */
    private Long skuId;
}
