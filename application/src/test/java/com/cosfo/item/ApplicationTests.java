package com.cosfo.item;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;

import java.util.stream.Stream;

/**
 * @author: monna.chen
 * @Date: 2023/5/25 17:52
 * @Description:
 */
@SpringBootTest(classes = TestApplication.class)
public class ApplicationTests {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    void contextLoads() {
        System.out.println(applicationContext.getBeanDefinitionCount());
        Stream.of(applicationContext.getBeanDefinitionNames()).forEach(e -> System.out.println(applicationContext.getBean(e).getClass()));
    }
}
