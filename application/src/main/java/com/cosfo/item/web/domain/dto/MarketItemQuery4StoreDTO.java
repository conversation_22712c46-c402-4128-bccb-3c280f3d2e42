package com.cosfo.item.web.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/12 11:08
 * @Description:
 */
@Data
public class MarketItemQuery4StoreDTO {
    /**
     * 登录信息
     */
    private Long tenantId;
    private Long storeId;
    private Long storeAccountId;

    /**
     * item_id
     */
    private Long itemId;
    private List<Long> itemIds;
    /**
     * 标题
     */
    private String title;

    /**
     * 是否上架
     */
    private Integer onSale;

    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;
}
