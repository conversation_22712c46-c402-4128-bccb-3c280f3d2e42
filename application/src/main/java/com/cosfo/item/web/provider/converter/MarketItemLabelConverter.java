package com.cosfo.item.web.provider.converter;

import com.cofso.item.client.enums.MarketItemLabelStatusEnum;
import com.cofso.item.client.req.MarketItemLabelInsertReq;
import com.cofso.item.client.req.MarketItemLabelQueryReq;
import com.cofso.item.client.resp.MarketItemLabelResp;
import com.cosfo.item.web.domain.dto.MarketItemLabelDTO;
import com.cosfo.item.web.domain.dto.MarketItemLabelQueryDTO;
import com.cosfo.item.web.domain.vo.MarketItemLabelVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName MarketItemLabelConverter
 * @Description
 * <AUTHOR>
 * @Date 13:45 2024/5/13
 * @Version 1.0
 **/
public class MarketItemLabelConverter {

    public static MarketItemLabelDTO marketItemLabelInsertReqToDTO(MarketItemLabelInsertReq marketItemLabelInsertReq) {
        MarketItemLabelDTO itemLabelDTO = new MarketItemLabelDTO();
        itemLabelDTO.setLabelName(marketItemLabelInsertReq.getItemName());
        itemLabelDTO.setLabelStatus(marketItemLabelInsertReq.getLabelStatus() == null
                ? MarketItemLabelStatusEnum.ENABLE.getType() : marketItemLabelInsertReq.getLabelStatus());
        itemLabelDTO.setTenantId(marketItemLabelInsertReq.getTenantId());
        return itemLabelDTO;
    }

    public static MarketItemLabelQueryDTO marketItemLabelQueryReqToDTO(MarketItemLabelQueryReq marketItemLabelQueryReq) {
        MarketItemLabelQueryDTO marketItemLabelQueryDTO = new MarketItemLabelQueryDTO();
        marketItemLabelQueryDTO.setLabelName(marketItemLabelQueryReq.getLabelName());
        marketItemLabelQueryDTO.setTenantId(marketItemLabelQueryReq.getTenantId());
        return marketItemLabelQueryDTO;
    }

    public static MarketItemLabelResp marketItemLabelVOToResp(MarketItemLabelVO marketItemLabelVO) {
        MarketItemLabelResp marketItemLabelResp = new MarketItemLabelResp();
        marketItemLabelResp.setId(marketItemLabelVO.getId());
        marketItemLabelResp.setLabelName(marketItemLabelVO.getLabelName());
        marketItemLabelResp.setTenantId(marketItemLabelVO.getTenantId());
        return marketItemLabelResp;
    }

    public static List<MarketItemLabelResp> marketItemLabelVOSToResp(List<MarketItemLabelVO> marketItemLabelVOS) {
        if (CollectionUtils.isEmpty(marketItemLabelVOS)) {
            return Collections.emptyList();
        }
        List<MarketItemLabelResp> marketItemLabelResps = new ArrayList<>(marketItemLabelVOS.size());
        marketItemLabelVOS.stream().forEach(marketItemLabelVO ->
                marketItemLabelResps.add(marketItemLabelVOToResp(marketItemLabelVO)));
        return marketItemLabelResps;
    }
}
