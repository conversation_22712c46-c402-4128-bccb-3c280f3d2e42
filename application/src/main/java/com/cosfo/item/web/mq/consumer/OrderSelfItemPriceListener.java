package com.cosfo.item.web.mq.consumer;

import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.service.MarketItemOnsalePriceDealService;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 描述: 所有的变更都先发送到顺序消息队列，以防止并发更新；
 */
//@Slf4j
//@Component
//@MqOrderlyListener(topic = RocketMqConstant.Topic.ITEM_PRICE,
//  consumerGroup = RocketMqConstant.ConsumeGroup.ITEM_PRICE,
//  tag = RocketMqConstant.Tag.ITEM_PRICE
//)
@Deprecated
public class OrderSelfItemPriceListener extends AbstractMqListener<ItemChangeMessageDTO> {

    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;

    @Override
    public void process(ItemChangeMessageDTO productPricingMessageDTO) {
//        log.info("收到market item价格价格变更消息，内容：{}", JSONObject.toJSONString(productPricingMessageDTO));
//        marketItemOnsalePriceDealService.saveOrUpdateBatchMarketItemPriceAndOnsale(productPricingMessageDTO.getTenantId(), productPricingMessageDTO.getMarketItemId());
    }
}
