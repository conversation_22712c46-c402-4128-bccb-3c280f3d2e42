package com.cosfo.item.web.domain.service;

import com.alibaba.schedulerx.shade.scala.annotation.meta.param;
import com.cofso.item.client.req.MarketClassificationQueryReq;
import com.cosfo.item.common.constants.MarketConstants;
import com.cosfo.item.common.constants.NumberConstants;
import com.cosfo.item.common.result.ResultDTOEnum;
import com.cosfo.item.infrastructure.classification.dao.MarketClassificationDao;
import com.cosfo.item.infrastructure.classification.dao.MarketItemClassificationDao;
import com.cosfo.item.infrastructure.classification.dto.MarketItemClassificationQueryParam;
import com.cosfo.item.infrastructure.classification.model.MarketClassification;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationAddDTO;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationUpdateDTO;
import com.cosfo.item.web.domain.converter.MarketClassificationConvert;
import com.cosfo.item.web.domain.dto.ItemClassificationDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationQueryDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationTreeDTO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/6
 */
@Component
@Slf4j
public class MarketClassificationDomainService {
    /**
     * 一级分类父类ID
     */
    private static final Long DEFAULT_PRIMARY_ID = 0L;

    @Autowired
    private MarketClassificationDao marketClassificationDao;
    @Autowired
    private MarketItemClassificationDao marketItemClassificationDao;

    /**
     * 新增
     *
     * @param marketClassificationAddDTO
     */
    public void add(MarketClassificationAddDTO marketClassificationAddDTO) {
        checkAddParams(marketClassificationAddDTO);

        //排序处理
        Integer sort = marketClassificationDao.selectMaxSort(marketClassificationAddDTO.getParentId(), marketClassificationAddDTO.getTenantId());

        MarketClassification marketClassification = MarketClassificationConvert.convertToMarketClassification(marketClassificationAddDTO);
        marketClassification.setSort(++sort);
        marketClassificationDao.save(marketClassification);
    }

    /**
     * 新增分类校验
     *
     * @param marketClassificationAddDTO
     */
    private void checkAddParams(MarketClassificationAddDTO marketClassificationAddDTO) {
        if (Objects.isNull(marketClassificationAddDTO) || Objects.isNull(marketClassificationAddDTO.getTenantId())) {
            throw new BizException("未查询到用户信息");
        }

        if (Objects.isNull(marketClassificationAddDTO.getParentId()) && Objects.isNull(marketClassificationAddDTO.getIcon())) {
            throw new BizException("一级分类图标不能为空");
        }


        if (Objects.isNull(marketClassificationAddDTO.getName())) {
            throw new BizException("分类名称不能为空");
        }

        if(Objects.isNull(marketClassificationAddDTO.getParentId())){
            marketClassificationAddDTO.setParentId(DEFAULT_PRIMARY_ID);
        }

        //同级分类名称校验
        MarketClassificationQueryParam param = MarketClassificationQueryParam.builder().build();
        param.setTenantId(marketClassificationAddDTO.getTenantId());
        param.setName(marketClassificationAddDTO.getName());
        param.setParentId(marketClassificationAddDTO.getParentId());
        List<MarketClassification> marketClassificationList = marketClassificationDao.listByParam(param);
        if (!CollectionUtils.isEmpty(marketClassificationList)) {
            throw new BizException("当前分类名称已存在");
        }
    }

    /**
     * 更新
     *
     * @param marketClassificationUpdateDTO
     */
    public void update(MarketClassificationUpdateDTO marketClassificationUpdateDTO) {
        checkUpdateParam(marketClassificationUpdateDTO);

        MarketClassification marketClassification = marketClassificationDao.getById(marketClassificationUpdateDTO.getId());
        if (Objects.isNull(marketClassification)) {
            throw new BizException("分类Id不存在，不能进行修改");
        }

        if(Objects.nonNull(marketClassificationUpdateDTO.getName())) {
            if (!Objects.equals(marketClassification.getName(), marketClassificationUpdateDTO.getName())) {
                if (Objects.isNull(marketClassificationUpdateDTO.getParentId())) {
                    marketClassificationUpdateDTO.setParentId(marketClassification.getParentId());
                }

                //同级分类名称校验
                MarketClassificationQueryParam param = MarketClassificationQueryParam.builder()
                        .tenantId(marketClassificationUpdateDTO.getTenantId())
                        .name(marketClassificationUpdateDTO.getName())
                        .parentId(marketClassificationUpdateDTO.getParentId())
                        .build();
                List<MarketClassification> marketClassificationList = marketClassificationDao.listByParam(param);
                if (!CollectionUtils.isEmpty(marketClassificationList)) {
                    throw new BizException("当前分类名称已存在");
                }
            }
        }

        marketClassification = MarketClassificationConvert.convertToMarketClassification(marketClassificationUpdateDTO);
        marketClassificationDao.updateById(marketClassification);
    }

    /**
     * 检查更新参数
     *
     * @param marketClassificationUpdateDTO
     */
    private void checkUpdateParam(MarketClassificationUpdateDTO marketClassificationUpdateDTO) {
        if (Objects.isNull(marketClassificationUpdateDTO) || Objects.isNull(marketClassificationUpdateDTO.getTenantId())) {
            throw new BizException("未查询到用户信息");
        }

        if (Objects.isNull(marketClassificationUpdateDTO.getId())) {
            throw new BizException("分类Id不能为空");
        }
    }

    /**
     * 删除前台分类
     *
     * @param id
     * @param tenantId
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long id, Long tenantId) {
        if (Objects.isNull(tenantId)) {
            throw new BizException("未查询到用户信息");
        }

        if (Objects.isNull(id)) {
            throw new BizException("分组Id不能为空");
        }

        MarketClassification marketClassification = marketClassificationDao.getById(id);
        if (!Objects.equals(marketClassification.getParentId(), DEFAULT_PRIMARY_ID)) {
            MarketItemClassificationQueryParam param = MarketItemClassificationQueryParam.builder().classificationId(id).build();
            // 查询该二级分组是否绑定有商品
            List<MarketItemClassification> marketItemClassifications = marketItemClassificationDao.listByParam(param);
            if (!CollectionUtils.isEmpty(marketItemClassifications)) {
                throw new BizException("当前分组下有商品，不可删除");
            }

            // 删除该二级分类
            marketClassificationDao.removeById(id);
            return Boolean.TRUE;
        }

        // 一级分类商品校验
        // 查询一级分类下所有二级分类
        MarketClassificationQueryParam query = MarketClassificationQueryParam.builder().build();
        query.setTenantId(tenantId);
        query.setParentId(id);
        // 查询所有二级分类
        List<MarketClassification> secondClassificationList = marketClassificationDao.listByParam(query);
        if(!CollectionUtils.isEmpty(secondClassificationList)) {
            List<Long> classificationIds = secondClassificationList.stream().map(MarketClassification::getId).collect(Collectors.toList());
            MarketItemClassificationQueryParam param = MarketItemClassificationQueryParam.builder().classificationIds(classificationIds).build();
            // 查询该二级分组是否绑定有商品
            List<MarketItemClassification> marketItemClassifications = marketItemClassificationDao.listByParam(param);
            if (!CollectionUtils.isEmpty(marketItemClassifications)) {
                throw new BizException("当前分类下有商品，不可删除");
            }

            // 删除二级分类
            marketClassificationDao.removeByIds(classificationIds);
        }

        //删除一级分类
        marketClassificationDao.removeById(id);
        return Boolean.TRUE;
    }

    /**
     * 分组树
     *
     * @param tenantId
     * @return
     */
    public List<MarketClassificationTreeDTO> selectClassificationTree(Long tenantId) {
        // 查询所有分类
        MarketClassificationQueryParam query = MarketClassificationQueryParam.builder().build();
        query.setTenantId(tenantId);
        List<MarketClassification> marketClassifications = marketClassificationDao.listByParam(query);
        if (CollectionUtils.isEmpty(marketClassifications)) {
            return new ArrayList<>();
        }

        // 分组
        Map<Long, List<MarketClassificationTreeDTO>> listMap = marketClassifications.stream()
            .map(MarketClassificationConvert::convertToMarketClassificationTreeDTO)
            .collect(Collectors.groupingBy(MarketClassificationTreeDTO::getParentId));

        // 获取一级分组
        List<MarketClassificationTreeDTO> result = listMap.get(DEFAULT_PRIMARY_ID);
        result.forEach(e -> e.setChildList(listMap.get(e.getId())));

        return result;
    }

    /**
     * 根据分组id查询子类分组
     *
     * @param id
     * @return
     */
    public List<MarketClassification> queryChildList(Long id) {
        if (Objects.isNull(id)) {
            throw new ParamsException(ResultDTOEnum.PARAMETER_MISSING.getMessage(),ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.name());
        }
        MarketClassification marketClassification = marketClassificationDao.getById(id);
        if (Objects.isNull(marketClassification)) {
            return Collections.emptyList();
        }
        Long parentId = marketClassification.getParentId();
        if (!Objects.equals(parentId, DEFAULT_PRIMARY_ID)) {
            return Collections.singletonList(marketClassification);
        }
        // 二级分类查询
        List<MarketClassification> marketClassifications = marketClassificationDao.listByParam(MarketClassificationQueryParam.builder()
            .tenantId(marketClassification.getTenantId())
            .parentId(marketClassification.getId())
            .build());
        if (CollectionUtils.isEmpty(marketClassifications)) {
            return Collections.singletonList(marketClassification);
        }
        return marketClassifications;
    }

    /**
     * 查询item的分类信息
     *
     * @param tenantId  可为空
     * @param marketIds
     * @return
     */
    public Map<Long, ItemClassificationDTO> getItemClassification(Long tenantId, List<Long> marketIds) {
        List<MarketItemClassification> classificationList = marketItemClassificationDao.listByParam(MarketItemClassificationQueryParam.builder()
            .tenantId(tenantId)
            .marketIds(marketIds)
            .build());
        // fixme 分类信息加缓存
        Map<Long, MarketItemClassification> itemClassMap = classificationList.stream().collect(Collectors.toMap(MarketItemClassification::getMarketId, Function.identity()));

        List<Long> classIds = classificationList.stream()
            .map(MarketItemClassification::getClassificationId)
            .collect(Collectors.toList());
        List<MarketClassification> secClassList = marketClassificationDao.listByParam(MarketClassificationQueryParam.builder()
            .ids(classIds)
            .tenantId(tenantId)
            .build());
        List<Long> parentIds = secClassList.stream()
            .map(MarketClassification::getParentId)
            .collect(Collectors.toList());
        List<MarketClassification> firstClassList = marketClassificationDao.listByParam(MarketClassificationQueryParam.builder()
            .ids(parentIds)
            .tenantId(tenantId)
            .build());
        Map<Long, MarketClassification> secClassMap = secClassList.stream().collect(Collectors.toMap(MarketClassification::getId, Function.identity()));
        Map<Long, MarketClassification> firstClassMap = firstClassList.stream().collect(Collectors.toMap(MarketClassification::getId, Function.identity()));

        Map<Long, ItemClassificationDTO> returnMap = new HashMap<>();
        for (Long marketId : marketIds) {
            ItemClassificationDTO itemClassificationDTO = ItemClassificationDTO.builder()
                .marketId(marketId)
                .build();
            MarketItemClassification itemClassification = itemClassMap.get(marketId);
            if (Objects.nonNull(itemClassification)) {
                MarketClassification secClass = secClassMap.get(itemClassification.getClassificationId());
                if (Objects.nonNull(secClass)) {
                    itemClassificationDTO.setSecondClassificationId(secClass.getId());
                    itemClassificationDTO.setSecondClassificationName(secClass.getName());
                    itemClassificationDTO.setSecondTenantId(secClass.getTenantId());
                    MarketClassification firstClass = firstClassMap.get(secClass.getParentId());
                    if (Objects.nonNull(firstClass)) {
                        itemClassificationDTO.setFirstClassificationId(firstClass.getId());
                        itemClassificationDTO.setFirstClassificationName(firstClass.getName());
                        itemClassificationDTO.setFirstTenantId(firstClass.getTenantId());
                    }
                    StringBuffer classStr = new StringBuffer();
                    if (StringUtils.isNotBlank(itemClassificationDTO.getFirstClassificationName())) {
                        classStr.append(itemClassificationDTO.getFirstClassificationName());
                    }
                    if (StringUtils.isNotBlank(itemClassificationDTO.getSecondClassificationName())) {
                        classStr.append(MarketConstants.CATEGORY_SEPARATOR).append(itemClassificationDTO.getSecondClassificationName());
                    }
                    itemClassificationDTO.setClassificationFullName(classStr.toString());
                }
            }
            returnMap.put(marketId, itemClassificationDTO);
        }
        return returnMap;
    }


    public List<MarketClassificationTreeDTO> queryByChildClassificationIds(List<Long> classificationIds, Long tenantId){
        if(CollectionUtils.isEmpty(classificationIds)){
            return Collections.emptyList();
        }

        MarketClassificationQueryParam query = MarketClassificationQueryParam.builder().build();
        query.setTenantId(tenantId);
        query.setIds(classificationIds);
        List<MarketClassification> secondClassificationList = marketClassificationDao.listByParam(query);
        if(CollectionUtils.isEmpty(secondClassificationList)){
            return Collections.emptyList();
        }

        // 获取父级Id
        List<Long> parentIds = secondClassificationList.stream().map(MarketClassification::getParentId).collect(Collectors.toList());
        query.setIds(parentIds);
        List<MarketClassification> firstClassificationList = marketClassificationDao.listByParam(query);
        // 分组
        Map<Long, List<MarketClassificationTreeDTO>> listMap = secondClassificationList.stream()
                .map(MarketClassificationConvert::convertToMarketClassificationTreeDTO)
                .collect(Collectors.groupingBy(MarketClassificationTreeDTO::getParentId));

        List<MarketClassificationTreeDTO> marketClassificationTreeDTOS = firstClassificationList.stream().map(marketClassification -> {
            MarketClassificationTreeDTO marketClassificationTreeDTO = MarketClassificationConvert.convertToMarketClassificationTreeDTO(marketClassification);
            marketClassificationTreeDTO.setChildList(listMap.get(marketClassification.getId()));
            return marketClassificationTreeDTO;
        }).collect(Collectors.toList());
        return marketClassificationTreeDTOS;
    }

    /**
     * 查询完整类目
     *
     * @param tenantId
     * @param marketId
     * @return
     */
    public MarketClassificationDTO selectWholeClassification(Long tenantId, Long marketId){
        if (Objects.isNull(tenantId) || Objects.isNull(marketId)) {
            throw new ParamsException("查询分组参数错误");
        }

        MarketItemClassificationQueryParam param = MarketItemClassificationQueryParam.builder().build();
        param.setTenantId(tenantId);
        param.setMarketId(marketId);
        List<MarketItemClassification> marketItemClassifications = marketItemClassificationDao.listByParam(param);
        if(CollectionUtils.isEmpty(marketItemClassifications)){
            return null;
        }

        MarketItemClassification marketItemClassification = marketItemClassifications.get(NumberConstants.ZERO);
        MarketClassification secondClassification = marketClassificationDao.getById(marketItemClassification.getClassificationId());
        if (Objects.isNull(secondClassification)) {
            return null;
        }

        MarketClassificationDTO marketClassificationDTO = new MarketClassificationDTO();
        marketClassificationDTO.setSecondClassificationName(secondClassification.getName());
        MarketClassification firstClassification = marketClassificationDao.getById(marketItemClassification.getClassificationId());
        marketClassificationDTO.setFirstClassificationName(Objects.isNull(firstClassification) ? "" : firstClassification.getName());
        return marketClassificationDTO;
    }

    public List<MarketClassificationDTO>  queryByCondition(MarketClassificationQueryDTO marketClassificationQueryDTO){
        MarketClassificationQueryParam param = new MarketClassificationQueryParam();
        param.setIds(marketClassificationQueryDTO.getClassificationIds());
        param.setName(marketClassificationQueryDTO.getName());
        param.setTenantId(marketClassificationQueryDTO.getTenantId());
        param.setParentId(marketClassificationQueryDTO.getParentId());
        param.setId(marketClassificationQueryDTO.getId());
        List<MarketClassification> marketClassifications = marketClassificationDao.listByParam(param);
        if(CollectionUtils.isEmpty(marketClassifications)){
            return Collections.emptyList();
        }

        List<MarketClassificationDTO> marketClassificationDTOS = MarketClassificationConvert.convertToMarketClassificationDTOList(marketClassifications);
        return marketClassificationDTOS;
    }
}
