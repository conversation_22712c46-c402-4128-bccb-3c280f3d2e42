package com.cosfo.item.web.domain.service;

import com.cosfo.item.infrastructure.item.dao.MarketItemLabelDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemLabelQueryParam;
import com.cosfo.item.infrastructure.item.model.MarketItemLabel;
import com.cosfo.item.web.domain.converter.MarketItemDetailConverter;
import com.cosfo.item.web.domain.dto.MarketItemLabelDTO;
import com.cosfo.item.web.domain.dto.MarketItemLabelQueryDTO;
import com.cosfo.item.web.domain.vo.MarketItemLabelVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @ClassName MarketItemLabelDomainService
 * @Description 商品标签
 * <AUTHOR>
 * @Date 13:38 2024/5/13
 * @Version 1.0
 **/
@Component
@Slf4j
public class MarketItemLabelDomainService {

    @Autowired
    private MarketItemLabelDao marketItemLabelDao;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;


    public MarketItemLabelVO saveMarketItemLabel(MarketItemLabelDTO marketItemLabelDTO) {
        //先查询是否已经存在该名称
        List<MarketItemLabel>  marketItemLabels = marketItemLabelDao.selectByNameAndTenantId(marketItemLabelDTO.getLabelName(), marketItemLabelDTO.getTenantId());
        if (CollectionUtils.isNotEmpty(marketItemLabels)){
            throw new BizException("标签名称已经存在无需重复插入！");
        }
        MarketItemLabel marketItemLabel = new MarketItemLabel();
        marketItemLabel.setLabelName(marketItemLabelDTO.getLabelName());
        marketItemLabel.setTenantId(marketItemLabelDTO.getTenantId());
        marketItemLabel.setLabelStatus(marketItemLabelDTO.getLabelStatus());
        marketItemLabelDao.save(marketItemLabel);
        return MarketItemDetailConverter.convert2MarketItemLabelVO(marketItemLabel);
    }

    public List<MarketItemLabelVO> queryMarketItemLabel(MarketItemLabelQueryDTO marketItemLabelQueryDTO) {
        MarketItemLabelQueryParam param = MarketItemDetailConverter.convertMarketItemLabel2Param(marketItemLabelQueryDTO);
        List<MarketItemLabel> marketItemLabels = marketItemLabelDao.queryMarketItemLabel(param);
        if (CollectionUtils.isEmpty(marketItemLabels)) {
            return Collections.emptyList();
        }
        return MarketItemDetailConverter.convert2MarketItemLabelVOList(marketItemLabels);
    }

}
