package com.cosfo.item.infrastructure.offline.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 数据同步信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Getter
@Setter
@TableName("data_synchronization_information")
public class DataSynchronizationInformation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 表名
     */
    private String tName;

    /**
     * 同步时间标记(yyyyMMdd/yyyyMM)
     */
    private Integer dateFlag;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * create time
     */
    private LocalDateTime createTime;


}
