package com.cosfo.item.web.domain.dto;

import com.cofso.item.client.req.MarketItemInfoQueryFlagReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/4/27 14:29
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemCommonQueryDTO implements Serializable {
    private static final long serialVersionUID = 5447659493205412989L;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品编码
     */
    private Long itemId;
    private List<Long> itemIds;

    /**
     * tenant_id
     */
    private Long tenantId;
    /**
     * market_id
     */
    private Long marketId;
    private List<Long> marketIds;
    /**
     * 货源类型
     */
    private Integer goodsType;
    private List<Integer> goodsTypes;

    /**
     * 查询非组合品
     */
    private Boolean combineFlag;
    /**
     * 上架状态
     */
    private Integer onSale;
    /**
     * 删除标识 0、已删除 1、正在使用
     */
    private Integer deleteFlag;

    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;
    
    /**
     * market item 的一些查询条件
     */
    private MarketItemInfoQueryFlagDTO marketItemInfoQueryFlagDTO;
    /**
     * 前台分类
     */
    private Long classificationId;

    /**
     * 后台类目
     */
    private List<Long> categoryIds;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 自有编码模糊查询
     */
    private String itemCodeLike;
    /**
     * xm大客户Id
     */
    private Long adminId;
    /**
     *  鲜沐商品类型 0 自营 1 代仓
     */
    private Integer characters;

    /**
     * 商品预售开关 0-不可预售 1-可预售
     */
    private Integer presaleSwitch;

    /**
     * 外部ID -- pdId
     */
    private Long outId;
}
