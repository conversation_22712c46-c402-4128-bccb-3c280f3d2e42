package com.cosfo.item.web.provider.converter;

import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cosfo.item.web.domain.dto.ItemClassificationDTO;
import com.cosfo.item.web.domain.vo.MarketItemClassificationVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 类目对象转换
 * @author: xiaowk
 * @date: 2023/5/16 下午4:46
 */
@Mapper
public interface MarketItemClassificationConvert {

    MarketItemClassificationConvert INSTANCE = Mappers.getMapper(MarketItemClassificationConvert.class);

    MarketItemClassificationResp convert2MarketItemClassificationResp(MarketItemClassificationVO vo);

    @Mapping(source = "firstTenantId",target = "tenantId")
    MarketItemClassificationResp convert2MarketItemClassificationResp(ItemClassificationDTO dt0);

}