package com.cosfo.item.infrastructure.item.dto;

import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/11 18:17
 * @Description: 门店查询商品参数
 */
@Data
public class MarketItemStoreQueryParam {
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 门店ID
     * 门店ID为空时认为是未登录状态
     */
    private Long storeId;
    /**
     * 标题
     */
    private String title;
    /**
     * 分类id
     */
    private Long classificationId;
    /**
     * market_id
     */
    private Long itemId;
    private List<Long> itemIds;
    /**
     * market_id
     */
    private List<Long> marketIds;

    /**
     * 是否上架
     */
    private Integer onSale;

    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;
}
