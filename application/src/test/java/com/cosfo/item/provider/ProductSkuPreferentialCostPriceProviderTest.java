package com.cosfo.item.provider;

import com.alibaba.fastjson.JSON;
import com.cofso.item.client.provider.MarketProvider;
import com.cofso.page.PageResp;
import com.cofso.preferential.client.provider.ProductSkuPreferentialCostPriceProvider;
import com.cofso.preferential.client.req.ProductSkuPreferentialQueryReq;
import com.cofso.preferential.client.resp.ProductSkuPreferentialPageResp;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2024-02-26
 * @Description:
 */
@SpringBootTest
public class ProductSkuPreferentialCostPriceProviderTest {

    @Resource
    private ProductSkuPreferentialCostPriceProvider costPriceProvider;

    @Test
    public void pagePreferentialCostPrice(){

        ProductSkuPreferentialQueryReq req = new ProductSkuPreferentialQueryReq();
        req.setTenantId(2L);
        req.setPageIndex(1);
        req.setPageSize(10);
        DubboResponse<PageResp<ProductSkuPreferentialPageResp>> pageRespDubboResponse = costPriceProvider.pagePreferentialCostPrice(req);

        System.err.println(JSON.toJSONString(pageRespDubboResponse));
    }
}
