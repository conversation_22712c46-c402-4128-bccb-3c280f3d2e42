package com.cosfo.item.web.domain.converter;

import com.cosfo.item.infrastructure.item.model.MarketItemUnit;
import com.cosfo.item.web.domain.dto.MarketItemUnitDTO;
import com.cosfo.item.web.domain.vo.MarketItemUnitVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface MarketItemUnitConvert {
    MarketItemUnitConvert instance = Mappers.getMapper(MarketItemUnitConvert.class);

    List<MarketItemUnitVO> MarketItemUnit2VOList(List<MarketItemUnit> list);

    List<MarketItemUnit> dto2VOEntity(List<MarketItemUnitDTO> marketItemUnitList);

}
