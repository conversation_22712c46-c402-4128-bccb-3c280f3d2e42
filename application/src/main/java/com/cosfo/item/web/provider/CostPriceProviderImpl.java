package com.cosfo.item.web.provider;

import com.cofso.item.client.provider.CostPriceProvider;
import com.cofso.item.client.req.CostPriceQueryRangeReq;
import com.cofso.item.client.req.CostPriceQueryReq;
import com.cofso.item.client.resp.CostPriceRangeResultResp;
import com.cofso.item.client.resp.CostPriceResultResp;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.vo.CostPriceRangeVO;
import com.cosfo.item.web.domain.vo.CostPriceVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import com.cosfo.item.web.provider.converter.CostPricePrividerConverter;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class CostPriceProviderImpl implements CostPriceProvider {

    @Resource
    private CostPriceDomianService costPriceDomianService;

    @Override
    @Deprecated
    public DubboResponse<List<CostPriceResultResp>> queryCostPriceByAddressAndSkuIds(CostPriceQueryReq req) {
        List<CostPriceVO> costPriceVOS = costPriceDomianService.listCostPriceByCityAreaAndTenantId(CostPricePrividerConverter.costPriceQueryReq2DTO(req));
        List<CostPriceResultResp> result = costPriceVOS.stream().map(e -> CostPricePrividerConverter.costPriceVO2Resp(e)).collect(Collectors.toList());
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<List<CostPriceRangeResultResp>> queryCostPriceRange(List<CostPriceQueryRangeReq> req, Long tenantId) {
        List<CostPriceRangeVO> costPriceRangeVOS = costPriceDomianService.queryCostPriceRange(req.stream ().map (CostPricePrividerConverter::costPriceQueryRangeReq2DTO).collect(Collectors.toList()),tenantId);
        List<CostPriceRangeResultResp> result = costPriceRangeVOS.stream().map(e -> CostPricePrividerConverter.costPriceRangeVO2Resp(e)).collect(Collectors.toList());
        return DubboResponse.getOK(result);
    }
}
