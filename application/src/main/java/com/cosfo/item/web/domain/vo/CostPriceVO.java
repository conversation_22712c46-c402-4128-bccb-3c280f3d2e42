package com.cosfo.item.web.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CostPriceVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * xm货品主键
     */
    private Long skuId;

    /**
     * ft货品主键
     */
    private Long ftSkuId;

    /**
     * 货品编码
     */
    private String skuCode;

    /**
     * 区域
     */
    private String area;

    /**
     * 城市
     */
    private String city;

    /**
     * 省
     */
    private String province;

    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 城市Id
     */
    private Integer cityId;

    private String addressKey;
}