<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.price.mapper.MarketItemPriceLogMapper">
  <insert id="batchInsert">
    INSERT INTO `market_item_price_log`
    (`tenant_id`,`target_type`,`target_id`,`market_item_id`,`sku_id`,`price`,`price_strategy`,`base_price`,`ops_type`,`price_rule`,`create_time`)
    VALUES
    <foreach collection="collection" item="item" separator=",">
      (#{item.tenantId}, #{item.targetType}, #{item.targetId}, #{item.marketItemId}, #{item.skuId},
      #{item.price},#{item.priceStrategy},#{item.basePrice},#{item.opsType},#{item.priceRule},now())
    </foreach>
  </insert>
</mapper>
