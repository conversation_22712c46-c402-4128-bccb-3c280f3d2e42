package com.cosfo.item.web.facade;

import com.cosfo.item.web.facade.converter.SfManageConverter;
import net.summerfarm.client.provider.product.saas.SaasInterfaceServiceProvider;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.web.domain.vo.SummerFarmCostPriceVO;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.client.resp.product.saas.MallPrice4SaasResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SfMallManageFacade {
    @DubboReference
    private SaasInterfaceServiceProvider saasInterfaceServiceProvider;

    public SummerFarmCostPriceVO queryMallPriceInfo4Saas(Integer areaNo, String sku, Long adminId){
        DubboResponse<MallPrice4SaasResp> response = saasInterfaceServiceProvider.queryMallPriceInfo4Saas( areaNo,  sku,  adminId);
        if (!response.isSuccess()){
            throw new BizException (response.getMsg());
        }
        MallPrice4SaasResp data = response.getData();
        if(!ObjectUtil.isEmpty(data)){
            return SfManageConverter.mallPrice4SaasResp2VO(data);
        }
        return null;
    }
}
