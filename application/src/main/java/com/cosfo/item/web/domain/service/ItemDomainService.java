package com.cosfo.item.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.enums.*;
import com.cofso.item.client.req.*;
import com.cofso.item.client.resp.*;
import com.cofso.item.client.resp.BatchChangNoGoodsSupplyPriceVO;
import com.cofso.item.client.result.ResultCodeEnum;
import com.cofso.page.PageResp;
import com.cosfo.item.common.constants.MarketCombineConstants;
import com.cosfo.item.common.constants.MarketConstants;
import com.cosfo.item.common.constants.NumberConstants;
import com.cosfo.item.common.constants.RedisKeyConstants;
import com.cosfo.item.common.constants.StringsConstantsUtil;
import com.cosfo.item.common.dto.PriceRangeDTO;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemPriceEnum;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.common.enums.ProductEnum;
import com.cosfo.item.common.enums.StockEnum;
import com.cosfo.item.common.result.ResultDTOEnum;
import com.cosfo.item.infrastructure.classification.dao.MarketItemClassificationDao;
import com.cosfo.item.infrastructure.classification.dto.MarketItemClassificationQueryParam;
import com.cosfo.item.infrastructure.classification.model.MarketClassification;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;
import com.cosfo.item.infrastructure.item.dao.*;
import com.cosfo.item.infrastructure.item.dto.*;
import com.cosfo.item.infrastructure.item.model.*;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyDao;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceStrategyDTO;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.web.domain.converter.*;
import com.cosfo.item.web.domain.dto.*;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.MerchantStoreFacade;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.converter.MerchantStoreConverter;
import com.cosfo.item.web.provider.converter.MarketConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cofso.item.client.result.ResultCodeEnum.ParamsResultCodeEnum.buildParamsException;
import static com.cosfo.item.common.constants.NumberConstants.BATCH_ON_SALE_LIMIT;
import static com.cosfo.item.common.result.ResultDTOEnum.TENANT_MATCH_ERROR;

/**
 * 商品
 */
@Component
@Slf4j
public class ItemDomainService {

    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private MarketDao marketDao;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private MarketItemClassificationDao marketItemClassificationDao;
    @Autowired
    private StockDao stockDao;
    @Autowired
    private StockRecordDao stockRecordDao;
    @Autowired
    private MarketItemDetailDao marketItemDetailDao;
    @Autowired
    private MarketDetailDao marketDetailDao;
    @Autowired
    private MarketItemPriceDomianService marketItemPriceDomianService;
    @Autowired
    private MarketItemOnsaleStrategyDomainService onSaleStrategyDomainService;
    @Autowired
    private MarketItemPriceStrategyDomainService priceStrategyDomainService;
    @Autowired
    private PriceDomianService priceDomianService;
    @Autowired
    private MarketCombineDomainService combineDomainService;
    @Autowired
    private MarketCombineItemMappingDao marketCombineItemMappingDao;
    @Autowired
    private MarketItemPriceStrategyDao strategyDao;
    @Autowired
    private MarketItemOnsalePriceDealService dealService;
    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private MerchantStoreFacade storeFacade;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    @Qualifier("asyncTaskExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Resource
    private MarketClassificationDomainService classificationDomainService;
    @Resource
    private UnfairPriceStrategyDomainService unfairPriceStrategyDomainService;
    @Autowired
    private MarketItemLabelDao marketItemLabelDao;
    @Autowired
    private MarketItemUnitDomanService itemUnitDomanService;

    /**
     * 根据货品idlist 查询商品list
     *
     * @param skuIds
     */
    public List<MarketItemVO> listMarketItemBySkuIdsAndTypes(Long tenantId, Set<Long> skuIds, List<Integer> goodsType) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        List<MarketItem> list = marketItemDao.listBySkuIdsAndTypes(tenantId, skuIds, goodsType);
        return list.stream().map(ItemConverter::marketItem2VO).collect(Collectors.toList());
    }

    /**
     * 根据货品idlist 查询商品list
     */
    public List<MarketItemVO> listMarketItemBySkuIdAndTenantIdAndGoodsTypes(Long skuId, Long tenantId, List<Integer> goodsTypes) {
        if (Objects.isNull(skuId)) {
            return Collections.emptyList();
        }
        List<MarketItem> list = marketItemDao.listBySkuIdAndTenantIdAndGoodsTypes(skuId, tenantId, goodsTypes);
        return list.stream().map(ItemConverter::marketItem2VO).collect(Collectors.toList());
    }

    /**
     * 根据tenantid查询所有商品
     *
     * @param tenantId
     * @return
     */
    public List<MarketItemVO> listMarketItemByTenantIdAndType(Long tenantId, Integer goodsType) {
        if (ObjectUtil.isEmpty(tenantId)) {
            return Collections.emptyList();
        }
        List<MarketItem> list = marketItemDao.listByTenantIdAndType(tenantId, goodsType);
        if (list.size() > 500) {
            log.error("listMarketItemByTenantIdAndType数据量超过500，尽快优化，tenantId={}", tenantId);
        }
        return list.stream().map(ItemConverter::marketItem2VO).collect(Collectors.toList());
    }

    /**
     * 根据tenantid查询所有商品
     *
     * @param tenantId
     * @return
     */
    public List<MarketItemVO> listMarketItemByTenantIdAndTypes(Long tenantId, List<Integer> goodsTypeList) {
        if (ObjectUtil.isEmpty(tenantId)) {
            return Collections.emptyList();
        }
        List<MarketItem> list = marketItemDao.listByTenantIdAndTypes(tenantId, goodsTypeList);
        if (list.size() > 500) {
            log.error("listMarketItemByTenantIdAndTypes数据量超过500，尽快优化，tenantId={}", tenantId);
        }
        return list.stream().map(ItemConverter::marketItem2VO).collect(Collectors.toList());
    }

    /**
     * 批量新增market_item
     *
     * @param itemDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveItem(MarketItemDTO itemDTO) {
        checkMarketItem(itemDTO, StringsConstantsUtil.ADD_OPERATE);
        String sku = null;
        // 保存销售商品信息
        ProductSkuVO productSkuVO = dealProductInfo (itemDTO);
        if(!Objects.isNull (productSkuVO)){
            sku = productSkuVO.getSku ();
        }
        // 保存销售商品区域信息
        MarketItem marketItem = MarketDomainConvert.INSTANCE.convert2Entity(itemDTO);
        marketItemDao.save(marketItem);

        // 保存market_item_detail
        if (Objects.nonNull(itemDTO.getOutId())) {
            List<MarketItemDetail> marketItemDetails = marketItemDetailDao.listByParam(MarketItemDetailParam.builder()
                .outId(itemDTO.getOutId())
                .build());
            if (CollectionUtils.isNotEmpty(marketItemDetails)) {
                throw buildParamsException(ResultCodeEnum.ParamsResultCodeEnum.ITEM_EXIST);
            }
            MarketItemDetail marketItemDetail = MarketDomainConvert.INSTANCE.convert2MarketItemDetail(itemDTO);
            marketItemDetail.setMarketItemId(marketItem.getId());
            marketItemDetailDao.save(marketItemDetail);
        }

        // 无货商品记录库存
        if (GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(itemDTO.getGoodsType())) {
            Stock stock = new Stock();
            stock.setTenantId(itemDTO.getTenantId());
            stock.setItemId(marketItem.getId());
            stock.setAmount(itemDTO.getAmount());
            stockDao.save(stock);
            StockRecord record = new StockRecord();
            record.setTenantId(itemDTO.getTenantId());
            record.setStockSkuId(marketItem.getId());
            record.setType(StockEnum.StockRecordType.SAVE_NEW.getType());
            record.setBeforeAmount(NumberConstants.ZERO);
            record.setChangeAmount(itemDTO.getAmount());
            record.setAfterAmount(itemDTO.getAmount());
            stockRecordDao.save(record);
        }

        // 保存价格策略及上下架策略
        if (CollectionUtils.isNotEmpty(itemDTO.getPriceList())) {
            for (MarketItemPriceStrategyDTO price : itemDTO.getPriceList()) {
                price.setItemId(marketItem.getId());
            }
            priceStrategyDomainService.saveOrUpdateMarketItemPriceStrategByItemId(itemDTO.getTenantId(), itemDTO.getPriceList(),itemDTO.getGoodsType (),sku);
        }

        unfairPriceStrategyDomainService.upsertUnfairPriceStrategy(itemDTO.getMarketItemUnfairPriceStrategyDTO(), marketItem.getTenantId(), marketItem.getId());

        // 单位
        if (CollectionUtils.isNotEmpty(itemDTO.getMarketItemUnitList ())) {
            itemUnitDomanService.saveOrUpdateMarketItemUnitByItemId (itemDTO.getTenantId (),marketItem.getId (), itemDTO.getMarketItemUnitList ());
        }

        return marketItem.getId();
    }

    private ProductSkuVO dealProductInfo(MarketItemDTO itemDTO) {
        if (Objects.isNull(itemDTO.getSkuId())) {
            return null;
        }
        // 查询货品信息
        ProductSkuVO productSkuVO = productFacade.querySkuInfo(itemDTO.getSkuId());
        if (Objects.isNull(productSkuVO)) {
            throw new BizException(ResultDTOEnum.PRODUCT_NOT_FOUND.getCode(), ResultDTOEnum.PRODUCT_NOT_FOUND.getMessage());
        }

        itemDTO.setSpecification(productSkuVO.getSpecification());
        itemDTO.setSpecificationUnit(productSkuVO.getSpecificationUnit());
        itemDTO.setBrandName(productSkuVO.getBrandName());
        // 更新货品或者报价单表是否关联关系字段
        if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(itemDTO.getGoodsType())) {
            productFacade.updateAssociated(itemDTO.getSkuId(), itemDTO.getTenantId(), NumberConstants.ONE);
        } else if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(itemDTO.getGoodsType())) {
            productFacade.updateSupplyAssociated(itemDTO.getSkuId(), itemDTO.getTenantId(), NumberConstants.ONE);
        }
        return productSkuVO;
    }

    /**
     * 校验参数
     *
     * @param itemDTO
     */
    private void checkMarketItem(MarketItemDTO itemDTO, String operateType) {
        if (Objects.isNull(itemDTO.getTenantId())) {
            throw new ParamsException("租户ID不能为空");
        }
        if (xmTenantId.equals(itemDTO.getTenantId()) && Objects.isNull(itemDTO.getOutId())) {
            throw new ParamsException("鲜沐ID不可为空");
        }
        if (Objects.isNull(itemDTO.getGoodsType())) {
            throw new ParamsException("货源类型不能为空");
        }
        // 鲜沐商品更新时不会改变skuID，所以不会传sku
        if (!xmTenantId.equals(itemDTO.getTenantId()) && Objects.isNull(itemDTO.getSkuId()) && GoodsTypeEnum.THIRD_DELIVERY_CODES.contains(itemDTO.getGoodsType())) {
            throw new ParamsException(ResultDTOEnum.SKU_ID_NOT_NULL.getMessage(), ResultDTOEnum.SKU_ID_NOT_NULL.getCode(), ResultDTOEnum.SKU_ID_NOT_NULL.name());
        }
        // 可以有多条价格及多条上下架策略，保证上架size=价格Size
        if (OnSaleTypeEnum.ON_SALE.getCode().equals(itemDTO.getOnSale()) && CollectionUtils.isEmpty(itemDTO.getPriceList())) {
            throw new ParamsException(ResultDTOEnum.PRICE_NOT_NULL.getMessage(), ResultDTOEnum.PRICE_NOT_NULL.getCode(), ResultDTOEnum.PRICE_NOT_NULL.name());
        }

        if (Objects.isNull(itemDTO.getAfterSaleUnit()) || Objects.isNull(itemDTO.getMaxAfterSaleAmount())) {
            throw new ParamsException("售后信息不能为空");
        }


        // 鲜沐变更item时没有sku
        // 鲜沐同步时只保存 纯鲜沐代仓品
        if (xmTenantId.equals(itemDTO.getTenantId())) {
            if (StringsConstantsUtil.ADD_OPERATE.equals(operateType)) {
                ProductAgentSkuMappingVO skuMappingVO = productFacade.getProductMappingBySkuIdAndTenantId(itemDTO.getSkuId(), itemDTO.getTenantId(), itemDTO.getTenantId());
                if (Objects.isNull(skuMappingVO)) {
                    throw new BizException("该鲜沐品无需同步");
                }
            }
        } else {
            // saas商品校验映射是否存在
            if (Objects.nonNull(itemDTO.getSkuId()) && GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(itemDTO.getGoodsType())) {
                List<ProductAgentSkuMappingVO> skuMappingVOS = productFacade.listProductMappingBySkuIdsAndTenantId(Collections.singleton(itemDTO.getSkuId()), itemDTO.getTenantId());
                if (CollectionUtils.isEmpty(skuMappingVOS)) {
                    throw new BizException("该自营货品暂时无法绑定商品");
                }
            }
        }

        // 校验sku价格策略是否正常
        checkSkuPrice(itemDTO.getPriceList(), itemDTO.getTenantId());

        // 校验前台分组是否填写，如果未填写，需要提示
        if (!xmTenantId.equals(itemDTO.getTenantId())) {
            List<MarketItemClassification> marketItemClassificationDTOs = marketItemClassificationDao
                .listByParam(MarketItemClassificationQueryParam.builder()
                    .tenantId(itemDTO.getTenantId())
                    .marketId(itemDTO.getMarketId())
                    .build());
            if (OnSaleTypeEnum.ON_SALE.getCode().equals(itemDTO.getOnSale()) && CollectionUtils.isEmpty(marketItemClassificationDTOs)) {
                throw new BizException("该商品前台分类未填写，不能进行上架。");
            }
        }
    }

    /**
     * 校验价格策略
     * <p>
     * 校验规则：
     * 1.默认规则：只有一条，且target_type = 品牌方
     * 2.门店规则：有多条，门店ID不为空。同一门店只能存在于一个规则中。
     * 3.门店分组：有多条，分组ID不为空。同一门店分组只能存在于一个规则中。
     *
     * @param priceList
     */
    private void checkSkuPrice(List<MarketItemPriceStrategyDTO> priceList, Long tenantId) {
        if (Objects.equals(xmTenantId, tenantId)) {
            return;
        }
        if (CollectionUtils.isEmpty(priceList)) {
            throw new ParamsException("价格策略不可为空！");
        }
        Map<Integer, List<MarketItemPriceStrategyDTO>> targetTypeMap = priceList.stream().collect(Collectors.groupingBy(MarketItemPriceStrategyDTO::getTargetType));
        for (Integer key : targetTypeMap.keySet()) {
            PriceTargetTypeEnum targetType = PriceTargetTypeEnum.getTypeByCode(key);
            if (Objects.isNull(key)) {
                throw new ParamsException(ResultDTOEnum.TARGET_TYPE_ERROR.getMessage(), ResultDTOEnum.TARGET_TYPE_ERROR.getCode(), ResultDTOEnum.TARGET_TYPE_ERROR.name());
            }
            List<MarketItemPriceStrategyDTO> priceStrategyDTOList = targetTypeMap.get(key);
            if (CollectionUtils.isEmpty(priceStrategyDTOList)) {
                continue;
            }
            switch (targetType) {
                case TENANT:
                    if (1 != priceStrategyDTOList.size()) {
                        throw new ParamsException("默认价格只能有一条");
                    }
                    if (CollectionUtils.isEmpty(priceStrategyDTOList.get(0).getTargetIds()) || !tenantId.equals(priceStrategyDTOList.get(0).getTargetIds().get(0))) {
                        throw new ParamsException("默认价格的目标对象只能是当前租户");
                    }
                    break;

                case STORE:
                    List<Long> storeIds = new ArrayList<>();
                    for (MarketItemPriceStrategyDTO priceStrategyDTO : priceStrategyDTOList) {
                        if (CollectionUtils.isEmpty(priceStrategyDTO.getTargetIds())) {
                            throw new ParamsException("门店列表不可为空");
                        }
                        storeIds.addAll(priceStrategyDTO.getTargetIds());
                    }
                    Set<Long> storeIdsSet = new HashSet<>(storeIds);
                    if (storeIdsSet.size() != storeIds.size()) {
                        throw new ParamsException(ResultDTOEnum.PRICE_STRATEGY_STORE_NOT_REPEAT.getMessage(), ResultDTOEnum.PRICE_STRATEGY_STORE_NOT_REPEAT.getCode(),
                            ResultDTOEnum.PRICE_STRATEGY_STORE_NOT_REPEAT.name());
                    }
                    break;

                case STORE_GROUP:
                    List<Long> storeGroupIds = new ArrayList<>();
                    for (MarketItemPriceStrategyDTO priceStrategyDTO : priceStrategyDTOList) {
                        if (CollectionUtils.isEmpty(priceStrategyDTO.getTargetIds())) {
                            throw new ParamsException("门店分组列表不可为空");
                        }
                        storeGroupIds.addAll(priceStrategyDTO.getTargetIds());
                    }
                    Set<Long> storeGroupIdSet = new HashSet<>(storeGroupIds);
                    if (storeGroupIdSet.size() != storeGroupIds.size()) {
                        throw new ParamsException(ResultDTOEnum.PRICE_STRATEGY_STORE_GROUP_NOT_REPEAT.getMessage(), ResultDTOEnum.PRICE_STRATEGY_STORE_GROUP_NOT_REPEAT.getCode(),
                            ResultDTOEnum.PRICE_STRATEGY_STORE_GROUP_NOT_REPEAT.name());
                    }
                    break;

                default:
                    break;
            }
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateMarketItem(MarketItemDTO itemDTO) {
        Long tenantId = itemDTO.getTenantId();
        checkMarketItem(itemDTO, StringsConstantsUtil.UPDATE_OPERATE);

        // 保存market_item_detail
        if (Objects.nonNull(itemDTO.getOutId())) {
            List<MarketItemDetail> marketItemDetails = marketItemDetailDao.listByParam(MarketItemDetailParam.builder()
                .outId(itemDTO.getOutId())
                .build());
            if (CollectionUtils.isEmpty(marketItemDetails)) {
                throw new ParamsException("未找到商品,请先保存.");
            }
            MarketItemDetail marketItemDetail = MarketDomainConvert.INSTANCE.convert2MarketItemDetail(itemDTO);
            marketItemDetail.setId(marketItemDetails.get(0).getId());
            itemDTO.setMarketItemId(marketItemDetails.get(0).getMarketItemId());
            marketItemDetailDao.saveOrUpdate(marketItemDetail);
        }

        MarketItem item = marketItemDao.getById(itemDTO.getMarketItemId());
        if (!Objects.equals(xmTenantId, item.getTenantId()) && MarketItemEnum.DeleteFlagEnum.DELETED.getFlag().equals(item.getDeleteFlag())) {
            throw new BizException("该商品已被删除，不可编辑！");
        }

        // 处理货品信息
        String sku = null;
        ProductSkuVO productSkuVO = dealProductInfo (itemDTO);
        if(!Objects.isNull (productSkuVO)){
            sku = productSkuVO.getSku ();
        }

        if (xmTenantId.equals(itemDTO.getTenantId())) {
            itemDTO.setSkuId(item.getSkuId());
        }
        itemDTO.setMarketId(item.getMarketId());
        Long oldSkuId = item.getSkuId();
        Integer oldGoodsType = item.getGoodsType();
        // 更新销售商品信息
        MarketItem marketItem = MarketDomainConvert.INSTANCE.convert2Entity(itemDTO);
        if (Objects.isNull(marketItem.getSkuId())) {
            marketItem.setSkuId(-1L);
        }
        marketItemDao.updateByIdWithNullAttribute(marketItem);
        switchGoodsTypeClean(marketItem, oldGoodsType);
        // 更新货品或者报价单表是否关联关系字段
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(item.getTenantId(),Collections.singletonList (oldSkuId));
        Integer associated = CollectionUtils.isEmpty(marketItems) ? NumberConstants.ZERO : NumberConstants.ONE;
        if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(oldGoodsType)) {
            productFacade.updateAssociated(oldSkuId, itemDTO.getTenantId(), associated);
        } else if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(oldGoodsType)) {
            productFacade.updateSupplyAssociated(oldSkuId, itemDTO.getTenantId(), associated);
        }

        updateStock(itemDTO, tenantId, oldGoodsType);

        // 防倒挂策略
        unfairPriceStrategyDomainService.upsertUnfairPriceStrategy(itemDTO.getMarketItemUnfairPriceStrategyDTO(), marketItem.getTenantId(), marketItem.getId());

        // 定价策略配置 上下架策略
        if (CollectionUtils.isNotEmpty(itemDTO.getPriceList())) {
            priceStrategyDomainService.saveOrUpdateMarketItemPriceStrategByItemId(itemDTO.getTenantId(), itemDTO.getPriceList(),itemDTO.getGoodsType (),sku);
        }
        // 单位
        if (CollectionUtils.isNotEmpty(itemDTO.getMarketItemUnitList ())) {
            itemUnitDomanService.saveOrUpdateMarketItemUnitByItemId (itemDTO.getTenantId (),marketItem.getId (), itemDTO.getMarketItemUnitList ());
        }
    }

    private void switchGoodsTypeClean(MarketItem item, Integer oldGoodsType) {
        if (Objects.equals(oldGoodsType, item.getGoodsType())) {
            return;
        }
        if (!Objects.equals(GoodsTypeEnum.NO_GOOD_TYPE.getCode(), item.getGoodsType())) {
            // 切换后货源不是无货
            log.info("marketId={}货源从{}切换为{}, 清理无货数据", item.getId(), oldGoodsType, item.getGoodsType());
//            marketItemDao.cleanNoGoodsInfo(item.getId());
        }
    }

    // 更新库存
    private void updateStock(MarketItemDTO itemDTO, Long tenantId, Integer oldGoodsType) {
        // 如果是自营仓，并且是品牌仓配送
        List<Stock> stocks = stockDao.listByParam(StockQueryParam.builder()
            .tenantId(tenantId)
            .itemId(itemDTO.getMarketItemId())
            .build());

        StockRecord stockRecord = new StockRecord();
        stockRecord.setTenantId(tenantId);
        stockRecord.setStockSkuId(itemDTO.getMarketItemId());
        if (GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(itemDTO.getGoodsType())) {
            // 未变更库存
            if (Optional.ofNullable(itemDTO.getChangeQuantity()).orElse(0) == 0) {
                return;
            }
            // 查询是否已经有库存信息，如果有库存累加, 没有新建
            Integer afterAmount;
            if (GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(oldGoodsType)) {
                stockRecord.setType(StockEnum.StockRecordType.MANUALLY_ADJUST.getType());
            } else {
                stockRecord.setType(StockEnum.StockRecordType.THIRD_TO_SELF.getType());
            }

            if (CollectionUtils.isNotEmpty(stocks)) {
                Stock stock = stocks.get(0);
                afterAmount = stock.getAmount() + itemDTO.getChangeQuantity();
                stockDao.increaseStock(stock.getId(), itemDTO.getChangeQuantity());

                stockRecord.setBeforeAmount(stock.getAmount());
                stockRecord.setChangeAmount(itemDTO.getChangeQuantity());
                stockRecord.setAfterAmount(afterAmount);
                stockRecordDao.save(stockRecord);
            } else {
                afterAmount = itemDTO.getAmount() + itemDTO.getChangeQuantity();
                Stock insert = new Stock();
                insert.setTenantId(tenantId);
                insert.setItemId(itemDTO.getMarketItemId());
                insert.setAmount(afterAmount);
                stockDao.save(insert);

                stockRecord.setBeforeAmount(NumberConstants.ZERO);
                stockRecord.setChangeAmount(afterAmount);
                stockRecord.setAfterAmount(afterAmount);
                stockRecordDao.save(stockRecord);
            }
        } else if (CollectionUtils.isNotEmpty(stocks) && stocks.get(0).getAmount() > 0) {
            // 无仓切换到其它货源场景下，清空库存
            Stock stock = stocks.get(0);
            stockDao.increaseStock(stock.getId(), -stock.getAmount());
            stockRecord.setType(StockEnum.StockRecordType.SELF_TO_THIRD.getType());
            stockRecord.setBeforeAmount(stock.getAmount());
            stockRecord.setChangeAmount(stock.getAmount());
            stockRecord.setAfterAmount(NumberConstants.ZERO);
            stockRecordDao.save(stockRecord);
        }
    }

    /**
     * 初始化规格 （更新上下架状态及价格,其它规格等参数可为空） 上下架策略与价格策略 不在同一事务
     *
     * @param itemDTO
     */
    public void initMarketItem(MarketItemDTO itemDTO) {
        checkInitValid(itemDTO);

        if (CollectionUtils.isNotEmpty(itemDTO.getPriceList())) {
            priceStrategyDomainService.saveOrUpdateMarketItemPriceStrategByItemId(itemDTO.getTenantId(), itemDTO.getPriceList());
        }
        if (CollectionUtils.isNotEmpty(itemDTO.getOnSaleList())) {
            onSaleStrategyDomainService.saveOrUpdateMarketItemOnsaleStrategy(itemDTO.getTenantId(), itemDTO.getOnSaleList());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void initMarketItemPriceOnSaleStratary(Long outId, MarketItemOnsaleStrategyDTO onsaleStrategy, MarketItemPriceStrategyDTO priceStrategy) {
        List<MarketItemDetail> marketItemDetails = marketItemDetailDao.listByParam(MarketItemDetailParam.builder()
            .outId(outId)
            .build());
        if (CollectionUtils.isEmpty(marketItemDetails)) {
            throw new ParamsException("未找到指定item detail");
        }
        MarketItem marketItem = marketItemDao.getById(marketItemDetails.get(0).getMarketItemId());
        if (Objects.isNull(marketItem)) {
            throw new ParamsException("未找到指定item");
        }
        Long tenantId = marketItem.getTenantId();
        if (ObjectUtil.isNotEmpty(priceStrategy)) {
            priceStrategy.setItemId(marketItem.getId());
            MarketItemPriceStrategyMappingDetailVO vo = priceStrategyDomainService.listMarketItemPriceStrategyByItemIdAndTargetId(tenantId, marketItem.getId(),
                MarketItemPriceStrategyEnum.TargetTypeEnum.AREA_NO.getCode(), priceStrategy.getTargetIds().get(0));
            if (ObjectUtil.isNotEmpty(vo)) {
                if (vo.getUpdateTime().isAfter(priceStrategy.getUpdateTime())) {
                    throw new ParamsException("请刷新后重试");
                } else {
                    priceStrategyDomainService.updateMarketItemPriceStrategByItemIdAndTargetId(vo.getMarketItemPriceStrategyId(), priceStrategy);
                }
            } else {
                priceStrategyDomainService.saveMarketItemPriceStrategByItemIdAndTargetId(tenantId, priceStrategy);
            }
        }
        if (ObjectUtil.isNotEmpty(onsaleStrategy)) {
            onsaleStrategy.setItemId(marketItem.getId());
            onSaleStrategyDomainService.saveOrUpdateMarketItemOnsaleStrategyByItemIdAndTargetId(tenantId, marketItem.getId(), onsaleStrategy);
        }
    }

    private void checkInitValid(MarketItemDTO itemDTO) {
        if (Objects.isNull(itemDTO.getOutId())) {
            throw new ParamsException("ID不可为空");
        }

        if (Objects.isNull(itemDTO.getUpdateTime())) {
            throw new ParamsException("更新时间不可为空");
        }

        if ((CollectionUtils.isEmpty(itemDTO.getPriceList()) && CollectionUtils.isEmpty(itemDTO.getOnSaleList()))) {
            throw new ParamsException("价格或者上下架信息不可为空");
        }

        List<MarketItemDetail> marketItemDetails = marketItemDetailDao.listByParam(MarketItemDetailParam.builder()
            .outId(itemDTO.getOutId())
            .build());
        if (CollectionUtils.isEmpty(marketItemDetails)) {
            throw new ParamsException("未找到指定item detail");
        }
        MarketItem marketItem = marketItemDao.getById(marketItemDetails.get(0).getMarketItemId());
        if (Objects.isNull(marketItem)) {
            throw new ParamsException("未找到指定item");
        }

        itemDTO.setMarketItemId(marketItem.getId());
        itemDTO.getPriceList().forEach(i ->
            i.setItemId(marketItem.getId()));
        itemDTO.getOnSaleList().forEach(i ->
            i.setItemId(marketItem.getId()));

        List<MarketItemPriceStrategy> strategys = strategyDao.listByItemIds(itemDTO.getTenantId(), Collections.singletonList(marketItem.getId()));
        if (CollectionUtils.isNotEmpty(strategys) && itemDTO.getUpdateTime().isBefore(strategys.get(0).getUpdateTime())) {
            throw new ParamsException("数据已更新！");
        }

    }
    @Deprecated
    public PageResp<MarketItemWithClassificationResp> queryItemWithClassification(MarketItemWithClassificationQueryReq queryReq) {
        MarketItemWithClassificationQueryParam param = ItemQueryConverter.INSTANCE.reqToParam(queryReq);
        // 前台类目
        param.setClassificationIds(handleClassification(queryReq));
        IPage<MarketItemWithClassificationDTO> page = marketItemDao.queryMarketItemWithClassification(param);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResp.emptyPage(queryReq.getPageIndex(), queryReq.getPageSize());
        }
        List<MarketItemWithClassificationResp> marketItemWithClassificationResps = MarketDomainConvert.INSTANCE.convert2RespList(page.getRecords());
        List<Long> marketIds = marketItemWithClassificationResps.stream()
                .map(MarketItemWithClassificationResp::getMarketId)
                .collect(Collectors.toList());
        // 补充分类信息
        handleClassificationName(marketItemWithClassificationResps, marketIds);
        return PageResp.toPageList(marketItemWithClassificationResps,
                (int) page.getTotal(), (int) page.getCurrent(), (int) page.getSize());

    }


    /**
     * 选择商品通用组件 分页查询商品项
     * 不含组合包
     *
     * @param queryReq
     * @return
     */
    @Deprecated
    public PageResp<PageMarketItemResp> pageMarketItem(PageMarketItemQueryReq queryReq) {
        MarketItemPageQueryParam param = ItemQueryConverter.INSTANCE.req2Param(queryReq);

        // 查询条件补充
        if (Objects.nonNull(queryReq.getClassificationId())) {
            List<MarketClassification> marketClassifications = classificationDomainService.queryChildList(queryReq.getClassificationId());
            if (CollectionUtils.isEmpty(marketClassifications)) {
                return PageResp.emptyPage(queryReq.getPageIndex(), queryReq.getPageSize());
            }
            param.setClassificationIds(marketClassifications.stream().map(MarketClassification::getId).collect(Collectors.toList()));
        }
        // 查询
        IPage<PageMarketItemDTO> page = marketItemDao.pageMarketItem(param);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return PageResp.emptyPage(queryReq.getPageIndex(), queryReq.getPageSize());
        }

        // 查询结果补充
        List<PageMarketItemResp> pageMarketItemResps = MarketDomainConvert.INSTANCE.convert2respList(page.getRecords());

        Set<Long> marketIds = pageMarketItemResps.stream().map(PageMarketItemResp::getMarketId).collect(Collectors.toSet());
        List<Long> marketItemIds = pageMarketItemResps.stream().map(PageMarketItemResp::getItemId).collect(Collectors.toList());
        Map<Long, ItemClassificationDTO> itemClassMap = classificationDomainService.getItemClassification(queryReq.getTenantId(), new ArrayList<>(marketIds));

        Map<Long, PriceRangeDTO> priceRangeDTOMap = marketItemPriceDomianService.listRangePriceByItemIds(queryReq.getTenantId(), marketItemIds);

        for (PageMarketItemResp marketItem : pageMarketItemResps) {
            ItemClassificationDTO itemClass = itemClassMap.getOrDefault(marketItem.getMarketId(), new ItemClassificationDTO());
            marketItem.setFirstClassificationId(itemClass.getFirstClassificationId());
            marketItem.setFirstClassificationName(itemClass.getFirstClassificationName());
            marketItem.setSecondClassificationId(itemClass.getSecondClassificationId());
            marketItem.setSecondClassificationName(itemClass.getSecondClassificationName());
            StringBuffer classStr = new StringBuffer();
            if (StringUtils.isNotBlank(marketItem.getFirstClassificationName())) {
                classStr.append(marketItem.getFirstClassificationName());
            }
            if (StringUtils.isNotBlank(marketItem.getSecondClassificationName())) {
                classStr.append(MarketConstants.CATEGORY_SEPARATOR).append(marketItem.getSecondClassificationName());
            }
            marketItem.setClassificationFullName(classStr.toString());

            PriceRangeDTO priceRangeDTO = priceRangeDTOMap.getOrDefault(marketItem.getItemId(), new PriceRangeDTO());
            marketItem.setPriceStr(priceRangeDTO.getPriceStr());
            marketItem.setMaxPrice(priceRangeDTO.getMaxPrice());
            marketItem.setMinPrice(priceRangeDTO.getMinPrice());
        }


        return PageResp.toPageList(pageMarketItemResps, (int) page.getTotal(), (int) page.getCurrent(), (int) page.getSize());
    }
    /**
     * 查询每个market下的item列表 key:marketId value:market的item列表
     *
     * @param marketIds
     * @return
     */
    public Map<Long, List<MarketItemVO>> queryMarketItemList(List<Long> marketIds,Long tenantId,Integer characters, Long adminId, MarketItemInfoQueryFlagDTO flagDTO) {
        MarketItemParam build = MarketItemParam.builder ()
                .deleteFlag (MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag ())
                .marketIds (marketIds)
                .adminId (adminId)
                .build ();
        if(ObjectUtil.isNotEmpty (adminId)){
            build.setNotAllFlag (true);
        }

        List<MarketItem> marketItems = marketItemDao.listByParam(build);
        List<MarketItemVO> marketItemVOS = MarketDomainConvert.INSTANCE.convert2VOs(marketItems);
        fillMarketItemList(marketItemVOS,tenantId,flagDTO,characters, adminId);
        return marketItemVOS.stream().collect(Collectors.groupingBy(MarketItemVO::getMarketId));
    }

    public Page<MarketItemVO> queryMarketItemList(MarketItemCommonQueryDTO queryDTO) {
        MarketItemParam param = MarketConvert.INSTANCE.convert2Param (queryDTO);
        // 查询条件补充
        if (Objects.nonNull(queryDTO.getClassificationId())) {
            List<MarketClassification> marketClassifications = classificationDomainService.queryChildList(queryDTO.getClassificationId());
            if (CollectionUtils.isEmpty(marketClassifications)) {
                return new Page<> (queryDTO.getPageNum (), queryDTO.getPageSize());
            }
            param.setClassificationIds(marketClassifications.stream().map(MarketClassification::getId).collect(Collectors.toList()));
        }
        if(ObjectUtil.isNotEmpty (queryDTO.getAdminId ())){
//            adminid - > 查询这个admin代仓 + 全部自营
            param.setNotAllFlag (true);
            param.setAdminId (queryDTO.getAdminId ());
        }else if(ObjectUtil.isNotEmpty (queryDTO.getCharacters ()) && queryDTO.getCharacters ()==0){
//            Characters ==0 - > 仅查询自营
            param.setNotAllFlag (true);
            param.setAdminId (null);
        }
        Page<MarketItem> marketItemPage = marketItemDao.pageByParam(param);
        Page<MarketItemVO> marketItemVOPage = MarketDomainConvert.INSTANCE.convert2PageItems(marketItemPage);
        MarketItemInfoQueryFlagDTO queryFlagDTO = queryDTO.getMarketItemInfoQueryFlagDTO ();
        fillMarketItemList(marketItemVOPage.getRecords (),queryDTO.getTenantId (), queryFlagDTO, queryDTO.getCharacters (), queryDTO.getAdminId ());
        return marketItemVOPage;
    }

    public Page<MarketItemVO> querySimpleMarketItemList(MarketItemCommonQueryDTO queryDTO) {
        Page<MarketItem> marketItemPage = marketItemDao.pageByParam(MarketConvert.INSTANCE.convert2Param(queryDTO));
        Page<MarketItemVO> marketItemVOPage = MarketDomainConvert.INSTANCE.convert2PageItems(marketItemPage);
        return marketItemVOPage;
    }

    /**
     * 补全查询的结果字段
     *
     * @param list
     */
    private void fillMarketItemList(List<MarketItemVO> list,Long tenantId,MarketItemInfoQueryFlagDTO queryFlagDTO,Integer characters,Long adminId) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if(ObjectUtil.isEmpty (queryFlagDTO)){
            queryFlagDTO = new MarketItemInfoQueryFlagDTO();
        }
        List<Long> marketIds = list.stream ()
                .map (MarketItemVO::getMarketId)
                .distinct ()
                .collect (Collectors.toList ());
        /**
         * query classificcation
         */
        Map<Long, ItemClassificationDTO> itemClassMap = Collections.emptyMap ();
        if(queryFlagDTO.isClassificationIdFlag ()) {
            itemClassMap = classificationDomainService.getItemClassification(tenantId, marketIds);
        }
        /**
         * query categoryid
         */
        Map<Long, Market> marketMap = new HashMap<>();
        if(queryFlagDTO.isCategoryIdFlag ()) {
            List<Market> markets = marketDao.listByIds (marketIds);
            if (CollectionUtils.isNotEmpty (markets)) {
                marketMap = markets.stream ().collect (Collectors.toMap (Market::getId, Function.identity ()));
            }
        }

        /**
         * query price info
         */
        Map<Long, PriceRangeDTO> priceRangeDtoMap = Collections.emptyMap ();
        if(queryFlagDTO.isPriceRangeFlag ()) {
            priceRangeDtoMap = getPriceRangeMap(list);
        }

        /**
         * query stock
         */
        Map<Long, Stock> stockMap = new HashMap<>();
        if(queryFlagDTO.isStockFlag ()) {
            List<Long> noGoodsItemIds = list.stream ()
                    .filter (i -> GoodsTypeEnum.NO_GOOD_TYPE.getCode ().equals (i.getGoodsType ()))
                    .map (MarketItemVO::getId)
                    .collect (Collectors.toList ());
            if (CollectionUtils.isNotEmpty (noGoodsItemIds)) {
                List<Stock> stocks = stockDao.listByParam (StockQueryParam.builder ()
                        .itemIds (noGoodsItemIds)
                        .build ());
                stockMap = stocks.stream ().collect (Collectors.toMap (Stock::getItemId, Function.identity (), (k1, k2) -> k1));
            }
        }
        /**
         * query unit
         */
        Map<Long, List<MarketItemUnitVO>> unitMap = Collections.emptyMap ();
        if(queryFlagDTO.isUnitFlag ()) {
            unitMap = itemUnitDomanService.getUnitMap(list.stream ().map (MarketItemVO::getId).collect (Collectors.toList ()),tenantId);
        }
        /**
         * query characters
         */
        Map<Long, MarketItemDetail> marketItemDetailMap = new HashMap<> ();
        if(xmTenantId.equals (tenantId) && ObjectUtil.isNotEmpty (adminId)){
            List<Long> marketItemIds = list.stream ()
                    .map (MarketItemVO::getId)
                    .distinct ()
                    .collect (Collectors.toList ());

            List<MarketItemDetail> marketItemDetails = marketItemDetailDao.listByParam(MarketItemDetailParam.builder()
                    .marketItemIds (marketItemIds)
                    .build());
            if(CollectionUtils.isNotEmpty (marketItemDetails)){
                marketItemDetailMap = marketItemDetails.stream ().collect (Collectors.toMap (MarketItemDetail::getMarketItemId, Function.identity ()));
            }
        }
        /**
         * fill all info
         */
        for (MarketItemVO marketItem : list) {
            Market market = marketMap.get(marketItem.getMarketId());
            Optional.ofNullable(market).ifPresent(m -> {
                marketItem.setTitle(m.getTitle());
                marketItem.setMainPicture(m.getMainPicture());
                marketItem.setCategoryId(m.getCategoryId());
            });
            Stock stock = stockMap.get(marketItem.getId());
            Optional.ofNullable(stock)
                .filter(s -> marketItem.getTenantId().equals(s.getTenantId()))
                .ifPresent(s -> marketItem.setStockAmount(stock.getAmount()));
            PriceRangeDTO priceRangeDTO = Optional.ofNullable(priceRangeDtoMap.get(marketItem.getId())).orElse(new PriceRangeDTO());
            marketItem.setPriceStr(Optional.ofNullable(priceRangeDTO.getPriceStr()).orElse(""));
            marketItem.setMaxPrice(Optional.ofNullable(priceRangeDTO.getMaxPrice()).orElse(BigDecimal.ZERO));
            marketItem.setMinPrice(Optional.ofNullable(priceRangeDTO.getMinPrice()).orElse(BigDecimal.ZERO));
            marketItem.setItemClassificationDTO (itemClassMap.get (marketItem.getMarketId()));
            MarketItemDetail marketItemDetail = Optional.ofNullable(marketItemDetailMap.get(marketItem.getId())).orElse(new MarketItemDetail());
            marketItem.setCharacters (ObjectUtil.isEmpty (marketItemDetail.getCustomerId ()) ? 0 : 1);
            marketItem.setMarketItemUnitList (unitMap.get (marketItem.getId()));
        }
    }


    /**
     * 查询每一个item的价格区间
     *
     * @param marketItemVOList
     * @return
     */
    public Map<Long, PriceRangeDTO> getPriceRangeMap(List<MarketItemVO> marketItemVOList) {
        Map<Long, List<MarketItemVO>> tenantMap = marketItemVOList.stream().collect(Collectors.groupingBy(MarketItemVO::getTenantId));
        Map<Long, PriceRangeDTO> priceRangeDtoMap = new HashMap<>();
        for (Long tenantId : tenantMap.keySet()) {
            List<MarketItemVO> marketItemVOs = tenantMap.get(tenantId);
            List<Long> marketItemIds = marketItemVOs.stream()
                .map(MarketItemVO::getId)
                .collect(Collectors.toList());
            priceRangeDtoMap.putAll(marketItemPriceDomianService.listRangePriceByItemIds(tenantId, marketItemIds));
        }
        return priceRangeDtoMap;
    }

    public MarketItemVO getMarketItemDetailByItemCode(Long tenantId, String itemCode) {
        List<MarketItem> marketItems = marketItemDao.getByItemCode(tenantId,itemCode);
        if (CollectionUtils.isEmpty(marketItems)) {
            return null;
        }
        MarketItem marketItem = marketItems.get(0);
        if (Objects.nonNull(tenantId) && !tenantId.equals(marketItem.getTenantId())) {
            return null;
        }
        MarketItemVO itemVO = MarketDomainConvert.INSTANCE.convert2ItemVO(marketItem);
        buildItemVO(itemVO);
        log.info ("tenantId={}, itemCode={},title={},it={}",tenantId, itemCode,itemVO.getTitle (),JSON.toJSONString (itemVO));
        MarketItemInfoResp marketItemInfoResp = MarketConvert.INSTANCE.convert2Item (itemVO);
        log.info ("tenantId={}, itemCode={},it_title={},it={}",tenantId, itemCode,marketItemInfoResp.getItemTitle (), JSON.toJSONString (marketItemInfoResp));
        return itemVO;
    }

    private void buildItemVO(MarketItemVO itemVO) {
        //如果是鲜沐的商品 返回该item的上下架明细，还有marketitemdtail、marketdetail
        if (Objects.equals(itemVO.getTenantId(), xmTenantId)) {
            addMarketInfo(itemVO);
            addMarketDetailInfo(itemVO);
            addMarketItemDetailInfo(itemVO);
            addOnsaleStrategy(itemVO);
        }
        /**
         * query price info
         */
        Map<Long, PriceRangeDTO> priceRangeDtoMap = getPriceRangeMap(Collections.singletonList(itemVO));
        itemVO.setPriceStr(Optional.ofNullable(priceRangeDtoMap.get(itemVO.getId())).orElse(new PriceRangeDTO()).getPriceStr());
        List<MarketItemPriceStrategyVO> marketItemPriceStrategyVOS = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds(itemVO.getTenantId(),
            Collections.singletonList(itemVO.getId()));
        if (!Objects.equals(itemVO.getTenantId(), xmTenantId)) {
            fillDifferenceValue(itemVO, marketItemPriceStrategyVOS);
        }
        itemVO.setPriceStrategyList(marketItemPriceStrategyVOS);

        /**
         * query stock
         */
        if (GoodsTypeEnum.NO_GOOD_TYPE.getCode().equals(itemVO.getGoodsType())) {
            List<Stock> stocks = stockDao.listByParam(StockQueryParam.builder()
                .itemId(itemVO.getId())
                .build());
            if (CollectionUtils.isNotEmpty(stocks)) {
                itemVO.setStockAmount(stocks.get(0).getAmount());
            }
        }

        UnfairPriceStrategyVO unfairPriceStrategyVO = unfairPriceStrategyDomainService.getStrategyValueByItemIdAndTargetType(itemVO.getTenantId(), itemVO.getId(), MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode());
        itemVO.setUnfairPriceStrategyVO(unfairPriceStrategyVO);

        Map<Long, List<MarketItemUnitVO>> unitMap = itemUnitDomanService.getUnitMap (Collections.singletonList (itemVO.getId ()), itemVO.getTenantId ());
        itemVO.setMarketItemUnitList (unitMap.get (itemVO.getId ()));
    }

    private void addOnsaleStrategy(MarketItemVO itemVO) {
        List<MarketItemOnsaleStrategyMappingVO> marketItemOnsaleStrategyVOS = onSaleStrategyDomainService.listMarketItemOnsaleStrategyMappingByItemIds(itemVO.getTenantId(), Collections.singletonList(itemVO.getId()));
        itemVO.setOnsaleStrategyList(marketItemOnsaleStrategyVOS);
    }

    private void addMarketItemDetailInfo(MarketItemVO itemVO) {
        MarketItemDetail marketItemDetail = marketItemDetailDao.getByMarketItemId(itemVO.getId());
        if (Objects.isNull(marketItemDetail)) {
            return;
        }
        buildMarketItemDetail(Stream.of(marketItemDetail).collect(Collectors.toMap(MarketItemDetail::getMarketItemId, Function.identity())),itemVO);
    }

    private void addMarketDetailInfo(MarketItemVO itemVO) {
        MarketDetail marketDetail = marketDetailDao.getByMarketId(itemVO.getMarketId());
        if (Objects.isNull(marketDetail)) {
            return;
        }
        buildMarketDetail(Stream.of(marketDetail).collect(Collectors.toMap(MarketDetail::getMarketId, Function.identity())),itemVO);
    }

    private void addMarketInfo(MarketItemVO itemVO) {
        Market market = marketDao.getById(itemVO.getMarketId());
        if(Objects.isNull (market)){
            return;
        }
        buildMarket(Stream.of(market).collect(Collectors.toMap(Market::getId, Function.identity())),itemVO);
    }

    /**
     * 查询单个规格详情
     *
     * @return
     */
    public MarketItemVO getMarketItemDetailById(Long tenantId, Long marketItemId) {
        MarketItem marketItem = marketItemDao.getById(marketItemId);
        if (Objects.isNull(marketItem)) {
            return null;
        }
        if (Objects.nonNull(tenantId) && !tenantId.equals(marketItem.getTenantId())) {
            return null;
        }
        MarketItemVO itemVO = MarketDomainConvert.INSTANCE.convert2ItemVO(marketItem);
        buildItemVO(itemVO);
        return itemVO;
    }


    /**
     * 查询上架商品item列表
     *
     * @param queryDTO
     * @return
     */
    public Page<MarketItem4StoreVO> listMarketItem4Store(MarketItemPageQuery4StoreDTO queryDTO) {
        // 校验入参
        checkListMarketItem4StoreValid(queryDTO);
        // 先查一下是否有tenant的上下架策略
        MarketItemStoreQueryParam queryParam = fillQueryParam(queryDTO);
        if (Objects.isNull(queryParam)) {
            return null;
        }
        Page<MarketItem> marketItemPage = marketItemDao.pageStoreItemByParam(queryParam);
        Page<MarketItem4StoreVO> pageInfo = new Page<>();
        pageInfo.setTotal(marketItemPage.getTotal());
        pageInfo.setRecords(fillMarketItem4Store(marketItemPage.getRecords(), queryDTO.getStoreId()));
        return pageInfo;
    }

    private MarketItemStoreQueryParam fillQueryParam(MarketItemPageQuery4StoreDTO queryDTO) {
        MarketItemStoreQueryParam queryParam = MarketDomainConvert.INSTANCE.convert2ItemStoreParam(queryDTO);

        if (Objects.nonNull(queryDTO.getClassificationId())) {
            List<MarketItemClassification> marketItemClassifications = marketItemClassificationDao.listByParam(MarketItemClassificationQueryParam.builder()
                .classificationId(queryDTO.getClassificationId())
                .tenantId(queryDTO.getTenantId())
                .build());
            if (CollectionUtils.isEmpty(marketItemClassifications)) {
                return null;
            }
            List<Long> classMarketIds = marketItemClassifications.stream().map(MarketItemClassification::getMarketId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(queryParam.getMarketIds())) {
                queryParam.setMarketIds(classMarketIds);
            } else {
                Collection<Long> intersection = CollectionUtils.intersection(classMarketIds, queryParam.getMarketIds());
                if (intersection.isEmpty()) {
                    return null;
                }
                queryParam.setItemIds(new ArrayList<>(intersection));
            }
        }

        return queryParam;
    }


    private void checkListMarketItem4StoreValid(MarketItemPageQuery4StoreDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            throw new ParamsException("入参不可为空");
        }
    }

    /**
     * 填充列表信息
     *
     * @param marketItems
     * @return
     */
    private List<MarketItem4StoreVO> fillMarketItem4Store(List<MarketItem> marketItems, Long storeId) {
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyList();
        }
        List<Long> itemIds = marketItems.stream()
            .map(MarketItem::getId)
            .collect(Collectors.toList());

        /**
         * query market
         */
        List<Long> marketIds = marketItems.stream()
            .map(MarketItem::getMarketId)
            .distinct()
            .collect(Collectors.toList());
        List<Market> markets = marketDao.listByIds(marketIds);
        Map<Long, Market> marketMap = markets.stream().collect(Collectors.toMap(Market::getId, Function.identity()));
        /**
         * query stock
         */
        List<Stock> stocks = stockDao.listByParam(StockQueryParam.builder()
            .itemIds(itemIds)
            .build());
        Map<Long, Stock> stockMap = stocks.stream().collect(Collectors.toMap(Stock::getItemId, Function.identity(), (k1, k2) -> k1));

        /**
         * query price
         */
        Map<Long, PriceDetailVO> priceDetailMap = new HashMap<>();
        if (Objects.nonNull(storeId)) {
            Map<Long, Integer> itemIdMap = itemIds.stream()
                    .collect(Collectors.toMap(itemId -> itemId, itemId -> 1, (existing, replacement) -> existing));
            priceDetailMap = priceDomianService.listItemPriceDetailByItemIdsWithQuantity(marketItems.get(0).getTenantId(), storeId, PriceTargetTypeEnum.STORE.getCode(), itemIdMap,false);
        }

        /**
         * query sub item
         */
        List<Long> combineItemIds = marketItems.stream()
            .filter(i -> ItemTypeEnum.COMBINE_ITEM.getCode().equals(i.getItemType()))
            .map(MarketItem::getId)
            .collect(Collectors.toList());
        Map<Long, List<MarketCombineVO>> combineSubItemMap = combineDomainService.batchQueryCombineSubItemList(combineItemIds);

        /**
         * fill all info
         */
        Map<Long, PriceDetailVO> finalPriceDetailMap = priceDetailMap;
        return marketItems.stream()
            .map(item -> {
                MarketItem4StoreVO itemVo = MarketDomainConvert.INSTANCE.convert2StoreItemVO(item);
                Optional.ofNullable(stockMap.get(item.getId()))
                    .ifPresent(a -> itemVo.setStockAmount(a.getAmount()));
                Optional.ofNullable(finalPriceDetailMap.get(item.getId()))
                    .ifPresent(p -> {itemVo.setPrice(p.getPrice());itemVo.setLadderPrices (p.getLadderPriceDTOS ());});

                itemVo.setCombineItemList(combineSubItemMap.get(item.getId()));

                Market market = Optional.ofNullable(marketMap.get(item.getMarketId())).orElse(new Market());
                itemVo.setTitle(market.getTitle());
                itemVo.setMainPicture(market.getMainPicture());
                return itemVo;
            })
            .collect(Collectors.toList());
    }

    /**
     * 查询item列表（只包含item主表的信息）
     *
     * @param queryDto
     * @return
     */
    public Page<MarketItemSimpleInfoVO> listMarketItemInfo(MarketItemQuery4StoreDTO queryDto) {
        checkListMarketItemInfo(queryDto);
        MarketItemStoreQueryParam queryParam = fillSimpleQueryParam(queryDto);
        if (Objects.isNull(queryParam)) {
            return null;
        }
        Page<MarketItem> marketItemPage = marketItemDao.pageStoreItemByParam(queryParam);
        List<MarketItem> marketItems = marketItemPage.getRecords ();

        Page<MarketItemSimpleInfoVO> pageInfo = new Page<>();
        pageInfo.setTotal(marketItemPage.getTotal());

        if (CollectionUtils.isNotEmpty (marketItems)) {
            /**
             * query market
             */
            Set<Long> marketIds = marketItems.stream()
                    .map(MarketItem::getMarketId)
                    .collect(Collectors.toSet());
            List<Market> markets = marketDao.listByIds(marketIds);
            Map<Long, Market> marketMap = markets.stream().collect(Collectors.toMap(Market::getId, Function.identity()));

            List<MarketItemSimpleInfoVO> collect = marketItems.stream ()
                    .map (item -> {
                        MarketItemSimpleInfoVO itemVo = MarketDomainConvert.INSTANCE.convert2SimpleItemVO (item);
                        Market market = Optional.ofNullable (marketMap.get (item.getMarketId ())).orElse (new Market ());
                        itemVo.setTitle (market.getTitle ());
                        itemVo.setMainPicture (market.getMainPicture ());
                        return itemVo;
                    })
                    .collect (Collectors.toList ());
            pageInfo.setRecords(collect);
        }
        return pageInfo;
    }

    private MarketItemStoreQueryParam fillSimpleQueryParam(MarketItemQuery4StoreDTO queryDTO) {
        MarketItemStoreQueryParam queryParam = MarketDomainConvert.INSTANCE.convert2Param(queryDTO);
        if (Objects.nonNull(queryDTO.getTitle())) {
            List<Market> markets = marketDao.listByParam(MarketQueryParam.builder()
                .title(queryDTO.getTitle())
                .tenantId(queryDTO.getTenantId())
                .build());
            if (CollectionUtils.isEmpty(markets)) {
                return null;
            }
            queryParam.setMarketIds(markets.stream().map(Market::getId).collect(Collectors.toList()));
        }
        return queryParam;
    }



    private void checkListMarketItemInfo(MarketItemQuery4StoreDTO queryDto) {
        if (Objects.isNull(queryDto)) {
            throw new ParamsException("入参不可为空");
        }
        if (Objects.isNull(queryDto.getTenantId())) {
            throw new ParamsException("租户不可为空");
        }
        if (Objects.isNull(queryDto.getStoreId())) {
            throw new ParamsException("门店ID不可为空");
        }
    }

    public MarketItemDetail4StoreVO getMarketItemDetail4Store(MarketItemDetailQueryDTO dto) {
        checkMarketItemInfoValid(dto);
        MarketItem marketItem = marketItemDao.getById(dto.getItemId());
        if (Objects.isNull(marketItem)
            || !OnSaleTypeEnum.ON_SALE.getCode().equals(marketItem.getOnSale())
            || MarketItemEnum.DeleteFlagEnum.DELETED.getFlag().equals(marketItem.getDeleteFlag())) {
            return null;
        }
        Market market = marketDao.getById(marketItem.getMarketId());
        if (Objects.isNull(market)) {
            return null;
        }
        MarketItemDetail4StoreVO marketItemDetail4StoreVO = MarketDomainConvert.INSTANCE.convert2ItemDetailVO(marketItem);
        marketItemDetail4StoreVO.setTitle(market.getTitle());
        marketItemDetail4StoreVO.setSubTitle(market.getSubTitle());
        marketItemDetail4StoreVO.setMainPicture(market.getMainPicture());
        marketItemDetail4StoreVO.setDetailPicture(market.getDetailPicture());
        marketItemDetail4StoreVO.setDescriptionString(market.getDescriptionString());
        marketItemDetail4StoreVO.setBrandName (market.getBrandName ());

        List<Stock> stocks = stockDao.listByParam(StockQueryParam.builder()
            .itemId(marketItem.getId())
            .tenantId(marketItem.getTenantId())
            .build());
        if (CollectionUtils.isNotEmpty(stocks)) {
            marketItemDetail4StoreVO.setStockAmount(stocks.get(0).getAmount());
        }

        Map<Long, PriceDetailVO> priceMap = priceDomianService.listItemPriceDetailByItemIdsWithQuantity(dto.getTenantId(),
            dto.getStoreId(),
            PriceTargetTypeEnum.STORE.getCode(),
            Collections.singletonMap(marketItem.getId(), 1),false);

        if (Objects.nonNull(priceMap.get(marketItem.getId()))) {
            marketItemDetail4StoreVO.setPrice(priceMap.get(marketItem.getId()).getPrice());
            marketItemDetail4StoreVO.setLadderPrices (priceMap.get(marketItem.getId()).getLadderPriceDTOS ());
        }

        // 查询子商品列表
        if (ItemTypeEnum.COMBINE_ITEM.getCode().equals(marketItem.getItemType())) {
            marketItemDetail4StoreVO.setCombineItemRespList(combineDomainService.queryCombineSubItemList(marketItem.getId()));
        }

        return marketItemDetail4StoreVO;
    }

    private void checkMarketItemInfoValid(MarketItemDetailQueryDTO queryDto) {
        if (Objects.isNull(queryDto)) {
            throw new ParamsException("入参不可为空");
        }
        if (Objects.isNull(queryDto.getTenantId())) {
            throw new ParamsException("租户不可为空");
        }
        if (Objects.isNull(queryDto.getStoreId())) {
            throw new ParamsException("门店ID不可为空");
        }
        if (Objects.isNull(queryDto.getItemId())) {
            throw new ParamsException("itemID不可为空");
        }
    }

    /**
     * 删除market_item
     *
     * @param dto
     */
    public void deleteMarketItem(MarketDeleteDTO dto) {
        if (Objects.isNull(dto.getMarketItemId())) {
            throw new ParamsException("商品Id不能为空");
        }
        MarketItem item = marketItemDao.getById(dto.getMarketItemId());
        if (Objects.isNull(item)) {
            throw new BizException("未查询到该商品信息");
        }
        Map<String, Integer> marketItemOnSale = onSaleStrategyDomainService.getMarketItemOnSale(dto.getTenantId(), Collections.singletonList(dto.getMarketItemId()));
        if (OnSaleTypeEnum.ON_SALE.getCode().equals(marketItemOnSale.get(onSaleStrategyDomainService.fetchOnSaleMapGroupKey(dto.getTenantId(), dto.getMarketItemId())))
            || OnSaleTypeEnum.ON_SALE.getCode().equals(item.getOnSale())) {
            throw new BizException("请将该商品完成下架再进行删除");
        }

        // 如果当前item为组合品的子item时，则不允许删除
        Set<Long> longs = marketCombineItemMappingDao.listItemIdsByCombineItemId(dto.getTenantId(), dto.getMarketItemId());
        if (CollectionUtils.isNotEmpty(longs)) {
            throw new BizException("请先在组合品中删除当前商品");
        }

        MarketItem update = new MarketItem();
        update.setId(dto.getMarketItemId());
        update.setDeleteFlag(MarketItemEnum.DeleteFlagEnum.DELETED.getFlag());
        marketItemDao.updateById(update);

        // 更新报价单和自营货品是否关联商品字段
        updateAssociated(item.getGoodsType(), item.getSkuId(), item.getTenantId());
    }

    /**
     * 更新自营货品是否关联商品字段
     *
     * @param goodsType
     * @param skuId
     * @param tenantId
     */
    private void updateAssociated(Integer goodsType, Long skuId, Long tenantId) {
        // 更新货品或者报价单表是否关联关系字段
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(tenantId,Collections.singletonList (skuId));
        Integer associated = ProductEnum.ProductSupplierSkuAssociateEnum.NOT_ASSOCIATE.getType();
        if (CollectionUtils.isNotEmpty(marketItems)) {
            associated = marketItems.stream().anyMatch(e -> !MarketItemEnum.DeleteFlagEnum.DELETED.getFlag().equals(e.getDeleteFlag()))
                ? ProductEnum.ProductSupplierSkuAssociateEnum.HAS_ASSOCIATE.getType()
                : ProductEnum.ProductSupplierSkuAssociateEnum.NOT_ASSOCIATE.getType();
        }

        // 更新货品或者报价单表是否关联关系字段
        // fixme 考虑远程调用失败
        if (GoodsTypeEnum.SELF_GOOD_TYPE.getCode().equals(goodsType)) {
            productFacade.updateAssociated(skuId, tenantId, associated);
        } else if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(goodsType)) {
            productFacade.updateSupplyAssociated(skuId, tenantId, associated);
        }
    }


    /**
     * 修改market_item的上下架状态
     *
     * @param dto
     */
    public void changeOnSale(MarketItemOnSaleInputDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getId())) {
            throw new ParamsException("商品编码不可为空！");
        }
        MarketItem marketItem = marketItemDao.getById(dto.getId());
        if (Objects.isNull(marketItem) || (Objects.nonNull(dto.getTenantId()) && !Objects.equals(dto.getTenantId(), marketItem.getTenantId()))) {
            throw new ParamsException("未找到商品！");
        }
        checkOnSaleUp(dto, marketItem);
        // 更新上下架状态
        MarketItem updateRecord = new MarketItem();
        updateRecord.setId(marketItem.getId());
        updateRecord.setOnSale(dto.getOnSale());
        marketItemDao.updateById(updateRecord);
    }

    /**
     * 上架校验
     *
     * @param dto
     * @param marketItem
     */
    private void checkOnSaleUp(MarketItemOnSaleInputDTO dto, MarketItem marketItem) {
        if (!Objects.equals(OnSaleTypeEnum.ON_SALE.getCode(), dto.getOnSale())) {
            return;
        }

        // 查询价格
        List<MarketItemPriceStrategyVO> priceStrategyVos = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds(dto.getTenantId(),
            Collections.singletonList(dto.getId()));

        if (CollectionUtils.isEmpty(priceStrategyVos)) {
            throw new BizException("请填写价格，再进行上架");
        }

        // 如果是三方仓sku并且修改上架，判断报价单是否有效
        if (GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItem.getGoodsType())) {
            // 判断报价单是否删除，如果删除，请重新关联报价单
            List<ProductPricingSupplyCityMappingVO> mappingVos = productFacade.querySupplyCityBySkuId(Collections.singletonList(marketItem.getSkuId()), marketItem.getTenantId());
            if (CollectionUtils.isEmpty(mappingVos)
                || mappingVos.stream().allMatch(item -> item.getEndTime().isBefore(LocalDateTime.now()))) {
                throw new BizException(ResultDTOEnum.PRICING_SUPPLY_FAILURE.getMessage());
            }
        }

        // 校验前台分组是否填写，如果未填写，需要提示
        List<MarketItemClassification> marketItemClassificationDtos = marketItemClassificationDao
            .listByParam(MarketItemClassificationQueryParam.builder()
                .tenantId(marketItem.getTenantId())
                .marketId(marketItem.getMarketId())
                .build());
        if (CollectionUtils.isEmpty(marketItemClassificationDtos)) {
            throw new BizException("该商品前台分类未填写，不能进行上架。");
        }
    }

    public List<MarketItemOnSaleSimpleDTO> queryMarketItemOnSaleInfo(MarketItemOnSaleReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getTenantId())) {
            throw new ParamsException("参数不可为空！");
        }
        if (CollectionUtils.isEmpty(req.getSkuIds())) {
            return Collections.emptyList();
        }
        return marketItemDao.queryMarketItemOnSaleInfo(req.getTenantId(), req.getOnSale(), req.getSkuIds());
    }

    public List<MarketItemOnSaleSimpleDTO> queryOnSaleMarketItems(MarketItemOnSaleReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getTenantId())) {
            throw new ParamsException("参数不可为空");
        }
        return marketItemDao.queryOnSaleMarketItems(req.getTenantId(), req.getOnSale());
    }


    /**
     * 批量修改上下架状态
     *
     * @param dto
     */
    public BatchOnSaleResultVO batchChangOnSale(MarketItemOnSaleInputDTO dto) {
        BatchOnSaleResultVO resultVO = BatchOnSaleResultVO.builder()
            .failCount(0)
            .successCount(0)
            .build();
        if (CollectionUtils.isEmpty(dto.getItemIds())) {
            return resultVO;
        }
        // 初始始化数据
        List<Long> failIds = new ArrayList<>();
        List<OnSaleFailResultVO> resultList = new ArrayList<>();
        // 校验租户
        OnSaleCheckDTO tenantCheckDto = checkOnSaleTenant(dto.getItemIds(), dto.getTenantId(), dto.getOnSale());
        if (Objects.isNull(tenantCheckDto)) {
            return resultVO;
        }
        Collection<Long> tenantErrorIds = tenantCheckDto.getFailIds();
        if (CollectionUtils.isNotEmpty(tenantErrorIds)) {
            failIds.addAll(tenantErrorIds);
            resultList.addAll(tenantErrorIds.stream()
                .map(i -> OnSaleFailResultVO.builder()
                    .itemId(i)
                    .failReason("编码不存在")
                    .build())
                .collect(Collectors.toList()));
        }

        OnSaleCheckDTO priceCheckDto = checkOnSalePrice(tenantCheckDto.getSuccessItemList(), dto.getTenantId(), dto.getOnSale());
        Collection<Long> noPassPriceIds = priceCheckDto.getFailIds();
        if (CollectionUtils.isNotEmpty(noPassPriceIds)) {
            failIds.addAll(noPassPriceIds);
            resultList.addAll(noPassPriceIds.stream()
                .map(i -> OnSaleFailResultVO.builder()
                    .itemId(i)
                    .failReason("请填写价格，再进行上架")
                    .build())
                .collect(Collectors.toList()));
        }

        // 校验前台分组是否填写，如果未填写，需要提示
        OnSaleCheckDTO classCheckDto = checkOnSaleClass(priceCheckDto.getSuccessItemList(), dto.getTenantId(), dto.getOnSale());
        Collection<Long> noPassClassIds = classCheckDto.getFailIds();
        if (CollectionUtils.isNotEmpty(noPassClassIds)) {
            failIds.addAll(noPassClassIds);
            resultList.addAll(noPassClassIds.stream()
                .map(i -> OnSaleFailResultVO.builder()
                    .itemId(i)
                    .failReason("该商品前台分类未填写，不能进行上架。")
                    .build())
                .collect(Collectors.toList()));
        }

        //校验报价单
        OnSaleCheckDTO supplyCheckDto = checkOnSaleSupply(classCheckDto.getSuccessItemList(), dto.getTenantId(), dto.getOnSale());
        Collection<Long> noSupplyIds = supplyCheckDto.getFailIds();
        if (CollectionUtils.isNotEmpty(noSupplyIds)) {
            failIds.addAll(noSupplyIds);
            resultList.addAll(noSupplyIds.stream()
                .map(i -> OnSaleFailResultVO.builder()
                    .itemId(i)
                    .failReason(ResultDTOEnum.PRICING_SUPPLY_FAILURE.getMessage())
                    .build())
                .collect(Collectors.toList()));
        }

        // 成功部分
        Set<Long> successIds = new HashSet<>(CollectionUtils.subtract(dto.getItemIds(), failIds));

        // 批量更新上下架状态
        marketItemDao.updateBatchOnSaleById(dto.getOnSale(), successIds);

        return BatchOnSaleResultVO.builder()
            .successCount(successIds.size())
            .failCount(failIds.size())
            .successItemIds(new ArrayList<>(successIds))
            .resultList(resultList)
            .build();
    }

    private OnSaleCheckDTO checkOnSaleTenant(List<Long> itemIds, Long tenantId, Integer onSale) {
        List<Long> failIds;
        // 查询原始数据
        MarketItemParam param = new MarketItemParam();
        param.setItemIds(itemIds);
        param.setTenantId(tenantId);
        param.setDeleteFlag(MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag());
        List<MarketItem> marketItems = marketItemDao.listByParam(param);
        List<MarketItem> onSaleItems = new ArrayList<>();

        if (CollectionUtils.isEmpty(marketItems)) {
            failIds = itemIds;
        } else {
            List<Long> ids = marketItems.stream().map(MarketItem::getId).collect(Collectors.toList());
            failIds = itemIds.stream().filter(itemId -> !ids.contains(itemId)).collect(Collectors.toList());
            // 过滤不需要修改状态的item
            onSaleItems = marketItems.stream().filter(i -> !onSale.equals(i.getOnSale())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(onSaleItems)) {
                return null;
            }
            if (onSaleItems.size() > BATCH_ON_SALE_LIMIT) {
                throw new BizException("一次性最多更新500条数据！");
            }
        }
        return OnSaleCheckDTO.builder()
            .failIds(failIds)
            .successItemList(onSaleItems.stream()
                .filter(i -> !failIds.contains(i.getId()))
                .collect(Collectors.toList()))
            .build();

    }

    private OnSaleCheckDTO checkOnSalePrice(List<MarketItem> marketItems, Long tenantId, Integer onSale) {
        if (CollectionUtils.isEmpty(marketItems)) {
            return OnSaleCheckDTO.builder().build();
        }
        if (OnSaleTypeEnum.SOLD_OUT.getCode().equals(onSale)) {
            return OnSaleCheckDTO.builder()
                .successItemList(marketItems)
                .build();
        }
        List<Long> updateItemIds = marketItems.stream()
            .map(MarketItem::getId)
            .collect(Collectors.toList());
        // 查询价格
        List<MarketItemPriceStrategyVO> priceStrategyVos = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds(tenantId, updateItemIds);
        List<Long> passPriceIds = priceStrategyVos.stream().map(MarketItemPriceStrategyVO::getItemId).distinct().collect(Collectors.toList());
        Collection<Long> noPassPriceIds = CollectionUtils.subtract(updateItemIds, passPriceIds);
        return OnSaleCheckDTO.builder()
            .failIds(noPassPriceIds)
            .successItemList(marketItems.stream()
                .filter(i -> passPriceIds.contains(i.getId()))
                .collect(Collectors.toList()))
            .build();
    }

    private OnSaleCheckDTO checkOnSaleClass(List<MarketItem> marketItems, Long tenantId, Integer onSale) {
        if (CollectionUtils.isEmpty(marketItems)) {
            return OnSaleCheckDTO.builder().build();
        }
        if (OnSaleTypeEnum.SOLD_OUT.getCode().equals(onSale)) {
            return OnSaleCheckDTO.builder()
                .successItemList(marketItems)
                .build();
        }

        List<Long> marketIds = marketItems.stream().map(MarketItem::getMarketId).distinct().collect(Collectors.toList());
        List<MarketItemClassification> marketItemClassificationDtos = marketItemClassificationDao
            .listByParam(MarketItemClassificationQueryParam.builder()
                .tenantId(tenantId)
                .marketIds(marketIds)
                .build());
        Set<Long> passClassMarketIds = marketItemClassificationDtos.stream().map(MarketItemClassification::getMarketId).collect(Collectors.toSet());
        List<Long> noPassIds = new ArrayList<>();
        List<MarketItem> successList = new ArrayList<>();
        for (MarketItem marketItem : marketItems) {
            if (passClassMarketIds.contains(marketItem.getMarketId())) {
                successList.add(marketItem);
            } else {
                noPassIds.add(marketItem.getId());
            }
        }
        return OnSaleCheckDTO.builder()
            .successItemList(successList)
            .failIds(noPassIds)
            .build();
    }

    private OnSaleCheckDTO checkOnSaleSupply(List<MarketItem> marketItems, Long tenantId, Integer onSale) {
        if (CollectionUtils.isEmpty(marketItems)) {
            return OnSaleCheckDTO.builder().build();
        }
        if (OnSaleTypeEnum.SOLD_OUT.getCode().equals(onSale)) {
            return OnSaleCheckDTO.builder()
                .successItemList(marketItems)
                .build();
        }
        // 如果是三方仓sku并且修改上架，判断报价单是否有效
        List<Long> supplySkuIds = marketItems.stream()
            .filter(i -> GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(i.getGoodsType()))
            .map(MarketItem::getSkuId)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(supplySkuIds)) {
            return OnSaleCheckDTO.builder()
                .successItemList(marketItems)
                .build();
        }
        List<ProductPricingSupplyCityMappingVO> mappingVos = productFacade.querySupplyCityBySkuId(supplySkuIds, tenantId);
        if (CollectionUtils.isEmpty(mappingVos)) {
            return OnSaleCheckDTO.builder()
                .failIds(marketItems.stream().map(MarketItem::getId).collect(Collectors.toList()))
                .build();
        }

        Map<Long, List<ProductPricingSupplyCityMappingVO>> supplyMap = mappingVos.stream().collect(Collectors.groupingBy(ProductPricingSupplyCityMappingVO::getSupplySkuId));

        List<MarketItem> successList = new ArrayList<>();
        List<Long> failIds = new ArrayList<>();
        for (MarketItem marketItem : marketItems) {
            List<ProductPricingSupplyCityMappingVO> mappingVOS = supplyMap.get(marketItem.getSkuId());
            if (!GoodsTypeEnum.QUOTATION_TYPE.getCode().equals(marketItem.getGoodsType())) {
                successList.add(marketItem);
            } else if (CollectionUtils.isEmpty(mappingVOS)
                || mappingVOS.stream().allMatch(item -> item.getEndTime().isBefore(LocalDateTime.now()))) {
                failIds.add(marketItem.getId());
            } else {
                successList.add(marketItem);
            }
        }
        return OnSaleCheckDTO.builder()
            .successItemList(successList)
            .failIds(failIds)
            .build();
    }

    /**
     * 查询价格策略
     *
     * @return
     */
    public MarketItemVO queryItemPriceStrategy(Long marketItemId, Long tenantId) {
        MarketItem marketItem = marketItemDao.getById(marketItemId);
        if (!marketItem.getTenantId().equals(tenantId)) {
            throw new BizException(TENANT_MATCH_ERROR.getMessage());
        }
        MarketItemVO itemVO = MarketDomainConvert.INSTANCE.convert2ItemVO(marketItem);
        List<MarketItemPriceStrategyVO> marketItemPriceStrategyVOS = priceStrategyDomainService.listMarketItemPriceStrategyByItemIds(tenantId,
            Collections.singletonList(marketItemId));
        fillDifferenceValue(itemVO, marketItemPriceStrategyVOS);
        itemVO.setPriceStrategyList(marketItemPriceStrategyVOS);
        UnfairPriceStrategyVO unfairPriceStrategyVO = unfairPriceStrategyDomainService.getStrategyValueByItemIdAndTargetType(tenantId, marketItemId, null);
        itemVO.setUnfairPriceStrategyVO(unfairPriceStrategyVO);
        return itemVO;
    }

    private void fillDifferenceValue(MarketItemVO marketItem, List<MarketItemPriceStrategyVO> marketItemPriceStrategyVOS) {
        if (Objects.equals(marketItem.getGoodsType(), GoodsTypeEnum.NO_GOOD_TYPE.getCode())) {
            fillNoGoodDifferenceValue(marketItem.getNoGoodsSupplyPrice(), marketItemPriceStrategyVOS);
        }
        if ((Objects.equals(marketItem.getGoodsType(), GoodsTypeEnum.SELF_GOOD_TYPE.getCode()) || Objects.equals(marketItem.getGoodsType(), GoodsTypeEnum.QUOTATION_TYPE.getCode())) && ObjectUtil.isNotNull(marketItem.getSkuId())) {
            List<MarketItemPriceStrategyVO> assignStrategyList = marketItemPriceStrategyVOS.stream()
                .filter(e -> Objects.equals(e.getStrategyType(), MarketItemPriceStrategyEnum.StrategyTypeEnum.ASSIGN.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(assignStrategyList)) {
                return;
            }
            //有固定价格的 才需要、计算倒挂值
            List<MarketItemPriceStrategyVO> storeStrategys = assignStrategyList.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.STORE.getCode())).collect(Collectors.toList());

            List<MarketItemPriceStrategyVO> storeGroupStrategies = assignStrategyList.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.STORE_GROUP.getCode())).collect(Collectors.toList());
            Set<Long> storeGroupIds = storeGroupStrategies.stream()
                .map(MarketItemPriceStrategyVO::getTargetIds)
                .collect(HashSet::new, HashSet::addAll, HashSet::addAll);
            Map<Long, List<Long>> storeGroupMap = storeFacade.getGroupByStoreGroupIds(marketItem.getTenantId(), new ArrayList<>(storeGroupIds));

            MarketItemPriceStrategyVO tenantStrategy = assignStrategyList.stream()
                .filter(e -> Objects.equals(e.getTargetType(), MarketItemPriceEnum.TargetTypeEnum.TENANT.getCode())).findFirst().orElse(null);

            //没有租户维度的价格策略就按照门店查询
            List<MerchantStoreAddressVO> merchantStoreAddressVOS;
            if (ObjectUtil.isNull(tenantStrategy)) {
                // 门店分组获取门店ID
                List<Long> storeIds = new ArrayList<>(new ArrayList<>(storeGroupMap.values().stream()
                    .collect(HashSet::new, HashSet::addAll, HashSet::addAll)));

                storeStrategys.forEach(e -> storeIds.addAll(e.getTargetIds()));
                merchantStoreAddressVOS = storeFacade.batchQueryStoreAddressByIds(marketItem.getTenantId(), storeIds);
            } else {
                merchantStoreAddressVOS = storeFacade.batchQueryStoreAddressFromCache(marketItem.getTenantId());
            }
            if (CollectionUtils.isEmpty(merchantStoreAddressVOS)) {
                return;
            }
            Map<Long, String> addressMap = merchantStoreAddressVOS.stream().collect(Collectors.toMap(MerchantStoreAddressVO::getStoreId, MerchantStoreAddressVO::getAddressKey));
            Set<MerchantAddressDTO> addressDTOS = merchantStoreAddressVOS.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<> (Comparator.comparing(MerchantStoreAddressVO::getAddressKey))),
                    ArrayList::new
            )).stream().map (MerchantStoreConverter::merchantStoreAddress2MerchantAddressDTO).collect(Collectors.toSet ());

            List<CostPriceVO> costCityPriceVOS = costPriceDomianService.listCostPriceBySkuId (marketItem.getSkuId (), addressDTOS, marketItem.getTenantId (), marketItem.getGoodsType ());
            if (CollectionUtils.isEmpty(costCityPriceVOS)) {
                return;
            }
            Map<String, List<CostPriceVO>> costCityPriceMap = costCityPriceVOS.stream().collect(Collectors.groupingBy(CostPriceVO::getAddressKey));
            if (ObjectUtil.isNotNull(tenantStrategy)) {
                BigDecimal strategyValue = tenantStrategy.getStrategyValue();
                BigDecimal maxCostPirce = costCityPriceVOS.stream().map(CostPriceVO::getPrice).max(Comparator.comparing(x -> x)).orElse(null);
                tenantStrategy.setDifferenceValue(strategyValue.compareTo(maxCostPirce) < 0 ? Objects.requireNonNull(maxCostPirce).subtract(strategyValue) : null);
            }

            storeStrategys.forEach(s -> {
                List<String> cityAreas = s.getTargetIds().stream().map(addressMap::get).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                fillStrategyDifferenceValue(costCityPriceMap, s, cityAreas);
            });

            // 门店分组
            storeGroupStrategies.forEach(s -> {
                HashSet<Object> storeIds = s.getTargetIds().stream()
                    .map(storeGroupMap::get)
                    .filter(ObjectUtil::isNotNull)
                    .collect(HashSet::new, HashSet::addAll, HashSet::addAll);
                List<String> cityAreas = storeIds.stream().map(addressMap::get).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
                fillStrategyDifferenceValue(costCityPriceMap, s, cityAreas);
            });
        }
    }

    private void fillNoGoodDifferenceValue(BigDecimal noGoodsSupplyPrice, List<MarketItemPriceStrategyVO> marketItemPriceStrategyVOS) {
        for (MarketItemPriceStrategyVO strategyVO : marketItemPriceStrategyVOS) {
            if (!Objects.equals(strategyVO.getStrategyType(), MarketItemPriceStrategyEnum.StrategyTypeEnum.ASSIGN.getCode())) {
                continue;
            }
            BigDecimal strategyValue = strategyVO.getStrategyValue();
            if (ObjectUtil.isNotNull(strategyValue) && ObjectUtil.isNotNull(noGoodsSupplyPrice)) {
                strategyVO.setDifferenceValue(strategyValue.compareTo(noGoodsSupplyPrice) < 0 ? Objects.requireNonNull(noGoodsSupplyPrice).subtract(strategyValue) : null);
            }
        }
    }

    // 门店和门店分组 填充差价
    private void fillStrategyDifferenceValue(Map<String, List<CostPriceVO>> costCityPriceMap, MarketItemPriceStrategyVO s, List<String> cityAreas) {
        if (CollectionUtils.isNotEmpty(cityAreas)) {
            BigDecimal maxCostPirce = cityAreas.stream().map(cityArea -> {
                List<CostPriceVO> costCityPrices = costCityPriceMap.get(cityArea);
                BigDecimal bigDecimal = null;
                if (CollectionUtils.isNotEmpty(costCityPrices)) {
                    bigDecimal = costCityPrices.stream().map(CostPriceVO::getPrice).max(Comparator.comparing(x -> x)).orElse(null);
                }
                return bigDecimal;
            }).filter(ObjectUtil::isNotNull).max(Comparator.comparing(x -> x)).orElse(null);
            if (ObjectUtil.isNotNull(maxCostPirce)) {
                BigDecimal strategyValue = s.getStrategyValue();
                s.setDifferenceValue(strategyValue.compareTo(maxCostPirce) < 0 ? Objects.requireNonNull(maxCostPirce).subtract(strategyValue) : null);
            }
        }
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     * 覆盖更新价格策略
     * <p>
     * 删除所有的价格策略，增加新的
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePriceStrategy(MarketItemPriceStrategyReq req, Boolean changePriceResultFlag) {
        // 校验sku价格策略是否正常
        List<MarketItemPriceStrategyDTO> priceList = MarketDomainConvert.INSTANCE.convert2PriceStrategyDtos(req.getPriceInputs());
        checkSkuPrice(priceList, req.getTenantId());
        MarketItemParam param = new MarketItemParam();
        param.setTenantId(req.getTenantId());
        param.setItemId(req.getItemId());
        Long skuId;
        Integer goodsType;
        List<MarketItem> marketItems = marketItemDao.listByParam(param);
        if (CollectionUtils.isNotEmpty(marketItems)) {
            MarketItem marketItem = marketItems.get(0);
            marketItem.setId(req.getItemId());
            marketItem.setPriceType(req.getPriceType());
            marketItemDao.updateById(marketItem);
            skuId  = marketItem.getSkuId ();
            goodsType  = marketItem.getGoodsType ();
        } else {
            throw new BizException("商品不存在");
        }
        if (CollectionUtils.isEmpty(req.getPriceInputs())) {
            return;
        }
        String sku = null;
        if(!Objects.isNull (skuId)){
            ProductSkuVO productSkuVO = productFacade.querySkuInfo(skuId);
            if (!Objects.isNull(productSkuVO)) {
                sku = productSkuVO.getSku ();
            }
        }
        // 更新策略
        priceStrategyDomainService.saveOrUpdateMarketItemPriceStrategByItemId(req.getTenantId(), priceList,goodsType, sku);
        // 刷新价格
        ItemChangeMessageDTO dto = new ItemChangeMessageDTO();
        dto.setMarketItemId(req.getItemId());
        dto.setTenantId(req.getTenantId());
        if (changePriceResultFlag) {
            dealService.process(dto);
        }

        if (Objects.nonNull(req.getMarketItemUnfairPriceStrategyReq())) {
            MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO = UnfairPriceStrategyConvert.INSTANCE.convertTO(req.getMarketItemUnfairPriceStrategyReq());
            unfairPriceStrategyDomainService.upsertUnfairPriceStrategy(marketItemUnfairPriceStrategyDTO, req.getTenantId(), req.getItemId());
        }
    }

    /**
     * 更新/插入 租户级别的 价格策略
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantTargetTypePriceStrategy(MarketItemPriceStrategyReq req) {
        List<MarketItemPriceStrategyDTO> priceList = MarketDomainConvert.INSTANCE.convert2PriceStrategyDtos(req.getPriceInputs());
        // 校验sku价格策略是否正常
        checkSkuPrice(priceList, req.getTenantId());
        MarketItemParam param = new MarketItemParam();
        param.setTenantId(req.getTenantId());
        param.setItemId(req.getItemId());
        List<MarketItem> marketItems = marketItemDao.listByParam(param);
        if (CollectionUtils.isEmpty(marketItems)) {
            throw new BizException("商品不存在");
        }
        if (CollectionUtils.isEmpty(req.getPriceInputs())) {
            return;
        }
        MarketItemPriceStrategyDTO strategy = priceList.get (0);
        if(PriceStrategyTypeEnum.ASSIGN.getCode ().equals (strategy.getStrategyType ())){
            List<LadderPrice> ladderPrices = new ArrayList<> ();
            LadderPrice ladderPriceDTO = new LadderPrice ();
            ladderPriceDTO.setPrice(strategy.getStrategyValue());
            ladderPriceDTO.setUnit (1);
            ladderPrices.add (ladderPriceDTO);
            strategy.setPriceStrategyValue (JSON.toJSONString (ladderPrices));
        }else{
            strategy.setPriceStrategyValue (strategy.getStrategyValue ().toPlainString ());
        }
        MarketItemPriceStrategyMappingDetailVO vo = priceStrategyDomainService.listMarketItemPriceStrategyByItemIdAndTargetId(req.getTenantId(), req.getItemId(), MarketItemPriceStrategyEnum.TargetTypeEnum.TENANT.getCode(), req.getTenantId());
        if (ObjectUtil.isNotEmpty(vo)) {
            priceStrategyDomainService.updateMarketItemPriceStrategByItemIdAndTargetId(vo.getMarketItemPriceStrategyId(), strategy);
        } else {
            priceStrategyDomainService.saveMarketItemPriceStrategByItemIdAndTargetId(req.getTenantId(), strategy);
        }
    }

    public MarketItemVO getMarketItemInfoByItemCode(Long tenantId, String itemCode) {
        String key = String.format("%s:%s:%s", RedisKeyConstants.MARKETITEM_ITEMCODE, tenantId, itemCode);
        Object data = redisTemplate.opsForValue().get(key);
        if (data != null) {
            MarketItemVO result = JSON.parseObject((String) data, MarketItemVO.class);
            return result;
        }
        MarketItemParam param = new MarketItemParam();
        param.setTenantId(tenantId);
        param.setItemCode(itemCode);

        List<MarketItem> marketItems = marketItemDao.listByParam(param);
        if (CollectionUtils.isEmpty(marketItems)) {
            return null;
        }
        List<MarketItemVO> marketItemVOS = MarketDomainConvert.INSTANCE.convert2VOs(marketItems);
        MarketItemVO marketItemVO = marketItemVOS.get(0);
        redisTemplate.opsForValue().set(key, JSON.toJSONString(marketItemVO), 2, TimeUnit.HOURS);
        return marketItemVO;
    }

    public List<MarketItemVO> listMarketItemInfoByItemCodes(Long tenantId, List<String> itemCodes) {
        MarketItemParam param = new MarketItemParam();
        param.setTenantId(tenantId);
        param.setItemCodes(itemCodes);

        List<MarketItem> marketItems = marketItemDao.listByParam(param);

        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyList();
        }
        List<MarketItemVO> marketItemVOS = MarketDomainConvert.INSTANCE.convert2VOs(marketItems);
        return marketItemVOS;
    }

    public Page<MarketItemVO> listItemDetailByItemCodes(MarketItemDetail4XmQueryDTO queryDTO) {
        // 查询实体信息
        Page<MarketItem> marketItemPage = listItemDetailEntityByItemCodes(queryDTO);
        if (Objects.isNull(marketItemPage)) {
            return null;
        }
        List<MarketItem> marketItems = marketItemPage.getRecords();
        if (CollectionUtils.isEmpty(marketItems)) {
            return null;
        }
        List<MarketItemVO> marketItemVOS = MarketDomainConvert.INSTANCE.convert2VOs(marketItems);

        // 查询 market market_detail market_item_detail关联数据
        List<Long> marketIds = marketItemVOS.stream().map(MarketItemVO::getMarketId).collect(Collectors.toList());
        List<Market> markets = marketDao.listByIds(marketIds);
        Map<Long, Market> marketMap = markets.stream().collect(Collectors.toMap(Market::getId, Function.identity()));

        Map<Long, MarketDetail> marketDetailMap = new HashMap<>();
        Map<Long, MarketItemDetail> marketItemDetailMap = new HashMap<>();


        //如果是鲜沐的商品，查询 market_detail market_item_detail
        if (Objects.equals(queryDTO.getTenantId(), xmTenantId)) {
            List<Long> marketItemIds = marketItemVOS.stream().map(MarketItemVO::getId).collect(Collectors.toList());

            List<MarketDetail> marketDetails = marketDetailDao.listByParam(MarketDetailParam.builder()
                .marketIds(marketIds)
                .build());
            marketDetailMap = marketDetails.stream().collect(Collectors.toMap(MarketDetail::getMarketId, Function.identity()));

            List<MarketItemDetail> marketItemDetails = marketItemDetailDao.listByParam(MarketItemDetailParam.builder()
                .marketItemIds(marketItemIds)
                .build());
            marketItemDetailMap = marketItemDetails.stream().collect(Collectors.toMap(MarketItemDetail::getMarketItemId, Function.identity()));
        }

        // 补充 market market_detail market_item_detail关联数据
        for (MarketItemVO marketItemVO : marketItemVOS) {
            buildMarket(marketMap, marketItemVO);

            buildMarketDetail(marketDetailMap, marketItemVO);

            buildMarketItemDetail(marketItemDetailMap, marketItemVO);
        }

        Page<MarketItemVO> marketItemVOPage = MarketDomainConvert.INSTANCE.convert2PageItems(marketItemPage);
        marketItemVOPage.setRecords(marketItemVOS);
        return marketItemVOPage;
    }

    /**
     * 查询market_item
     *
     * @param queryDTO
     * @return
     */
    private Page<MarketItem> listItemDetailEntityByItemCodes(MarketItemDetail4XmQueryDTO queryDTO) {
        Long tenantId = queryDTO.getTenantId();

        Set<Long> marketIds = Collections.emptySet ();
        // 查询market_detail
        if (ObjectUtil.isNotNull(queryDTO.getMarketOutId()) || CollectionUtils.isNotEmpty (queryDTO.getMarketOutIds())) {
            List<MarketDetail> marketDetails = marketDetailDao.listByParam(MarketDetailParam.builder()
                .outId(queryDTO.getMarketOutId())
                .outIds(queryDTO.getMarketOutIds())
                .build());
            if (CollectionUtils.isEmpty(marketDetails)) {
                return null;
            }
            marketIds = marketDetails.stream ().map (MarketDetail::getMarketId).collect(Collectors.toSet ());
        }


        MarketItemXmParam param = MarketItemXmParam.builder()
            .tenantId(tenantId)
            .itemCodeList(queryDTO.getItemCodeList())
            .marketIds(new ArrayList<> (marketIds))
            .categoryIds(queryDTO.getCategoryIds())
            .marketTitle(queryDTO.getMarketTitle())
            .deleteFlag(queryDTO.getDeleteFlag())
            .onSaleFlag(queryDTO.getOnSaleFlag())
            .onSaleTargetId(queryDTO.getOnSaleTargetId())
            .sortDescList(queryDTO.getSortDescList())
            .adminShow(queryDTO.getAdminShow())
            .pageNum(queryDTO.getPageNum())
            .pageSize(queryDTO.getPageSize())
            .build();
        // 查询market_item
        return marketItemDao.pageMarketItemByParam(param);
    }

    private void buildMarketItemDetail(Map<Long, MarketItemDetail> marketItemDetailMap, MarketItemVO marketItemVO) {
        MarketItemDetail marketItemDetail = marketItemDetailMap.getOrDefault(marketItemVO.getMarketItemId(), new MarketItemDetail());
        marketItemVO.setExtType(marketItemDetail.getExtType());
        marketItemVO.setBaseSaleUnit(marketItemDetail.getBaseSaleUnit());
        marketItemVO.setAveragePriceFlag(marketItemDetail.getAveragePriceFlag());
        marketItemVO.setSamplePool(marketItemDetail.getSamplePool());
        marketItemVO.setCustomerId(marketItemDetail.getCustomerId());
        marketItemVO.setOutId(marketItemDetail.getOutId());
        marketItemVO.setSalePropertyDesc (marketItemDetail.getSalePropertyDesc ());
        marketItemVO.setQuoteType (marketItemDetail.getQuoteType ());
    }

    private void buildMarketDetail(Map<Long, MarketDetail> marketDetailMap, MarketItemVO marketItemVO) {
        MarketDetail marketDetail = marketDetailMap.getOrDefault(marketItemVO.getMarketId(), new MarketDetail());
        marketItemVO.setAfterSaleReason(marketDetail.getAfterSaleReason());
        marketItemVO.setAfterSaleTime(marketDetail.getAfterSaleTime());
        marketItemVO.setRefundReason(marketDetail.getRefundReason());
        marketItemVO.setMarketOutId(marketDetail.getOutId());
        marketItemVO.setDescription(marketDetail.getDescription());
        marketItemVO.setSlogan(marketDetail.getSlogan());
    }

    private void buildMarket(Map<Long, Market> marketMap, MarketItemVO marketItemVO) {
        Market market = marketMap.getOrDefault(marketItemVO.getMarketId(), new Market());
        marketItemVO.setCategoryId(market.getCategoryId());
        marketItemVO.setMarketTitle(market.getTitle());
        marketItemVO.setMarketSubTitle(market.getSubTitle());
        marketItemVO.setMarketMainPicture(market.getMainPicture());
        marketItemVO.setMarketDetailPicture(market.getDetailPicture());
        marketItemVO.setMarketDeleteFlag(String.valueOf(market.getDeleteFlag()));
        marketItemVO.setDescriptionString(market.getDescriptionString());
    }

    private void handleClassificationName(List<MarketItemWithClassificationResp> marketItemWithClassificationResps, List<Long> marketIds) {
        Map<Long, ItemClassificationDTO> itemClassMap = classificationDomainService.getItemClassification(null, marketIds);
        marketItemWithClassificationResps.forEach(resp -> {
            ItemClassificationDTO classification = itemClassMap.get(resp.getMarketId());
            if (classification == null) {
                return;
            }
            resp.setFirstClassificationName(classification.getFirstClassificationName());
            resp.setSecondClassificationName(classification.getSecondClassificationName());
        });
    }

    private List<Long> handleClassification(MarketItemWithClassificationQueryReq queryReq) {
        if (CollectionUtils.isEmpty(queryReq.getClassificationIds())) {
            return null;
        }

        List<MarketClassification> marketClassifications = classificationDomainService.queryChildList(queryReq.getClassificationIds().get(0));
        if (CollectionUtils.isEmpty(marketClassifications)) {
            return null;
        }

        List<Long> classificationIds = marketClassifications.stream().map(MarketClassification::getId).collect(Collectors.toList());
        return classificationIds;
    }


    public List<MarketItemPriceStrategyUpdateResultVO> batchUpdatePriceStrategy(List<MarketItemPriceStrategyReq> reqs) {
        List<MarketItemPriceStrategyUpdateResultVO> result = new ArrayList<>();
        List<CompletableFuture<MarketItemPriceStrategyUpdateResultVO>> futures = new ArrayList<>();
        for (MarketItemPriceStrategyReq req : reqs) {
            CompletableFuture<MarketItemPriceStrategyUpdateResultVO> future = CompletableFuture.supplyAsync(() -> {
                MarketItemPriceStrategyUpdateResultVO vo = new MarketItemPriceStrategyUpdateResultVO();
                vo.setItemId(req.getItemId());
                try {
                    updatePriceStrategy(req, false);
                    vo.setSuccessFlag(true);
                } catch (ParamsException e) {
                    vo.setSuccessFlag(false);
                    vo.setErrorMessage(e.getMessage());
                } catch (BizException e) {
                    vo.setSuccessFlag(false);
                    vo.setErrorMessage(e.getMessage());
                } catch (Exception e) {
                    vo.setSuccessFlag(false);
                    log.error("batchUpdatePriceStrategy异常", e);
                    vo.setErrorMessage("系统异常");
                }
                return vo;
            }, threadPoolTaskExecutor);
            futures.add(future);

            try {
                //方法处理完成回调
                future.thenAccept((MarketItemPriceStrategyUpdateResultVO vo) -> result.add(vo));
            } catch (Exception e) {
                log.error("batchUpdatePriceStrategy回掉异常", e);
            }
        }
        //等待所有线程执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]))
            .whenComplete((v, th) -> log.info("batchUpdatePriceStrategy更新结束")).join();
        //返回结果
        return result;
    }

    public List<MarketItemPriceStrategyUpdateResultVO> batchUpdateTenantTargetTypePriceStrategy(List<MarketItemPriceStrategyReq> reqs) {
        if (CollectionUtils.isEmpty(reqs)) {
            return Collections.emptyList();
        }
        List<MarketItemPriceStrategyUpdateResultVO> result = new ArrayList<>();
        List<CompletableFuture<MarketItemPriceStrategyUpdateResultVO>> futures = new ArrayList<>();
        for (MarketItemPriceStrategyReq req : reqs) {
            CompletableFuture<MarketItemPriceStrategyUpdateResultVO> future = CompletableFuture.supplyAsync(() -> {
                MarketItemPriceStrategyUpdateResultVO vo = new MarketItemPriceStrategyUpdateResultVO();
                vo.setItemId(req.getItemId());
                try {
                    updateTenantTargetTypePriceStrategy(req);
                    vo.setSuccessFlag(true);
                } catch (ParamsException e) {
                    vo.setSuccessFlag(false);
                    vo.setErrorMessage(e.getMessage());
                } catch (BizException e) {
                    vo.setSuccessFlag(false);
                    vo.setErrorMessage(e.getMessage());
                } catch (Exception e) {
                    vo.setSuccessFlag(false);
                    log.error("batchUpdatePriceStrategy异常", e);
                    vo.setErrorMessage("系统异常");
                }
                return vo;
            }, threadPoolTaskExecutor);
            futures.add(future);

            try {
                //方法处理完成回调
                future.thenAccept((MarketItemPriceStrategyUpdateResultVO vo) -> result.add(vo));
            } catch (Exception e) {
                log.error("batchUpdatePriceStrategy回掉异常", e);
            }
        }
        //等待所有线程执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]))
            .whenComplete((v, th) -> log.info("batchUpdatePriceStrategy更新结束")).join();
        //返回结果
        return result;
    }


    public BatchOnSaleResultVO batchChangOnSaleIncludeAllStatus(MarketItemOnSaleInputDTO soldOut, MarketItemOnSaleInputDTO onSale) {
        Integer successCount = 0;
        Integer failCount = 0;
        List<Long> successItemIds = new ArrayList<>();
        List<OnSaleFailResultVO> resultList = new ArrayList<>();

        if (ObjectUtil.isNotNull(soldOut)) {
            BatchOnSaleResultVO soldOutVO = batchChangOnSale(soldOut);
            log.info("soldOutVO={}", JSON.toJSONString(soldOutVO));
            successCount = successCount + soldOutVO.getSuccessCount();
            failCount = failCount + soldOutVO.getFailCount();
            if (CollectionUtils.isNotEmpty(soldOutVO.getSuccessItemIds())) {
                successItemIds.addAll(soldOutVO.getSuccessItemIds());
            }
            if (CollectionUtils.isNotEmpty(soldOutVO.getResultList())) {
                resultList.addAll(soldOutVO.getResultList());
            }
        }
        if (ObjectUtil.isNotNull(onSale)) {
            BatchOnSaleResultVO onSaleVO = batchChangOnSale(onSale);
            log.info("onSaleVO={}", JSON.toJSONString(onSaleVO));
            successCount = successCount + onSaleVO.getSuccessCount();
            failCount = failCount + onSaleVO.getFailCount();
            if (CollectionUtils.isNotEmpty(onSaleVO.getSuccessItemIds())) {
                successItemIds.addAll(onSaleVO.getSuccessItemIds());
            }
            if (CollectionUtils.isNotEmpty(onSaleVO.getResultList())) {
                resultList.addAll(onSaleVO.getResultList());
            }
        }

        BatchOnSaleResultVO reslut = new BatchOnSaleResultVO();
        reslut.setSuccessCount(successCount);
        reslut.setFailCount(failCount);
        reslut.setSuccessItemIds(successItemIds);
        reslut.setResultList(resultList);
        return reslut;
    }

    public List<MarketItemVO> queryAssociatedItemBySkuIds(Set<Long> skuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(skuIds) || tenantId == null) {
            throw new ParamsException("参数异常");
        }
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(tenantId,skuIds);
        if (CollectionUtil.isNotEmpty(marketItems)) {
            Set<Long> marketIds = marketItems.stream().map(MarketItem::getMarketId).collect(Collectors.toSet());
            List<Market> markets = marketDao.listByIds(marketIds);
            Map<Long, String> marketMap = markets.stream().collect(Collectors.toMap(Market::getId, Market::getTitle));
            List<MarketItemVO> marketItemVOS = MarketDomainConvert.INSTANCE.convert2VOs(marketItems);
            // 价格
            Map<Long, PriceRangeDTO> priceRangeDtoMap = getPriceRangeMap(marketItemVOS);
            // 返回map
            for (MarketItemVO itemVO : marketItemVOS) {
                itemVO.setPriceStr(Objects.nonNull(priceRangeDtoMap.get(itemVO.getId())) ? priceRangeDtoMap.get(itemVO.getId()).getPriceStr() : "");
                if (CollectionUtil.isNotEmpty(marketMap)) {
                    itemVO.setTitle(marketMap.get(itemVO.getMarketId()));
                }
            }
            return marketItemVOS;
        } else {
            return Collections.emptyList();
        }
    }


    public List<MarketItemVO> listMarketItemInfoBySkuIds(Long tenantId, Collection<Long> skuIds) {
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(tenantId, skuIds);

        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyList();
        }

        Set<Long> marketIds = marketItems.stream().map(MarketItem::getMarketId).collect(Collectors.toSet());
        List<Market> markets = marketDao.listByIds(marketIds);
        Map<Long, Market> marketMap = markets.stream().collect(Collectors.toMap(Market::getId, Function.identity(), (v1, v2) -> v1));
        List<MarketItemVO> marketItemVOS = MarketDomainConvert.INSTANCE.convert2VOs(marketItems);
        for (MarketItemVO itemVO : marketItemVOS) {
            Market market = marketMap.get(itemVO.getMarketId());
            if (market != null) {
                itemVO.setTitle(market.getTitle());
                itemVO.setMainPicture(market.getMainPicture());
            }
        }

        return marketItemVOS;
    }

    public void updateItemCode(String itemCode, Long marketItemId, Long tenantId) {
        MarketItem marketItem = marketItemDao.getById(marketItemId);
        if (ObjectUtil.isNotNull (marketItem)) {
            marketItem.setItemCode (itemCode);
            marketItemDao.updateById (marketItem);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public BatchChangNoGoodsSupplyPriceVO batchChangNoGoodsSupplyPrice(Long tenantId, List<ItemNoGoodsSupplyPriceReq> list) {
        List<Long> itemIds = list.stream ().map (ItemNoGoodsSupplyPriceReq::getItemId).collect (Collectors.toList ());
        List<MarketItem> marketItems = marketItemDao.listByIds(itemIds);
        List<MarketItem> marketItemUpateList = new ArrayList<> ();

        BatchChangNoGoodsSupplyPriceVO vo = new BatchChangNoGoodsSupplyPriceVO ();
        if(CollectionUtil.isEmpty (marketItems)){
            vo.setSuccessCount (0);
            vo.setFailCount (itemIds.size ());
            vo.setSuccessItemIds (Collections.emptyList ());
            List<ChangNoGoodsSupplyPriceFailResultVO> resultList = list.stream ().map (e -> {
                ChangNoGoodsSupplyPriceFailResultVO failVO = new ChangNoGoodsSupplyPriceFailResultVO ();
                failVO.setItemId (e.getItemId ());
                failVO.setFailReason ("商品不存在");
                return failVO;
            }).collect (Collectors.toList ());
            vo.setResultList (resultList);
        }else {
            List<Long> successItemIds = itemIds;
            List<ChangNoGoodsSupplyPriceFailResultVO> resultList = new ArrayList<> ();
            Map<Long, MarketItem> itemMap = marketItems.stream ().collect (Collectors.toMap (MarketItem::getId, e -> e));
            Map<Long, ItemNoGoodsSupplyPriceReq> reqMap = list.stream ().collect (Collectors.toMap (ItemNoGoodsSupplyPriceReq::getItemId, e -> e));
            reqMap.forEach ((id, req) -> {
                MarketItem marketItem = itemMap.get (id);
                if (Objects.isNull (marketItem) || !Objects.equals (marketItem.getTenantId (), tenantId)) {
                    successItemIds.remove (id);
                    ChangNoGoodsSupplyPriceFailResultVO failVO = new ChangNoGoodsSupplyPriceFailResultVO ();
                    failVO.setItemId (id);
                    failVO.setFailReason ("商品不存在");
                    resultList.add (failVO);
                } else {
                    if (!Objects.equals (marketItem.getGoodsType (), GoodsTypeEnum.NO_GOOD_TYPE.getCode ())) {
                        successItemIds.remove (id);
                        ChangNoGoodsSupplyPriceFailResultVO failVO = new ChangNoGoodsSupplyPriceFailResultVO ();
                        failVO.setItemId (id);
                        failVO.setFailReason ("该商品供货模式还不是非总部供货");
                        resultList.add (failVO);
                    } else {
                        marketItem.setNoGoodsSupplyPrice (req.getNoGoodsSupplyPrice ());
                        marketItem.setSupplierId (String.valueOf (req.getSupplierId ()));
                        marketItem.setSupplierName (req.getSupplierName ());
                        marketItemUpateList.add (marketItem);
                    }
                }
            });
            if (CollectionUtil.isNotEmpty (marketItemUpateList)) {
                marketItemDao.updateBatchById (marketItemUpateList);
            }
            vo.setSuccessCount (successItemIds.size ());
            vo.setFailCount (resultList.size ());
            vo.setSuccessItemIds (successItemIds);
            vo.setResultList (resultList);
        }
        return vo;
    }

    public Integer countOnsaleItem(Long tenantId) {
        return marketItemDao.countOnsaleItem(tenantId);
    }

    public Map<Long,Long> querySkuIdByItemIds(Long tenantId, List<Long> itemIds) {
        if(CollectionUtils.isEmpty (itemIds)){
            return Collections.emptyMap ();
        }
        if(itemIds.size () > 50){
           throw new BizException ("数量过多，分批查询！");
        }
        MarketItemParam param = new MarketItemParam ();
        param.setTenantId (tenantId);
        param.setItemIds (itemIds);
        return marketItemDao.listByParam (param).stream().filter (e->ObjectUtil.isNotEmpty (e.getSkuId ()) && !Objects.equals (e.getSkuId (), MarketCombineConstants.NULL_SKU)).collect (Collectors.toMap (MarketItem::getId,MarketItem::getSkuId));
    }

    public Map<Long,OnSaleTypeEnum> queryItemOnsaleFlagByItemIds(Long tenantId, List<Long> itemIds, List<Long> targetIds) {
        if(CollectionUtils.isEmpty (itemIds)){
            return Collections.emptyMap ();
        }
        if(itemIds.size () > 50){
            throw new BizException ("数量过多，分批查询！");
        }
        if(!xmTenantId.equals(tenantId)) {
            MarketItemParam param = new MarketItemParam ();
            param.setTenantId (tenantId);
            param.setItemIds (itemIds);
            return marketItemDao.listByParam (param).stream ().collect (Collectors.toMap (MarketItem::getId, x -> OnSaleTypeEnum.of(x.getOnSale ())));
        }else{
            return onSaleStrategyDomainService.listMarketItemOnSale (tenantId, itemIds,targetIds);
        }
    }

    public Map<Long,OnSaleTypeEnum> queryItemOnsaleFlagBySkuIds(Long tenantId, List<Long> skuIds,List<Long> targetIds) {
        if(CollectionUtils.isEmpty (skuIds)){
            return Collections.emptyMap ();
        }
        if(skuIds.size () > 50){
            throw new BizException ("数量过多，分批查询！");
        }
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(tenantId,skuIds);
        if(CollectionUtils.isEmpty (marketItems)){
            return Collections.emptyMap ();
        }
        if(!xmTenantId.equals(tenantId)) {
            return marketItems.stream().collect(Collectors.groupingBy(MarketItem::getSkuId,Collectors.collectingAndThen(Collectors.toList(), this::getOnSaleStatus)));
        }else{
            Map<Long,OnSaleTypeEnum> reslut = new HashMap<> ();
            Map<Long, OnSaleTypeEnum> itemMap = onSaleStrategyDomainService.listMarketItemOnSale (tenantId, marketItems.stream ().map (MarketItem::getId).collect (Collectors.toList ()),targetIds);
            Map<Long, List<Long>> skuItemMap = marketItems.stream().collect(Collectors.groupingBy(MarketItem::getSkuId, Collectors.mapping(MarketItem::getId, Collectors.toList())));
            skuItemMap.forEach ((skuId,itemIds)-> reslut.put(skuId,itemIds.stream().anyMatch (e->itemMap.get (e).equals (OnSaleTypeEnum.ON_SALE))?OnSaleTypeEnum.ON_SALE:OnSaleTypeEnum.SOLD_OUT));
            return reslut;
        }
    }

    public Map<Long, MTypeEnum> queryItemMTypeFlagBySkuIds(Long tenantId, List<Long> skuIds, List<Long> targetIds) {
        if(CollectionUtils.isEmpty (skuIds)){
            return Collections.emptyMap ();
        }
        if(skuIds.size () > 50){
            throw new BizException ("数量过多，分批查询！");
        }
        List<MarketItem> marketItems = marketItemDao.listBySkuIdsAndTenantId(tenantId,skuIds);
        if(CollectionUtils.isEmpty (marketItems)){
            return Collections.emptyMap ();
        }
        if(!xmTenantId.equals(tenantId)) {
            return marketItems.stream().collect(Collectors.groupingBy(
                    MarketItem::getSkuId,Collectors.collectingAndThen(Collectors.toList(), this::getMType)));
        }else{
            Map<Long,MTypeEnum> result = new HashMap<> ();
            Map<Long, MTypeEnum> itemMap = onSaleStrategyDomainService.listMarketItemMType (tenantId, marketItems.stream ().map (MarketItem::getId).collect (Collectors.toList ()),targetIds);
            Map<Long, List<Long>> skuItemMap = marketItems.stream().collect(Collectors.groupingBy(MarketItem::getSkuId, Collectors.mapping(MarketItem::getId, Collectors.toList())));
            skuItemMap.forEach ((skuId,itemIds)-> result.put(skuId,itemIds.stream().anyMatch (e->itemMap.get (e).equals (MTypeEnum.BIG_CUSTOMER_EXCLUSIVITY))?MTypeEnum.BIG_CUSTOMER_EXCLUSIVITY:MTypeEnum.NOT_BIG_CUSTOMER_EXCLUSIVITY));
            return result;
        }
    }

    private OnSaleTypeEnum getOnSaleStatus(List<MarketItem> marketItems) {
        if (marketItems.stream().anyMatch(i -> OnSaleTypeEnum.ON_SALE.getCode().equals(i.getOnSale()))) {
            return OnSaleTypeEnum.ON_SALE;
        } else {
            return OnSaleTypeEnum.SOLD_OUT;
        }
    }

    private MTypeEnum getMType(List<MarketItem> marketItems) {
        return MTypeEnum.NOT_BIG_CUSTOMER_EXCLUSIVITY;
    }

    /***
     * @author: lzh
     * @description: 根据条件查询商品详情信息
     * @date: 2024/4/28 16:47
     * @param: [outIds]
     * @return: java.util.List<com.cosfo.item.web.domain.vo.MarketItemDetailVO>
     **/
    public List<MarketItemDetailVO> getItemDetailByOutId(List<Long> outIds) {
        if (CollectionUtils.isEmpty(outIds)) {
            throw new ParamsException("outIds不能为空！");
        }
        List<MarketItemDetail> marketItemDetails = marketItemDetailDao.getByOutIds(outIds);
        if (CollectionUtils.isEmpty(marketItemDetails)) {
            return Collections.emptyList();
        }

        //组装标签信息并返回
        return MarketItemDetailConverter.marketItemDetailsToVO(marketItemDetails);
    }

    /***
     * @author: lzh
     * @description: 保存商品标签信息 根据标签名称查询标签表-》有则获取已有标签ID，没有就新增
     * @date: 2024/4/28 11:15
     * @param: [itemDTO]
     * @return: void
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveItemLabel(MarketItemDTO itemDTO) {
        checkSaveItemLabelParam (itemDTO);

        //根据商品ID查询商品明细--获取原有标签信息
        MarketItemDetail marketItemDetail = marketItemDetailDao.getByOutId (itemDTO.getOutId ());
        if (Objects.isNull (marketItemDetail)) {
            throw new BizException ("商品marketItemDetail信息不存在！");
        }

        //根据itemCode查询商品信息
        MarketItem marketItem = marketItemDao.getById (marketItemDetail.getMarketItemId ());
        if (Objects.isNull (marketItem)) {
            throw new BizException ("商品信息不存在！");
        }

        String itemLabel = itemDTO.getItemLabel ();
        if (!StringUtils.isEmpty (itemLabel)) {
            List<ItemLabelDTO> itemLabelDTOS = JSON.parseArray (itemLabel, ItemLabelDTO.class);
            List<Long> itemLabelIds = itemLabelDTOS.stream ().map (ItemLabelDTO::getId).collect (Collectors.toList ());
            if (!CollectionUtils.isEmpty (itemLabelIds)) {
                List<MarketItemLabel> marketItemLabels = marketItemLabelDao.getByIds (itemLabelIds);
                if (!CollectionUtils.isEmpty (marketItemLabels)) {
                    Set<Long> dbIds = marketItemLabels.stream ().map (MarketItemLabel::getId).collect (Collectors.toSet ());
                    Set<ItemLabelDTO> haveList = itemLabelDTOS.stream ().filter (e -> dbIds.contains (e.getId ())).collect (Collectors.toSet ());
                    if (!CollectionUtils.isEmpty (haveList)) {
                        itemLabel = JSON.toJSONString (haveList);
                    }
                }
            } else {
                itemLabel = null;
            }
        }

        //更新、新增商品标签信息
        marketItemDetail.setItemLabel (itemLabel);
        marketItemDetailDao.updateItemLabelById (marketItemDetail);
    }

    /***
     * @author: lzh
     * @description: 校验保存商品标签信息参数
     * @date: 2024/4/28 11:28
     * @param: [itemDTO]
     * @return: void
     **/
    private void checkSaveItemLabelParam(MarketItemDTO itemDTO) {
        if (Objects.isNull(itemDTO)) {
            throw new ParamsException("保存商品标签入参异常！");
        }
        if (Objects.isNull(itemDTO.getItemCode())) {
            throw new ParamsException("保存商品标签itemCode不能为空！");
        }
        if (Objects.isNull(itemDTO.getItemLabel())) {
            throw new ParamsException("保存商品标签itemLabel不能为空！");
        }
        if (Objects.isNull(itemDTO.getTenantId())) {
            throw new ParamsException("保存商品标签tenantId不能为空！");
        }
        if (Objects.isNull(itemDTO.getOutId())) {
            throw new ParamsException("保存商品标签outId不能为空！");
        }
    }

    public List<MarketItemOnsaleStrategyMappingVO> queryOnsaleStrategyByItemCode(Long tenantId, String itemCode) {
        List<MarketItem> marketItems = marketItemDao.getByItemCode (tenantId, itemCode);
        if (CollectionUtils.isEmpty(marketItems)) {
            return null;
        }
        MarketItem marketItem = marketItems.get(0);
        if (Objects.nonNull(tenantId) && !tenantId.equals(marketItem.getTenantId())) {
            return null;
        }
        List<MarketItemOnsaleStrategyMappingVO> marketItemOnsaleStrategyVOS = onSaleStrategyDomainService.listMarketItemOnsaleStrategyMappingByItemIds (tenantId, Collections.singletonList (marketItem.getId ()));
        return marketItemOnsaleStrategyVOS;
    }

    public Map<String, List<MarketItemOnsaleStrategyMappingVO>> queryOnsaleStrategyByItemCodeBatch(Long tenantId, List<String> itemCodes) {
        List<MarketItem> marketItems = marketItemDao.getByItemCodes (tenantId,itemCodes);
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyMap ();
        }
        Map<Long, List<MarketItem>> itemMap = marketItems.stream ().collect (Collectors.groupingBy (MarketItem::getId));

        List<MarketItemOnsaleStrategyMappingVO> marketItemOnsaleStrategyVOS = onSaleStrategyDomainService.listMarketItemOnsaleStrategyMappingByItemIds (tenantId,new ArrayList<> (itemMap.keySet ()));
        if (CollectionUtils.isEmpty(marketItemOnsaleStrategyVOS)) {
            return Collections.emptyMap ();
        }
        Map<Long, List<MarketItemOnsaleStrategyMappingVO>> itemOnsaleMap = marketItemOnsaleStrategyVOS.stream ().collect (Collectors.groupingBy (MarketItemOnsaleStrategyMappingVO::getItemId));

        Map<String,List<MarketItemOnsaleStrategyMappingVO>> result = new HashMap<> ();
        itemOnsaleMap.forEach ((k,v)-> {
            List<MarketItem> i = itemMap.get (k);
            if(CollectionUtil.isNotEmpty (i)){
                result.put (i.get (0).getItemCode (),v);
            }
        });
        return result;
    }

    public Map<String, List<MarketItemOnsaleStrategyMappingVO>> queryOnsaleStrategyByCommonQuery(OnsaleStrategyCommonQueryReq req) {
        Long tenantId = req.getTenantId ();
        if(tenantId == null){
            throw new BizException ("租户id不能空");
        }
        Map<Long, List<MarketItem>> itemMap = Collections.emptyMap ();
        List<String> itemCodes = req.getItemCodes ();
        if(CollectionUtil.isNotEmpty (itemCodes)){
            List<MarketItem> marketItems = marketItemDao.getByItemCodes (tenantId,itemCodes);
            if (CollectionUtils.isEmpty(marketItems)) {
                return Collections.emptyMap ();
            }
            itemMap = marketItems.stream ().collect (Collectors.groupingBy (MarketItem::getId));
        }
        List<Long> itemIds = req.getItemIds ();
        if(CollectionUtil.isNotEmpty (itemIds) && CollectionUtil.isNotEmpty (itemMap)) {
            List<Long> ids = itemIds.stream()
                    .filter(itemMap.keySet ()::contains)
                    .collect(Collectors.toList());
            req.setItemIds (ids);
        }


        List<MarketItemOnsaleStrategyMappingVO> marketItemOnsaleStrategyVOS = onSaleStrategyDomainService.listMarketItemOnsaleStrategyMappingByCommonQuery (tenantId,req);
        if (CollectionUtils.isEmpty(marketItemOnsaleStrategyVOS)) {
            return Collections.emptyMap ();
        }

        List<MarketItem> marketItems = marketItemDao.listByIds (marketItemOnsaleStrategyVOS.stream().map (MarketItemOnsaleStrategyMappingVO::getItemId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyMap ();
        }
        itemMap = marketItems.stream ().collect (Collectors.groupingBy (MarketItem::getId));



        Map<Long, List<MarketItemOnsaleStrategyMappingVO>> itemOnsaleMap = marketItemOnsaleStrategyVOS.stream ().collect (Collectors.groupingBy (MarketItemOnsaleStrategyMappingVO::getItemId));

        Map<String,List<MarketItemOnsaleStrategyMappingVO>> result = new HashMap<> ();
        Map<Long, List<MarketItem>> finalItemMap = itemMap;
        itemOnsaleMap.forEach ((k, v)-> {
            List<MarketItem> i = finalItemMap.get (k);
            if(CollectionUtil.isNotEmpty (i)){
                result.put (i.get (0).getItemCode (),v);
            }
        });
        return result;
    }

    public List<MarketItemDetailVO> queryMarketItemLabelByItemCodes(Long tenantId, Set<String> itemCodes) {

        if (CollectionUtils.isEmpty(itemCodes)) {
            throw new ParamsException("itemCodes不能null！");
        }
        List<MarketItem> byItemCodes = marketItemDao.getByItemCodes (tenantId, new ArrayList<> (itemCodes));
        if (CollectionUtils.isEmpty(byItemCodes)) {
            return Collections.emptyList ();
        }
        Map<Long, MarketItem> itemMap = byItemCodes.stream ().collect (Collectors.toMap (MarketItem::getId, Function.identity ()));

        List<MarketItemDetail> marketItemDetails = marketItemDetailDao.listByParam(MarketItemDetailParam.builder()
                .marketItemIds (byItemCodes.stream().map (MarketItem::getId).collect(Collectors.toList()))
                .build());
        if(CollectionUtils.isNotEmpty (marketItemDetails)){
            List<MarketItemDetailVO> collect = marketItemDetails.stream ().map (itemDetail -> {
                MarketItemDetailVO marketItemDetailVO = new MarketItemDetailVO ();
                marketItemDetailVO.setOutId (itemDetail.getOutId ());
                marketItemDetailVO.setItemCode (itemMap.get (itemDetail.getMarketItemId ()).getItemCode ());
                marketItemDetailVO.setItemLabel (itemDetail.getItemLabel ());
                return marketItemDetailVO;
            }).collect (Collectors.toList ());
            return collect;
        }
        return Collections.emptyList ();
    }
}
