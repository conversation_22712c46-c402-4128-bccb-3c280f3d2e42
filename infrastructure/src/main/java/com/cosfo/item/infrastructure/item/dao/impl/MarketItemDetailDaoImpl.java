package com.cosfo.item.infrastructure.item.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.item.dao.MarketItemDetailDao;
import com.cosfo.item.infrastructure.item.dto.MarketItemDetailParam;
import com.cosfo.item.infrastructure.item.mapper.MarketItemDetailMapper;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 商品item扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class MarketItemDetailDaoImpl extends ServiceImpl<MarketItemDetailMapper, MarketItemDetail> implements MarketItemDetailDao {


    @Override
    public List<MarketItemDetail> listByParam(MarketItemDetailParam param) {
        return list(buildQueryParam(param));
    }

    @Override
    public MarketItemDetail getByMarketItemId(Long itemId) {
        LambdaQueryWrapper<MarketItemDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(itemId), MarketItemDetail::getMarketItemId, itemId);
        return getOne(queryWrapper);
    }

    @Override
    public MarketItemDetail getByOutId(Long outId) {
        LambdaQueryWrapper<MarketItemDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(outId), MarketItemDetail::getOutId, outId);
        return getOne(queryWrapper);
    }

    @Override
    public List<MarketItemDetail> getByOutIds(List<Long> outIds) {
        LambdaQueryWrapper<MarketItemDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(outIds), MarketItemDetail::getOutId, outIds);
        return list(queryWrapper);
    }

    @Override
    public List<MarketItemDetail> getByIds(List<Long> ids) {
        LambdaQueryWrapper<MarketItemDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(ids), MarketItemDetail::getId, ids);
        return list(queryWrapper);
    }

    @Override
    public void updateItemLabelById(MarketItemDetail marketItemDetail) {
        LambdaUpdateWrapper<MarketItemDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MarketItemDetail::getId, marketItemDetail.getId());
        updateWrapper.set(MarketItemDetail::getItemLabel, marketItemDetail.getItemLabel());
        update(updateWrapper);
    }

    private LambdaQueryWrapper<MarketItemDetail> buildQueryParam(MarketItemDetailParam param) {
        LambdaQueryWrapper<MarketItemDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getOutId()), MarketItemDetail::getOutId, param.getOutId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getMarketItemIds()), MarketItemDetail::getMarketItemId, param.getMarketItemIds());

        return queryWrapper;
    }
}
