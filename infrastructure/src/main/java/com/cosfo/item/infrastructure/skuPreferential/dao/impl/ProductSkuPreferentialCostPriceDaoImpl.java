package com.cosfo.item.infrastructure.skuPreferential.dao.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.common.skuPreferential.dto.ProductSkuPreferentialBasicDTO;
import com.cosfo.item.common.skuPreferential.enums.DeletedEnum;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.infrastructure.skuPreferential.dao.ProductSkuPreferentialCostPriceDao;
import com.cosfo.item.infrastructure.skuPreferential.mapper.ProductSkuPreferentialCostPriceMapper;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPrice;
import com.cosfo.item.infrastructure.skuPreferential.param.ProductSkuPreferentialQueryParam;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 省心定报价 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Service
public class ProductSkuPreferentialCostPriceDaoImpl extends ServiceImpl<ProductSkuPreferentialCostPriceMapper, ProductSkuPreferentialCostPrice> implements ProductSkuPreferentialCostPriceDao {

    @Override
    public Page<ProductSkuPreferentialCostPrice> pagePreferentialCostPriceByParam(ProductSkuPreferentialQueryParam param) {

        Page<ProductSkuPreferentialCostPrice> page = new Page<>(param.getPageIndex(), param.getPageSize());
        MPJLambdaWrapper<ProductSkuPreferentialCostPrice> queryWrapper = builderQueryCondition(param);
        queryWrapper.groupBy(ProductSkuPreferentialCostPrice::getSkuId);
        queryWrapper.orderByDesc(ProductSkuPreferentialCostPrice::getId);
        return page(page, queryWrapper);
    }

    @Override
    public List<ProductSkuPreferentialCostPrice> listPreferentialCostPriceByParam(ProductSkuPreferentialQueryParam param) {
        return list(buildSkuPreferentialCostPriceQueryWrapper(param));
    }


    @Override
    public ProductSkuPreferentialBasicDTO queryBasicData(Long tenantId, List<Long> skuIds, Boolean availableFlag, Boolean waitValid) {
        return baseMapper.queryBasicData(tenantId, skuIds, availableFlag, waitValid);
    }

    @Override
    public Boolean updateDeletedStatus(List<Long> productSkuPreferentialIds, Long tenantId, Long skuId) {
        LambdaUpdateWrapper<ProductSkuPreferentialCostPrice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProductSkuPreferentialCostPrice::getDeleted, DeletedEnum.DELETED.getCode());
        updateWrapper.in(ProductSkuPreferentialCostPrice::getId, productSkuPreferentialIds);
        updateWrapper.in(ProductSkuPreferentialCostPrice::getTenantId, tenantId);
        updateWrapper.in(ProductSkuPreferentialCostPrice::getSkuId, skuId);
        return update(updateWrapper);
    }

    @Override
    public Set<Long> queryAvailableSkuIdByTenantId(Long tenantId) {
        LambdaQueryWrapper<ProductSkuPreferentialCostPrice> queryWrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        queryWrapper.eq(ProductSkuPreferentialCostPrice::getTenantId, tenantId);
        queryWrapper.le(ProductSkuPreferentialCostPrice::getStartTime, LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
        queryWrapper.ge(ProductSkuPreferentialCostPrice::getEndTime, LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
        queryWrapper.gt(ProductSkuPreferentialCostPrice::getAvailableQuantity, 0);
        queryWrapper.select(ProductSkuPreferentialCostPrice::getSkuId);
        return baseMapper.selectObjs(queryWrapper).stream().map(o -> (Long) o).collect(Collectors.toSet());
    }

    @Override
    public List<ProductPricingMessageDTO> selectFutureEndTime() {
        return getBaseMapper ().selectFutureEndTime();
    }

    @Override
    public List<ProductPricingMessageDTO> selectFutureStartTime() {
        return getBaseMapper ().selectFutureStartTime();
    }

    @Override
    public Integer optAvailableQuantity(Long id, Integer optQuantity) {
        return getBaseMapper ().optAvailableQuantity(id,optQuantity);
    }

    private MPJLambdaWrapper<ProductSkuPreferentialCostPrice> buildSkuPreferentialCostPriceQueryWrapper(ProductSkuPreferentialQueryParam param) {
        MPJLambdaWrapper<ProductSkuPreferentialCostPrice> queryWrapper = builderQueryCondition(param);

        queryWrapper.orderByDesc(ProductSkuPreferentialCostPrice::getId);
        return queryWrapper;
    }

    private MPJLambdaWrapper<ProductSkuPreferentialCostPrice> builderQueryCondition(ProductSkuPreferentialQueryParam param) {
        MPJLambdaWrapper<ProductSkuPreferentialCostPrice> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(ProductSkuPreferentialCostPrice.class);

        queryWrapper.eq(ProductSkuPreferentialCostPrice::getTenantId, param.getTenantId());
        queryWrapper.eq(ProductSkuPreferentialCostPrice::getDeleted, DeletedEnum.NORMAL.getCode());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getSkuIds()), ProductSkuPreferentialCostPrice::getSkuId, param.getSkuIds());

        queryWrapper.in(CollectionUtils.isNotEmpty(param.getIds()), ProductSkuPreferentialCostPrice::getId, param.getIds());
        LocalDateTime now = LocalDateTime.now();
        if (ObjectUtil.isNotNull(param.getAvailableFlag()) && param.getAvailableFlag()) {
            queryWrapper.le(ProductSkuPreferentialCostPrice::getStartTime, LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
            queryWrapper.ge(ProductSkuPreferentialCostPrice::getEndTime, LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
            queryWrapper.gt(ProductSkuPreferentialCostPrice::getAvailableQuantity, 0);
        }
        // 时间不在范围内或无可用数量
        if (ObjectUtil.isNotNull(param.getAvailableFlag()) && !param.getAvailableFlag()) {
            queryWrapper.and(wrapper -> {
                wrapper.and(wrap -> {
                    wrap.le(ProductSkuPreferentialCostPrice::getEndTime, LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
                });
                wrapper.or(wrap -> wrap.eq(ProductSkuPreferentialCostPrice::getAvailableQuantity, 0));
            });
        }
        if (Objects.nonNull(param.getWaitValid()) && param.getWaitValid()) {
            queryWrapper.ge(ProductSkuPreferentialCostPrice::getStartTime, LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
        }
        return queryWrapper;
    }
}
