package com.cosfo.item.web.provider;

import com.cofso.item.client.provider.MarketClassificationProvider;
import com.cofso.item.client.req.MarketClassificationAddReq;
import com.cofso.item.client.req.MarketClassificationQueryReq;
import com.cofso.item.client.req.MarketClassificationUpdateReq;

import com.cofso.item.client.resp.MarketClassificationResp;
import com.cofso.item.client.resp.MarketClassificationTreeResp;
import com.cofso.item.client.resp.MarketItemClassificationResp;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationAddDTO;
import com.cosfo.item.infrastructure.item.dto.MarketClassificationUpdateDTO;
import com.cosfo.item.web.domain.converter.MarketClassificationConvert;
import com.cosfo.item.web.domain.dto.ItemClassificationDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationQueryDTO;
import com.cosfo.item.web.domain.dto.MarketClassificationTreeDTO;
import com.cosfo.item.web.domain.service.MarketClassificationDomainService;
import com.cosfo.item.web.domain.vo.MarketItemClassificationVO;
import com.cosfo.item.web.provider.converter.MarketItemClassificationConvert;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/5/6
 */
@DubboService
@Slf4j
public class MarketClassificationProviderImpl implements MarketClassificationProvider {

    @Autowired
    private MarketClassificationDomainService marketClassificationDomainService;

    /**
     * 增加
     *
     * @param marketClassificationAddReq
     * @return
     */
    @Override
    public DubboResponse<Boolean> add(MarketClassificationAddReq marketClassificationAddReq) {
        MarketClassificationAddDTO marketClassificationAddDTO = MarketClassificationConvert.convertToMarketClassificationAddDTO(marketClassificationAddReq);
        marketClassificationDomainService.add(marketClassificationAddDTO);
        return DubboResponse.getOK(Boolean.TRUE);
    }

    /**
     * 更新
     *
     * @param marketClassificationUpdateReq
     * @return
     */
    @Override
    public DubboResponse<Boolean> update(MarketClassificationUpdateReq marketClassificationUpdateReq) {
        MarketClassificationUpdateDTO marketClassificationUpdateDTO = MarketClassificationConvert.convertToMarketClassificationUpdateDTO(marketClassificationUpdateReq);
        marketClassificationDomainService.update(marketClassificationUpdateDTO);
        return DubboResponse.getOK(Boolean.TRUE);
    }

    /**
     * 删除
     *
     * @param classificationId
     * @param tenantId
     * @return
     */
    @Override
    public DubboResponse<Boolean> delete(Long classificationId, Long tenantId) {
        Boolean flag = marketClassificationDomainService.delete(classificationId, tenantId);
        return DubboResponse.getOK(flag);
    }

    /**
     * 分类树
     *
     * @param tenantId
     * @return
     */
    @Override
    public DubboResponse<List<MarketClassificationTreeResp>> selectClassificationTree(Long tenantId) {
        List<MarketClassificationTreeDTO> marketClassificationTreeDTOS = marketClassificationDomainService.selectClassificationTree(tenantId);
        List<MarketClassificationTreeResp> marketClassificationTreeResps = MarketClassificationConvert.convertToMarketClassificationTreeRespDTO(marketClassificationTreeDTOS);
        return DubboResponse.getOK(marketClassificationTreeResps);
    }

    @Override
    public DubboResponse<List<MarketClassificationResp>> queryByCondition(MarketClassificationQueryReq marketClassificationQueryReq) {
        MarketClassificationQueryDTO marketClassificationQueryDTO = MarketClassificationConvert.convertToMarketClassificationQueryDTO(marketClassificationQueryReq);
        List<MarketClassificationDTO> marketClassificationDTOS = marketClassificationDomainService.queryByCondition(marketClassificationQueryDTO);
        List<MarketClassificationResp> marketClassificationResps = MarketClassificationConvert.convertTOMarketClassificationRespList(marketClassificationDTOS);
        return DubboResponse.getOK(marketClassificationResps);
    }

    @Override
    public DubboResponse<Map<Long, MarketItemClassificationResp>> queryByMarketIds(Long tenantId, List<Long> marketIdList) {
        Map<Long, ItemClassificationDTO> dtoMap = marketClassificationDomainService.getItemClassification(tenantId, marketIdList);
        Map<Long, MarketItemClassificationResp> resultMap = dtoMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        e -> MarketItemClassificationConvert.INSTANCE.convert2MarketItemClassificationResp(e.getValue())));
        return DubboResponse.getOK(resultMap);
    }

    @Override
    public DubboResponse<MarketClassificationResp> selectWholeClassification(Long tenantId, Long marketId) {
        MarketClassificationDTO marketClassificationDTO = marketClassificationDomainService.selectWholeClassification(tenantId, marketId);
        MarketClassificationResp marketClassificationResp = MarketClassificationConvert.convertTOMarketClassificationResp(marketClassificationDTO);
        return DubboResponse.getOK(marketClassificationResp);
    }

    @Override
    public DubboResponse<List<MarketClassificationTreeResp>> queryByChildClassificationIds(List<Long> classificationIds, Long tenantId) {
        List<MarketClassificationTreeDTO> marketClassificationTreeDTOS = marketClassificationDomainService.queryByChildClassificationIds(classificationIds, tenantId);
        List<MarketClassificationTreeResp> marketClassificationTreeResps = MarketClassificationConvert.convertToMarketClassificationTreeRespDTO(marketClassificationTreeDTOS);
        return DubboResponse.getOK(marketClassificationTreeResps);
    }
}
