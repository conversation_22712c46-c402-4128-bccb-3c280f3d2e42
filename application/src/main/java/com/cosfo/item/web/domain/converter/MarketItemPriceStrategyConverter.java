package com.cosfo.item.web.domain.converter;

import com.cosfo.item.infrastructure.price.dto.MarketItemPriceStrategyDTO;
import com.cosfo.item.infrastructure.price.model.*;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyMappingDetailVO;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyVO;

import java.util.List;
import java.util.stream.Collectors;

public class MarketItemPriceStrategyConverter {

    public static MarketItemPriceStrategy marketItemPriceStrategyDTO2Entity(Long tenantId,MarketItemPriceStrategyDTO dto) {
        MarketItemPriceStrategy entity = new MarketItemPriceStrategy ();
        entity.setTenantId(tenantId);
        entity.setItemId(dto.getItemId ());
        entity.setStrategyType(dto.getStrategyType ());
        entity.setStrategyValue(dto.getStrategyValue ());
        entity.setTargetType(dto.getTargetType ());
        entity.setUpdateTime (dto.getUpdateTime ());
        entity.setRemarks(dto.getRemark());
        entity.setPriceStrategyValue (dto.getPriceStrategyValue ());
        return entity;
    }

    public static MarketItemPriceStrategyVO marketItemPriceStrategy2VO(MarketItemPriceStrategy entity, List<Long> ids) {
        MarketItemPriceStrategyVO vo = new MarketItemPriceStrategyVO ();
        vo.setId(entity.getId ());
        vo.setTenantId(entity.getTenantId ());
        vo.setItemId(entity.getItemId ());
        vo.setStrategyType(entity.getStrategyType ());
        vo.setStrategyValue(entity.getStrategyValue ());
        vo.setTargetType(entity.getTargetType ());
        vo.setTargetIds(ids);
        vo.setUpdateTime (entity.getUpdateTime ());
        vo.setRemark(entity.getRemarks());
        vo.setPriceStrategyValue (entity.getPriceStrategyValue ());
        return vo;
    }

    public static List<MarketItemPriceStrategyMapping> targetIds2Entity(Long tenantId,Long itemPriceStrategyId,List<Long> targetIds) {
        return targetIds.stream ().map (e->{
            MarketItemPriceStrategyMapping entity = new MarketItemPriceStrategyMapping ();
            entity.setTenantId(tenantId);
            entity.setItemPriceStrategyId(itemPriceStrategyId);
            entity.setTargetId(e);
            return entity;
        }).collect(Collectors.toList());
    }

    public static MarketItemPriceStrategyMappingDetailVO entity2MarketItemPriceStrategyMappingDetailVO(MarketItemPriceStrategyMapping mapping, MarketItemPriceStrategy e) {
        MarketItemPriceStrategyMappingDetailVO vo = new MarketItemPriceStrategyMappingDetailVO ();
        vo.setTenantId(e.getTenantId ());
        vo.setItemId(e.getItemId ());
        vo.setStrategyType(e.getStrategyType ());
        vo.setStrategyValue(e.getStrategyValue ());
        vo.setPriceStrategyValue (e.getPriceStrategyValue ());
        vo.setTargetType(e.getTargetType ());
        vo.setTargetId(mapping.getTargetId ());
        vo.setCreateTime(e.getCreateTime ());
        vo.setUpdateTime(e.getUpdateTime ());
        vo.setMarketItemPriceStrategyId (e.getId ());
        vo.setMarketItemPriceStrategyMappingId (mapping.getId ());
        return vo;
    }
}
