package com.cosfo.item.infrastructure.price.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.common.dto.PriceRangeDTO;
import com.cosfo.item.infrastructure.price.model.MarketItemPrice;

import java.util.Collection;
import java.util.List;

/**
 * 商品价格表(MarketItemPrice)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-28 15:45:07
 */
public interface MarketItemPriceDao extends IService<MarketItemPrice>{

    void saveOrUpdateByItemIdAndTargetIdAndTargetType(List<MarketItemPrice> price);

    List<MarketItemPrice> listByItemIds(Long tenantId, List<Long> itemIds);

    List<MarketItemPrice> ListByItemIdAndTargetIdsAndTargetType(Long tenantId, Long marketItemId, Collection<Long> targetIds, Integer targetType);

    List<PriceRangeDTO> queryPriceRangeGroupByItemId(Long tenantId, List<Long> itemIds);

    List<MarketItemPrice> ListByItemIdAndTarget(Long tenantId, Collection<Long> marketItemIds, Long targetId, Integer targetType);

}
