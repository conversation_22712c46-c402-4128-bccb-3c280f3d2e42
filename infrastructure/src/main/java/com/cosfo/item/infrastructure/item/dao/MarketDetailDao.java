package com.cosfo.item.infrastructure.item.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.item.dto.MarketDetailParam;
import com.cosfo.item.infrastructure.item.model.MarketDetail;

import java.util.List;

/**
 * <p>
 * 商品扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface MarketDetailDao extends IService<MarketDetail> {

    List<MarketDetail> listByParam(MarketDetailParam param);
    MarketDetail getByMarketId(Long marketId);
}
