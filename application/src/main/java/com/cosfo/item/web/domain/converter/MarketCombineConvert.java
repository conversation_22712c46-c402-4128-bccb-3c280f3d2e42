package com.cosfo.item.web.domain.converter;

import com.cosfo.item.common.dto.CombineListPriceDTO;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.model.Market;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.web.domain.dto.CombineMarketQueryDTO;
import com.cosfo.item.web.domain.vo.CombineMarketDetailVO;
import com.cosfo.item.web.domain.vo.CombineMarketListVO;
import com.cosfo.item.web.domain.vo.MarketCombineVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;


/**
 * @author: monna.chen
 * @Date: 2023/4/28 18:47
 * @Description:
 */
@Mapper
public interface MarketCombineConvert {
    MarketCombineConvert INSTANCE = Mappers.getMapper(MarketCombineConvert.class);


    @Mapping(source = "id", target = "marketItemId")
    MarketCombineVO convert2Combines(MarketItem item);

    MarketCombineQueryParam convert2QueryParam(CombineMarketQueryDTO dto);

    @Mapping(source = "id", target = "combineMarketId")
    @Mapping(source = "title", target = "combineMarketTitle")
    CombineMarketListVO convert2ListVO(Market market);

    @Mapping(source = "id", target = "combineMarketId")
    @Mapping(source = "title", target = "combineMarketTitle")
    @Mapping(source = "subTitle", target = "combineMarketSubTitle")
    CombineMarketDetailVO convert2CombineMarket(Market market);

    CombineListPriceDTO convert2CombinePrice(MarketCombineVO combineVO);
}
