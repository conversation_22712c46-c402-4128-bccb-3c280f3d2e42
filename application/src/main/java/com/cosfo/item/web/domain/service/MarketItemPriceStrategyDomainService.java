package com.cosfo.item.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.enums.PriceStrategyTypeEnum;
import com.cofso.item.client.enums.PriceTargetTypeEnum;
import com.cosfo.item.common.dto.LadderPriceDTO;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.web.domain.dto.PriceStrategyFloatingRangeDTO;
import com.cosfo.item.web.domain.vo.CostPriceVO;
import com.cosfo.item.web.domain.vo.PriceStrategyRangeVO;
import com.cosfo.item.web.facade.MerchantStoreFacade;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.infrastructure.item.dto.MarketItemDetailParam;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyDao;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyMappingDao;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceStrategyDTO;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategyMapping;
import com.cosfo.item.web.domain.converter.MarketItemPriceStrategyConverter;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyMappingDetailVO;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * item价格策略
 */
@Component
@Slf4j
public class MarketItemPriceStrategyDomainService {
    @Autowired
    private MarketItemPriceStrategyDao strategyDao;
    @Autowired
    private MarketItemPriceStrategyMappingDao strategyMappingDao;
    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private MerchantStoreFacade merchantStoreFacade;
    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;

    /**
     * 批量保存价格策略
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateMarketItemPriceStrategByItemId(Long tenantId, List<MarketItemPriceStrategyDTO> dtos){
        //1、删除
        List<Long> itemIds = dtos.stream ().map (MarketItemPriceStrategyDTO::getItemId).collect (Collectors.toList ());
        List<MarketItemPriceStrategy> strategys = strategyDao.listByItemIds(tenantId,itemIds);
        if(CollectionUtils.isNotEmpty(strategys)) {
            List<MarketItemPriceStrategyMapping> strategyMappings = strategyMappingDao.listByStrategyIds (strategys.stream ().map (MarketItemPriceStrategy::getId).collect (Collectors.toList ()));
            strategyDao.removeByIds (strategys.stream ().map (MarketItemPriceStrategy::getId).collect (Collectors.toList ()));
            if(CollectionUtils.isNotEmpty (strategyMappings)) {
                strategyMappingDao.removeByIds (strategyMappings.stream ().map (MarketItemPriceStrategyMapping::getId).collect (Collectors.toList ()));
            }
        }
        //2、新增
        dtos.forEach (strategyDTO->{
            MarketItemPriceStrategy strategy = MarketItemPriceStrategyConverter.marketItemPriceStrategyDTO2Entity (tenantId, strategyDTO);
            strategyDao.save (strategy);
            List<MarketItemPriceStrategyMapping> strategyMappings = MarketItemPriceStrategyConverter.targetIds2Entity (tenantId, strategy.getId (), strategyDTO.getTargetIds ());
            if(CollectionUtils.isNotEmpty (strategyMappings)){
                strategyMappingDao.saveBatch (strategyMappings);
            }
        });
    }

    /**
     * 批量保存价格策略
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateMarketItemPriceStrategByItemId(Long tenantId, List<MarketItemPriceStrategyDTO> dtos,Integer goodsType,String sku){
        Optional<MarketItemPriceStrategyDTO> any = dtos.stream ().filter (e -> MarketItemPriceStrategyEnum.StrategyTypeEnum.WITH_COSTPRICE_STRATEGY_LIST.contains (e.getStrategyType ())).findAny ();
        //不是鲜沐的商品 & 有货品 & 策略是有成本价基础
        if(!xmTenantId.equals (tenantId) && MarketItemEnum.GoodsType.SELF_SUPPORT.getCode ().equals (goodsType) && ObjectUtil.isNotEmpty (sku) && any.isPresent ()){
            List<CostPriceVO> costPriceVOS = costPriceDomianService.listValidCostPriceBySkuCode (sku, tenantId);
            if(CollectionUtils.isEmpty (costPriceVOS)){
                throw new BizException ("没有成本价，请修改定价方式为【自定义固定价】");
            }
        }
        saveOrUpdateMarketItemPriceStrategByItemId(tenantId, dtos);
    }

    /**
     * 查询商品价格策略
     */
    public List<MarketItemPriceStrategyVO> listMarketItemPriceStrategyByItemIds(Long tenantId, List<Long> itemIds){
        List<MarketItemPriceStrategy> strategys = strategyDao.listByItemIds(tenantId,itemIds);
        if(CollectionUtils.isNotEmpty (strategys)){
            List<MarketItemPriceStrategyMapping> strategyMappings = strategyMappingDao.listByStrategyIds (strategys.stream ().map (MarketItemPriceStrategy::getId).collect (Collectors.toList ()));
            Map<Long, List<Long>> strategyMappingMap;
            if(CollectionUtils.isNotEmpty (strategyMappings)) {
                strategyMappingMap = strategyMappings.stream ().collect (Collectors.groupingBy (MarketItemPriceStrategyMapping::getItemPriceStrategyId, Collectors.mapping (MarketItemPriceStrategyMapping::getTargetId, Collectors.toList ())));
            } else {
                log.error ("当前价格策略没有详情！！strategys={}", JSON.toJSONString (strategys));
                strategyMappingMap = Collections.emptyMap ();
            }
            return strategys.stream ().map (e-> MarketItemPriceStrategyConverter.marketItemPriceStrategy2VO(e,strategyMappingMap.get (e.getId ()))).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /***
     * 根据商品
     * @param tenantId
     * @param marketItemId
     * @param targetType
     * @param targetId
     * @return
     */
    public MarketItemPriceStrategyMappingDetailVO listMarketItemPriceStrategyByItemIdAndTargetId(Long tenantId, Long marketItemId, Integer targetType,Long targetId) {
        List<MarketItemPriceStrategy> strategys = strategyDao.getByItemIdAndTargetType(tenantId,marketItemId,targetType);
        if(CollectionUtil.isNotEmpty (strategys)){
            List<MarketItemPriceStrategyMapping> strategyMappings = strategyMappingDao.listByStrategyIds (strategys.stream ().map (MarketItemPriceStrategy::getId).collect (Collectors.toList ()));
            if(CollectionUtil.isNotEmpty (strategyMappings)) {
                MarketItemPriceStrategyMapping mapping = strategyMappings.stream ().filter (e -> Objects.equals (targetId, e.getTargetId ())).findAny ().orElse (null);
                if(ObjectUtil.isNotNull (mapping)){
                    Map<Long, MarketItemPriceStrategy> strategyMap = strategys.stream().collect(Collectors.toMap (MarketItemPriceStrategy::getId, Function.identity()));
                    return MarketItemPriceStrategyConverter.entity2MarketItemPriceStrategyMappingDetailVO(mapping,strategyMap.get (mapping.getItemPriceStrategyId ()));
                }
            }
        }
        return null;
    }
    public Map<Long, List<LadderPriceDTO>> getLadderPriceMap(Long tenantId, List<Long> itemIds, Long targetId) {
        List<MarketItemPriceStrategy> all = new ArrayList<> ();
        List<MarketItemPriceStrategy> storeStrategy = strategyDao.listByItemIdsAndStrategyTypeAndTarget (tenantId, itemIds, PriceStrategyTypeEnum.ASSIGN.getCode (), targetId, MarketItemPriceStrategyEnum.TargetTypeEnum.STORE.getCode ());
        if(CollectionUtil.isNotEmpty (storeStrategy)){
            all.addAll (storeStrategy);
        }
        List<MarketItemPriceStrategy> storeGroupStrategy = strategyDao.listByItemIdsAndStrategyTypeAndTarget (tenantId, itemIds, PriceStrategyTypeEnum.ASSIGN.getCode (),  merchantStoreFacade.getGroupIdByStoreId(tenantId, targetId), PriceTargetTypeEnum.STORE_GROUP.getCode ());
        if(CollectionUtil.isNotEmpty (storeGroupStrategy)){
            all.addAll (storeGroupStrategy);
        }
        List<MarketItemPriceStrategy> tenantStrategy = strategyDao.listByItemIdsAndStrategyTypeAndTarget (tenantId, itemIds, PriceStrategyTypeEnum.ASSIGN.getCode (), tenantId, PriceTargetTypeEnum.TENANT.getCode ());
        if(CollectionUtil.isNotEmpty (tenantStrategy)){
            all.addAll (tenantStrategy);
        }

        if(CollectionUtils.isNotEmpty (all)) {
            return all.stream().filter (e-> StringUtils.isNotBlank (e.getPriceStrategyValue()))
                    .collect(Collectors.toMap(
                            MarketItemPriceStrategy::getItemId,
                            e -> JSON.parseArray(e.getPriceStrategyValue(), LadderPriceDTO.class),
                            (existingList, newList) -> {
                                List<LadderPriceDTO> mergedList = new ArrayList<>(existingList);
                                for (LadderPriceDTO newItem : newList) {
                                    boolean found = false;
                                    for (LadderPriceDTO existingItem : mergedList) {
                                        if (existingItem.getUnit() == newItem.getUnit()) {
                                            if (existingItem.getPrice().compareTo(newItem.getPrice()) > 0) {
                                                mergedList.remove(existingItem);
                                                mergedList.add(newItem);
                                            }
                                            found = true;
                                            break;
                                        }
                                    }
                                    if (!found) {
                                        mergedList.add(newItem);
                                    }
                                }
                                mergedList.sort(Comparator.comparingInt(LadderPriceDTO::getUnit));
                                return mergedList;
                            }
                    ));
        }
        return Collections.emptyMap ();
    }

    public void updateMarketItemPriceStrategByItemIdAndTargetId(Long marketItemPriceStrategyId,MarketItemPriceStrategyDTO priceStrategy) {
        MarketItemPriceStrategy strategy = strategyDao.getById (marketItemPriceStrategyId);
        strategy.setStrategyValue (priceStrategy.getStrategyValue ());
        strategy.setStrategyType (priceStrategy.getStrategyType ());
        strategy.setUpdateTime (priceStrategy.getUpdateTime());
        strategy.setPriceStrategyValue (priceStrategy.getPriceStrategyValue ());
        strategyDao.updateById (strategy);
    }

    public void saveMarketItemPriceStrategByItemIdAndTargetId(Long tenantId,MarketItemPriceStrategyDTO strategyDTO) {
        MarketItemPriceStrategy strategy = MarketItemPriceStrategyConverter.marketItemPriceStrategyDTO2Entity (tenantId, strategyDTO);
        strategyDao.save (strategy);
        List<MarketItemPriceStrategyMapping> strategyMappings = MarketItemPriceStrategyConverter.targetIds2Entity (tenantId, strategy.getId (), strategyDTO.getTargetIds ());
        if(CollectionUtil.isNotEmpty (strategyMappings)){
            strategyMappingDao.saveBatch (strategyMappings);
        }
    }
    public void batchUpdatePriceStrategy(PriceStrategyFloatingRangeDTO dto) {
        List<MarketItemPriceStrategy> updateList = new ArrayList<> ();
        List<MarketItemPriceStrategy> strategys = strategyDao.listByItemIds(dto.getTenantId (),dto.getMarketItemIds ());
        if(CollectionUtil.isNotEmpty (strategys)){
            Map<Integer, List<MarketItemPriceStrategy>> strategyMap = strategys.stream().collect(Collectors.groupingBy(MarketItemPriceStrategy::getStrategyType, Collectors.toList ()));
            if(ObjectUtil.isNotNull (dto.getCostPriceAddPercentageRange ()) && strategyMap.containsKey (MarketItemPriceStrategyEnum.StrategyTypeEnum.COST_PRICE_ADD_PERCENTAGE.getCode ())){
                List<MarketItemPriceStrategy> costPriceAddPercentage = strategyMap.get (MarketItemPriceStrategyEnum.StrategyTypeEnum.COST_PRICE_ADD_PERCENTAGE.getCode ());
                costPriceAddPercentage.forEach (e-> {
                    BigDecimal result = e.getStrategyValue ().add (dto.getCostPriceAddPercentageRange ().multiply (new BigDecimal (dto.getCostPriceAddPercentageOptype ())));
                    if(result.compareTo (BigDecimal.ZERO) <= 0){
                        result = new BigDecimal (0);
                    }
                    e.setStrategyValue (result);
                });
                updateList.addAll (costPriceAddPercentage);
            }
            if(ObjectUtil.isNotNull (dto.getCostPriceAddFixedRange ()) && strategyMap.containsKey (MarketItemPriceStrategyEnum.StrategyTypeEnum.COST_PRICE_ADD_FIXED.getCode ())){
                List<MarketItemPriceStrategy> costPriceAddFixed = strategyMap.get (MarketItemPriceStrategyEnum.StrategyTypeEnum.COST_PRICE_ADD_FIXED.getCode ());
                costPriceAddFixed.forEach (e->{
                    BigDecimal result = e.getStrategyValue ().add (dto.getCostPriceAddFixedRange ().multiply (new BigDecimal (dto.getCostPriceAddFixedOptype ())));
                    if(result.compareTo (BigDecimal.ZERO) <= 0){
                        result = new BigDecimal (0);
                    }
                    e.setStrategyValue (result);
                });
                updateList.addAll (costPriceAddFixed);
            }
            if(ObjectUtil.isNotNull (dto.getAssignRange ()) && strategyMap.containsKey (MarketItemPriceStrategyEnum.StrategyTypeEnum.ASSIGN.getCode ())){
                List<MarketItemPriceStrategy> assign = strategyMap.get (MarketItemPriceStrategyEnum.StrategyTypeEnum.ASSIGN.getCode ());
                assign.forEach (e-> {
                    BigDecimal optBigDecimal = dto.getAssignRange ().multiply (new BigDecimal (dto.getAssignOptype ()));
                    BigDecimal result = e.getStrategyValue ().add (optBigDecimal);
                    if(result.compareTo (BigDecimal.ZERO) <= 0){
                        result = new BigDecimal ("0.01");
                    }
                    e.setStrategyValue (result);
                    String priceStrategyValue = e.getPriceStrategyValue ();
                    if(StringUtils.isNotBlank (priceStrategyValue)){
                        List<LadderPriceDTO> ladderPriceDTOS = JSON.parseArray (priceStrategyValue, LadderPriceDTO.class);
                        ladderPriceDTOS.forEach (l-> {
                            BigDecimal price = l.getPrice ().add (optBigDecimal);
                            if(price.compareTo (BigDecimal.ZERO) <= 0){
                                price = new BigDecimal ("0.01");
                            }
                            l.setPrice (price);
                        });
                        e.setPriceStrategyValue (JSON.toJSONString (ladderPriceDTOS));
                    }
                });
                updateList.addAll (assign);
            }
        }
        if(CollectionUtil.isNotEmpty (updateList)){
            strategyDao.updateBatchById (updateList);
        }
    }
}
