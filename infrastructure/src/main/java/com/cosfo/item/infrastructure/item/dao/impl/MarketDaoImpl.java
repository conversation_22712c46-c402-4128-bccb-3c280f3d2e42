package com.cosfo.item.infrastructure.item.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;
import com.cosfo.item.infrastructure.item.dao.MarketDao;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketPageQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketQueryParam;
import com.cosfo.item.infrastructure.item.mapper.MarketMapper;
import com.cosfo.item.infrastructure.item.model.Market;
import com.cosfo.item.infrastructure.item.model.MarketCombineItemMapping;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;
import com.cosfo.item.infrastructure.price.model.MarketItemUnfairPriceStrategy;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.cosfo.item.common.enums.MarketItemEnum.ItemTypeEnum.NON_COMBINE_CODES;

/**
 * 销售商品(Market)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-27 14:06:12
 */
@Service
public class MarketDaoImpl extends ServiceImpl<MarketMapper, Market> implements MarketDao {

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;

    @Override
    public List<Market> listByParam(MarketQueryParam param) {
        LambdaQueryWrapper<Market> queryWrapper = buildQueryWrapper(param);
        return list(queryWrapper);
    }

    @Override
    public Page<Market> pageCombineByParam(MarketCombineQueryParam param) {
        Page<Market> page = new Page<>(param.getPageNum(), param.getPageSize());
        return page(page, buildMPJCombineQueryWrapper(param));
    }

    @Override
    public Page<Market> pageMarketByParam(MarketPageQueryParam param) {
        Page<Market> page = new Page<>(param.getPageNum(), param.getPageSize());
        return page(page, buildMPJMarketQueryWrapper(param));
    }

    private LambdaQueryWrapper<Market> buildQueryWrapper(MarketQueryParam param) {
        LambdaQueryWrapper<Market> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(Objects.nonNull(param.getTitle()), Market::getTitle, param.getTitle());
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), Market::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getId()), Market::getId, param.getId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getCategoryIds()), Market::getCategoryId, param.getCategoryIds());

        return queryWrapper;
    }

    public MPJLambdaWrapper<Market> buildMPJCombineQueryWrapper(MarketCombineQueryParam param) {
        MPJLambdaWrapper<Market> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Market.class);
        queryWrapper.leftJoin(MarketItem.class, MarketItem::getMarketId, Market::getId);
        queryWrapper.leftJoin(MarketCombineItemMapping.class, MarketCombineItemMapping::getItemId, MarketItem::getId);

        queryWrapper.eq(MarketItem::getItemType, MarketItemEnum.ItemTypeEnum.COMBINE_ITEM.getCode());
        queryWrapper.isNotNull(MarketCombineItemMapping::getCombineItemId);

        queryWrapper.eq(Objects.nonNull(param.getTenantId()), Market::getTenantId, param.getTenantId());
        queryWrapper.like(Objects.nonNull(param.getCombineMarketTitle()), Market::getTitle, param.getCombineMarketTitle());
        queryWrapper.eq(Objects.nonNull(param.getCombineMarketId()), Market::getId, param.getCombineMarketId());
        queryWrapper.eq(Objects.nonNull(param.getSubItemId()), MarketCombineItemMapping::getCombineItemId, param.getSubItemId());
        queryWrapper.in(Objects.nonNull(param.getSubItemIds()), MarketCombineItemMapping::getCombineItemId, param.getSubItemIds());

        queryWrapper.orderByDesc(Market::getId);
        queryWrapper.groupBy(Market::getId);
        return queryWrapper;
    }

    public MPJLambdaWrapper<Market> buildMPJMarketQueryWrapper(MarketPageQueryParam param) {
        MPJLambdaWrapper<Market> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(Market.class);
        queryWrapper.leftJoin(MarketItem.class, MarketItem::getMarketId, Market::getId);
        if (Objects.nonNull(param.getClassificationId())) {
            queryWrapper.leftJoin(MarketItemClassification.class, MarketItemClassification::getMarketId, Market::getId);
        }
        // 如果不是查询所有
        //如果是xm的查询， 增加admin的查询条件
        if(xmTenantId.equals (param.getTenantId ())) {
            if (param.isNotAllFlag ()) {
                queryWrapper.leftJoin (MarketItemDetail.class, MarketItemDetail::getMarketItemId, MarketItem::getId);
                if (Objects.nonNull (param.getAdminId ())) {
                    // 如果adminid不是空， 则查询大客户代仓品和xm自营品
                    queryWrapper.and (q -> q.eq (MarketItemDetail::getCustomerId, param.getAdminId ()).or ().isNull (MarketItemDetail::getCustomerId));
                } else {
                    // 如果adminid是空， 则查询xm自营品
                    queryWrapper.and (q -> q.isNull (MarketItemDetail::getCustomerId));
                }
            }
        }

        queryWrapper.and(q -> q.in(MarketItem::getItemType, NON_COMBINE_CODES)
            .or().isNull(MarketItem::getItemType));
        queryWrapper.eq(Market::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag());
        queryWrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag());

        queryWrapper.eq(Objects.nonNull(param.getTenantId()), Market::getTenantId, param.getTenantId());
        queryWrapper.like(Objects.nonNull(param.getTitle()), Market::getTitle, param.getTitle());
        queryWrapper.in(!CollectionUtils.isEmpty(param.getMarketIds()), Market::getId, param.getMarketIds());
        queryWrapper.eq(Objects.nonNull(param.getId()), Market::getId, param.getId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getClassificationIds()), MarketItemClassification::getClassificationId, param.getClassificationIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getCategoryIds()), Market::getCategoryId, param.getCategoryIds());
        queryWrapper.eq(Objects.nonNull(param.getItemId()), MarketItem::getId, param.getItemId());
        queryWrapper.eq(Objects.nonNull(param.getItemCode()), MarketItem::getItemCode, param.getItemCode());
        queryWrapper.eq(Objects.nonNull(param.getOnSale()), MarketItem::getOnSale, param.getOnSale());
        queryWrapper.eq(Objects.nonNull(param.getGoodsType()), MarketItem::getGoodsType, param.getGoodsType());
        queryWrapper.eq(Objects.nonNull(param.getSkuId()), MarketItem::getSkuId, param.getSkuId());
        queryWrapper.like(Objects.nonNull(param.getBrandName()), MarketItem::getBrandName, param.getBrandName());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getGoodsTypeList()), MarketItem::getGoodsType, param.getGoodsTypeList());
        queryWrapper.eq(Objects.nonNull(param.getSupplierId()), MarketItem::getSupplierId, param.getSupplierId());
        queryWrapper.notIn(!CollectionUtils.isEmpty(param.getNotInMarketItemIds()), MarketItem::getId, param.getNotInMarketItemIds());
        queryWrapper.in(!CollectionUtils.isEmpty(param.getInMarketItemIds()), MarketItem::getId, param.getInMarketItemIds());
        queryWrapper.eq(Objects.nonNull(param.getStoreInventoryControlFlag ()), MarketItem::getStoreInventoryControlFlag, param.getStoreInventoryControlFlag());
        queryWrapper.eq(Objects.nonNull(param.getPresaleSwitch()), MarketItem::getPresaleSwitch, param.getPresaleSwitch());
        /**
         * 2024.02.01 客户反馈分类无法删除且查询不到商品。
         * 问题原因：客户操作创建商品后，未新建规格，根据下面 item.id is not null的条件查询不到数据
         * 目前生产这类(只有market没有market_item)数据有198条，涉及租户27个
         * 评估后isnotnull条件可以去掉，这个限制条件在2023/8/4查询防倒挂条件时加上，且去掉这个条件后不影响防倒挂的查询逻辑。
         */
        // queryWrapper.isNotNull(MarketItem::getId);
        queryWrapper.groupBy(Market::getId);

        queryWrapper.orderByDesc(Market::getId);
        return queryWrapper;
    }
    @Override
    public Set<String> queryBrandNameByTenantId(Long tenantId) {
        QueryWrapper<Market> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT brand_name")
                .eq("tenant_id", tenantId)
                .isNotNull("brand_name")
                .ne("brand_name", "");

        // 使用 Set 直接返回去重的品牌名称
        return list(queryWrapper)
                .stream()
                .map(Market::getBrandName)
                .collect(Collectors.toSet());
    }
}
