package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
public interface MarketAreaItemEnum {
    @Getter
    @AllArgsConstructor
    public enum WarehouseTypeEnum {
        PROPRIETARY(0,"品牌自营仓"),
        THREE_PARTIES(1,"三方优选仓");

        /**
         * 配送仓类型编码
         */
        private Integer code;
        /**
         * 配送仓类型描述
         */
        private String desc;
    }


    @Getter
    @AllArgsConstructor
    public enum DeliveryTypeEnum {

        /**
         * 品牌方配送
         */
        BRAND_DELIVERY(0, "品牌方配送"),

        /**
         * 三方配送
         */
        THIRD_DELIVERY(1,"三方配送");

        /**
         * 状态编码
         */
        private Integer code;
        /**
         * 状态描述
         */
        private String desc;

        public static DeliveryTypeEnum getByCode(Integer code){
            for(DeliveryTypeEnum deliveryTypeEnum: DeliveryTypeEnum.values()){
                if (deliveryTypeEnum.getCode().equals(code)) {
                    return deliveryTypeEnum;
                }
            }
            return null;
        }
    }
}