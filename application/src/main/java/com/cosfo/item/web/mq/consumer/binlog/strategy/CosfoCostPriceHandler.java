package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.service.MarketItemOnsalePriceDealService;
import com.cosfo.item.web.domain.vo.MarketItemVO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.ProductAgentSkuMappingVO;
import com.cosfo.item.web.facade.MerchantStoreFacade;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
public class CosfoCostPriceHandler implements DbTableDmlStrategy {

    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;

    @Autowired
    private ProductFacade productFacade;

    @Autowired
    private ItemDomainService itemDomainService;

    @Autowired
    private MerchantStoreFacade merchantStoreFacade;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.COST_PRICE;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        Map<Long, Set<CostPriceDbRecord>> tenantAndItsSkus = Maps.newHashMap();
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                appendTenantAndSkuFromDataMap(tenantAndItsSkus, map);
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey(), oldMap = pair.getValue();
                if (oldMap.containsKey("price") || oldMap.containsKey("valid_time") || oldMap.containsKey("invalid_time")) {
                    appendTenantAndSkuFromDataMap(tenantAndItsSkus, dataMap);
                } else {
                    log.error("这条消息没有变更cost_price的核心字段：{}", JSON.toJSONString(oldMap));
                }
            }
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.DELETE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue();
                appendTenantAndSkuFromDataMap(tenantAndItsSkus, oldMap);
            }
        }
        handleItemPrice(tenantAndItsSkus);
    }

    private static void appendTenantAndSkuFromDataMap(Map<Long, Set<CostPriceDbRecord>> tenantAndItsSkus, Map<String, String> dataMap) {
        if (CollectionUtils.isEmpty(dataMap) || !dataMap.containsKey("tenant_id")) {
            log.error("非法的数据变更类型：{}", JSON.toJSONString(dataMap));
            return;
        }
        Long tenantId = Long.valueOf(dataMap.get("tenant_id"));
        if (!tenantAndItsSkus.containsKey(tenantId)) {
            tenantAndItsSkus.put(tenantId, new HashSet<>());
        }
        tenantAndItsSkus.get(tenantId).add(JSON.parseObject(JSON.toJSONString(dataMap), CostPriceDbRecord.class));
    }

    private void handleItemPrice(Map<Long, Set<CostPriceDbRecord>> tenantIdSkusMap) {
        if(CollectionUtil.isEmpty (tenantIdSkusMap)){
            return;
        }
        tenantIdSkusMap.entrySet().forEach(tenantAndItsSkus -> {
            long tenantId = tenantAndItsSkus.getKey();

            Set<String> skuCodes = tenantAndItsSkus.getValue().stream().map(CostPriceDbRecord::getSkuCode).collect(Collectors.toSet());
            List<ProductAgentSkuMappingVO> productAgentSkuMappingVOSQuotation = productFacade.listProductMappingByAgentSkuCodes (skuCodes, xmTenantId,xmTenantId);

            List<ProductAgentSkuMappingVO> productAgentSkuMappingVOSSelfSupport = productFacade.listProductMappingByAgentSkuCodes (skuCodes, xmTenantId,tenantId);

            List<ProductAgentSkuMappingVO> productAgentSkuMappingVOS = new ArrayList<> ();
            productAgentSkuMappingVOS.addAll (productAgentSkuMappingVOSQuotation);
            productAgentSkuMappingVOS.addAll (productAgentSkuMappingVOSSelfSupport);
            if (CollectionUtil.isEmpty(productAgentSkuMappingVOS)) {
                log.info("tenantId:{}, skus={} 没有相关的货品,不发送消息，直接返回", tenantId, JSON.toJSONString(tenantAndItsSkus.getValue()));
                return;
            }
            Set<Long> skuIds = productAgentSkuMappingVOS.stream ().map (ProductAgentSkuMappingVO::getSkuId).collect (Collectors.toSet ());
            List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemBySkuIdsAndTypes (tenantId, skuIds, CollectionUtil.newArrayList (MarketItemEnum.GoodsType.QUOTATION.getCode(),MarketItemEnum.GoodsType.SELF_SUPPORT.getCode()));
            if (CollectionUtil.isEmpty(marketItemVOS)) {
                log.info("tenantId:{}, skus={} 没有相关的货品,不发送消息，直接返回", tenantId, JSON.toJSONString(tenantAndItsSkus.getValue()));
                return;
            }
            Map<Long, List<ProductAgentSkuMappingVO>> mappingMap = productAgentSkuMappingVOS.stream().collect(Collectors.groupingBy(ProductAgentSkuMappingVO::getAgentSkuId));

            log.info("skus={} 有相关的货品,个数={}", JSON.toJSONString(skuIds), marketItemVOS.size());
            List<MerchantStoreAddressVO> merchantStoreAddressVOList = merchantStoreFacade.batchQueryStoreAddressFromCache(tenantId);
            if (CollectionUtils.isEmpty(merchantStoreAddressVOList)) {
                log.error("租户没有店铺地址：{}", tenantId);
                return;
            }
            tenantAndItsSkus.getValue().forEach(costPriceDbRecord -> {
                List<MerchantStoreAddressVO> filteredStores = merchantStoreAddressVOList.stream().filter(storeAddress -> {
                    // 同时匹配City和area；
                    return storeAddress.getCity().equalsIgnoreCase(costPriceDbRecord.getCity())
                        && storeAddress.getArea().equalsIgnoreCase(costPriceDbRecord.getArea());
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filteredStores)) {
                    log.warn("这条记录变更没有影响到任何店铺：{}", costPriceDbRecord);
                    return;
                }
                List<ProductAgentSkuMappingVO> list = mappingMap.get (costPriceDbRecord.getSkuId ());
                List<Long> skuids = list.stream ().map (ProductAgentSkuMappingVO::getSkuId).collect (Collectors.toList ());
                marketItemVOS.stream().filter (item-> ObjectUtil.isNotNull (item.getSkuId ()) && skuids.contains (item.getSkuId ())).forEach (marketItemVO->{
                    Set<Long> storeIds = filteredStores.stream().map(MerchantStoreAddressVO::getStoreId).collect(Collectors.toSet());
                    log.info("cost_price变更影响到的店铺ID List：{}, 记录：{}", JSON.toJSONString(storeIds), costPriceDbRecord);
                    ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
                    dto.setStoreIds (storeIds);
                    marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
                });
            });
        });
    }

    /**
     * 一锤子买卖，别的地方用不到
     */
    @Data
    static class CostPriceDbRecord {

        private Long tenantId;

        @JSONField(name = "sku_code")
        private String skuCode;

        @JSONField(name = "sku_id")
        private Long skuId;

        private String province;

        private String city;

        private String area;
    }
}
