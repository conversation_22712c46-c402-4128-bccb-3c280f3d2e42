package com.cosfo.item.infrastructure.item.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.common.constants.MarketConstants;
import com.cosfo.item.common.constants.NumberConstants;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemOnSaleEnum;
import com.cosfo.item.common.enums.MarketItemOnsaleStrategyMappingEnum;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.*;
import com.cosfo.item.infrastructure.item.mapper.MarketItemMapper;
import com.cosfo.item.infrastructure.item.model.*;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.cosfo.item.common.enums.MarketItemEnum.ItemTypeEnum.NON_COMBINE_CODES;

/**
 * <p>
 * 商品表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Service
@Slf4j
public class MarketItemDaoImpl extends MPJBaseServiceImpl<MarketItemMapper, MarketItem> implements MarketItemDao {
    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;

    @Override
    public List<MarketItem> listBySkuIdsAndTypes(Long tenantId, Set<Long> skuIds, List<Integer> goodsTypes) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag())
            .in(MarketItem::getGoodsType, goodsTypes)
            .in(MarketItem::getSkuId, skuIds);
        if (null != tenantId && tenantId.longValue() > 0L) {
            wrapper.eq(MarketItem::getTenantId, tenantId);
        }
        return list(wrapper);
    }


    @Override
    public List<MarketItem> listBySkuIdsAndTenantId(Long tenantId, Collection<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.in(MarketItem::getSkuId, skuIds);
        wrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag ());
        if (null != tenantId && tenantId.longValue() > 0L) {
            wrapper.eq(MarketItem::getTenantId, tenantId);
        }
        return list(wrapper);
    }

    @Override
    public List<MarketItem> listBySkuIdAndTenantIdAndGoodsTypes(Long skuId, Long tenantId, List<Integer> goodsTypes) {
        if (Objects.isNull(skuId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag())
            .in(MarketItem::getGoodsType, goodsTypes)
            .eq(MarketItem::getTenantId, tenantId)
            .eq(MarketItem::getSkuId, skuId);
        return list(wrapper);
    }

    @Override
    public List<MarketItem> listByTenantIdAndType(Long tenantId, Integer goodsType) {
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItem::getTenantId, tenantId)
            .eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag())
            .eq(MarketItem::getGoodsType, goodsType);
        return list(wrapper);
    }

    @Override
    public List<MarketItem> listByTenantIdAndTypes(Long tenantId, List<Integer> goodsType) {
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItem::getTenantId, tenantId)
            .eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag())
            .in(MarketItem::getGoodsType, goodsType);
        return list(wrapper);
    }

    @Override
    public List<MarketItem> listByParam(MarketItemParam param) {
        return list(buildMPJQueryWrapper(param));
    }

    @Override
    public Page<MarketItem> pageMarketItemByParam(MarketItemXmParam param) {
        Page<MarketItem> page = new Page<>(param.getPageNum(), param.getPageSize());
        return page(page, buildMPJOnsaleQueryWrapper(param));
    }

    public MPJLambdaWrapper<MarketItem> buildMPJOnsaleQueryWrapper(MarketItemXmParam param) {
        MPJLambdaWrapper<MarketItem> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(MarketItem.class);
        // 有上架策略表相关的参数时，关联查询
        if (Objects.nonNull(param.getOnSaleFlag()) || Objects.nonNull(param.getOnSaleTargetId())
            // 有根据是否展示排序
            || (CollectionUtils.isNotEmpty(param.getSortDescList()) && param.getSortDescList().contains(MarketConstants.ORDERBY_ON_SALE_SHOW))
            // 有根据大客户标识是否大客户专享
            || !MarketConstants.ADMIN_SHOW_APPOINT.equals(param.getAdminShow())) {
            queryWrapper.leftJoin(MarketItemOnsaleStrategyMapping.class, MarketItemOnsaleStrategyMapping::getItemId, MarketItem::getId);
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getTenantId,param.getTenantId());
        }
        // 有类目或标题时，关联查询market
        if (CollectionUtils.isNotEmpty(param.getMarketIds()) || CollectionUtils.isNotEmpty(param.getCategoryIds()) || Objects.nonNull(param.getMarketTitle())){
            queryWrapper.leftJoin(Market.class, Market::getId, MarketItem::getMarketId);
            queryWrapper.eq(Market::getTenantId,param.getTenantId());
            queryWrapper.in(CollectionUtils.isNotEmpty(param.getMarketIds()),Market::getId,param.getMarketIds());
            if(Objects.equals (param.getMarketTitleLikeFlag(),false)) {
                queryWrapper.eq (!StringUtils.isEmpty (param.getMarketTitle ()), Market::getTitle, param.getMarketTitle ());
            }else{
                queryWrapper.like (!StringUtils.isEmpty (param.getMarketTitle ()), Market::getTitle, param.getMarketTitle ());
            }
            queryWrapper.in(CollectionUtils.isNotEmpty(param.getCategoryIds()),Market::getCategoryId,param.getCategoryIds());
        }

        queryWrapper.eq(MarketItem::getTenantId, param.getTenantId());
        // admin_show = 2  -> m_type=0 or sku in,需要特殊处理
        if (!MarketConstants.ADMIN_SHOW_ALL.equals(param.getAdminShow())) {
            queryWrapper.in(CollectionUtils.isNotEmpty(param.getItemCodeList()), MarketItem::getItemCode, param.getItemCodeList());
        }
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getMarketIds()), MarketItem::getMarketId, param.getMarketIds());
        queryWrapper.eq(Objects.nonNull(param.getDeleteFlag()), MarketItem::getDeleteFlag, param.getDeleteFlag());
        if (Objects.nonNull(param.getOnSaleFlag())) {
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getOnSale, param.getOnSaleFlag());
        }
        if (Objects.nonNull(param.getOnSaleTargetId())) {
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getTargetId, param.getOnSaleTargetId());
        }
        if (Objects.isNull(param.getAdminShow())) {
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getMType, NumberConstants.ZERO);
        } else if (MarketConstants.ADMIN_SHOW_ALL.equals(param.getAdminShow())) {
            queryWrapper.and(wrapper -> wrapper.eq(MarketItemOnsaleStrategyMapping::getMType, NumberConstants.ZERO)
                .or()
                .in(CollectionUtils.isNotEmpty(param.getItemCodeList()),MarketItem::getItemCode, param.getItemCodeList()));
        }

        if (CollectionUtils.isNotEmpty(param.getSortDescList())){
            for (String sort : param.getSortDescList()) {
                switch (sort) {
                    case MarketConstants.ORDERBY_ITEM_CODE_LIST:
                        if (CollectionUtils.isNotEmpty(param.getItemCodeList())) {
                            queryWrapper.last("ORDER BY FIELD(`item_code`, "
                                + param.getItemCodeList().stream()
                                .map(i -> "\"" + i + "\"")
                                .collect(Collectors.joining(","))
                                + ")");
                        }
                        break;
                    case MarketConstants.ORDERBY_ON_SALE_SHOW:
                        queryWrapper.orderByDesc(MarketItemOnsaleStrategyMapping::getShowFlag);
                        break;
                    default:
                        break;
                }
            }
        }

        return queryWrapper;
    }


    @Override
    public Page<MarketItem> pageByParam(MarketItemParam param) {
        Page<MarketItem> page = new Page<>(param.getPageNum(), param.getPageSize());
        return page(page, buildMPJQueryWrapper(param));
    }

    @Override
    public Page<MarketItem> pageStoreItemByParam(MarketItemStoreQueryParam param) {
        Page<MarketItem> page = new Page<>(param.getPageNum(), param.getPageSize());
        return page(page, buildMPJStoreQueryWrapper(param));
    }

    @Override
    public Page<MarketItem> pageByTenantId(Long tenantId, Integer pageIndex, Integer pageSize) {
        Page<MarketItem> page = new Page<>(pageIndex, pageSize);
        LambdaQueryWrapper<MarketItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(tenantId), MarketItem::getTenantId, tenantId);
        queryWrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag());
        return page(page, queryWrapper);
    }

    @Override
    public List<MarketItem> listByUpdateTime(String updateTime) {
        LambdaQueryWrapper<MarketItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.le(MarketItem::getUpdateTime, updateTime);
        queryWrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag());
        return list();
    }

    @Override
    public List<MarketItemOnSaleSimpleDTO> queryMarketItemOnSaleInfo(Long tenantId, Integer onSale, List<Long> skuIds) {
        return getBaseMapper().queryMarketItemOnSaleInfo(tenantId, onSale, skuIds);
    }

    @Override
    public List<MarketItemOnSaleSimpleDTO> queryOnSaleMarketItems(Long tenantId, Integer onSale) {
        return getBaseMapper().queryOnSaleMarketItems(tenantId, onSale);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateBatchOnSaleById(Integer onSale, Set<Long> itemIds) {
        if (Objects.isNull(onSale) || CollectionUtils.isEmpty(itemIds)) {
            return 0;
        }
        LambdaUpdateWrapper<MarketItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(MarketItem::getOnSale, onSale);
        updateWrapper.in(MarketItem::getId, itemIds);
        update(updateWrapper);
        // 校验是否全部更新成功
        LambdaQueryWrapper<MarketItem> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(MarketItem::getOnSale, onSale);
        countWrapper.in(MarketItem::getId, itemIds);
        Long updateCount = count(countWrapper);
        if (updateCount != itemIds.size()) {
            throw new BizException("批量更新上下架失败！");
        }
        return itemIds.size();
    }

    public MPJLambdaWrapper<MarketItem> buildMPJQueryWrapper(MarketItemParam param) {
        MPJLambdaWrapper<MarketItem> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(MarketItem.class);
        queryWrapper.selectIgnore(MarketItem::getTitle, MarketItem::getMainPicture);
        queryWrapper.select(Market::getTitle, Market::getMainPicture);

        queryWrapper.leftJoin(Market.class, Market::getId, MarketItem::getMarketId);

        queryWrapper.leftJoin(MarketDetail.class, MarketDetail::getMarketId, MarketItem::getMarketId);

        queryWrapper.leftJoin(MarketItemClassification.class, MarketItemClassification::getMarketId, Market::getId);
        // 如果不是查询所有
        //如果是xm的查询， 增加admin的查询条件
        if(xmTenantId.equals (param.getTenantId ())) {
            if (param.isNotAllFlag ()) {
                queryWrapper.leftJoin (MarketItemDetail.class, MarketItemDetail::getMarketItemId, MarketItem::getId);
                if (Objects.nonNull (param.getAdminId ())) {
                    // 如果adminid不是空， 则查询大客户代仓品和xm自营品
                    queryWrapper.and (q -> q.eq (MarketItemDetail::getCustomerId, param.getAdminId ()).or ().isNull (MarketItemDetail::getCustomerId));
                } else {
                    // 如果adminid是空， 则查询xm自营品
                    queryWrapper.and (q -> q.isNull (MarketItemDetail::getCustomerId));
                }
            }
        }
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getClassificationIds()), MarketItemClassification::getClassificationId, param.getClassificationIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getCategoryIds()), Market::getCategoryId, param.getCategoryIds());

        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MarketItem::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getItemId()), MarketItem::getId, param.getItemId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getItemIds()), MarketItem::getId, param.getItemIds());
        queryWrapper.eq(Objects.nonNull(param.getSkuId()), MarketItem::getSkuId, param.getSkuId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getSkuIds()), MarketItem::getSkuId, param.getSkuIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getItemCodes()), MarketItem::getItemCode, param.getItemCodes());
        queryWrapper.eq(Objects.nonNull(param.getItemCode()), MarketItem::getItemCode, param.getItemCode());
        queryWrapper.like(Objects.nonNull(param.getItemCodeLike()), MarketItem::getItemCode, param.getItemCodeLike());
        queryWrapper.like(Objects.nonNull(param.getBrandName()), MarketItem::getBrandName, param.getBrandName());
        queryWrapper.eq(Objects.nonNull(param.getMarketId()), MarketItem::getMarketId, param.getMarketId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getMarketIds()), MarketItem::getMarketId, param.getMarketIds());
        queryWrapper.eq(Objects.nonNull(param.getGoodsType()), MarketItem::getGoodsType, param.getGoodsType());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getGoodsTypes()), MarketItem::getGoodsType, param.getGoodsTypes());
        queryWrapper.like(Objects.nonNull(param.getItemTitle()), Market::getTitle, param.getItemTitle());
        queryWrapper.eq(Objects.nonNull(param.getEqTitle()), Market::getTitle, param.getEqTitle());
        queryWrapper.eq(Objects.nonNull(param.getItemType()), MarketItem::getItemType, param.getItemType());
        if (Boolean.TRUE.equals(param.getCombineFlag())) {
            queryWrapper.eq(MarketItem::getItemType, MarketItemEnum.ItemTypeEnum.COMBINE_ITEM.getCode());
        }
        if (Boolean.FALSE.equals(param.getCombineFlag())) {
            queryWrapper.ne(MarketItem::getItemType, MarketItemEnum.ItemTypeEnum.COMBINE_ITEM.getCode());
        }
        queryWrapper.eq(Objects.nonNull(param.getOnSale()), MarketItem::getOnSale, param.getOnSale());
        queryWrapper.eq(Objects.nonNull(param.getDeleteFlag()), MarketItem::getDeleteFlag, param.getDeleteFlag());
        queryWrapper.eq(Objects.nonNull(param.getDeleteFlag()), Market::getDeleteFlag, param.getDeleteFlag());
        queryWrapper.eq(Objects.nonNull(param.getPresaleSwitch()), MarketItem::getPresaleSwitch, param.getPresaleSwitch());
        queryWrapper.eq(Objects.nonNull(param.getOutId()), MarketDetail::getOutId, param.getOutId());
        queryWrapper.orderByDesc(MarketItem::getId);
        return queryWrapper;
    }

    public MPJLambdaWrapper<MarketItem> buildMPJStoreQueryWrapper(MarketItemStoreQueryParam param) {
        MPJLambdaWrapper<MarketItem> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.selectAll(MarketItem.class);
        queryWrapper.leftJoin(MarketItemOnsaleStrategyMapping.class, MarketItemOnsaleStrategyMapping::getItemId, MarketItem::getId);
        if (Objects.nonNull(param.getTitle())){
            queryWrapper.leftJoin(Market.class, Market::getId, MarketItem::getMarketId);
            queryWrapper.eq(Objects.nonNull(param.getTenantId()), Market::getTenantId, param.getTenantId());
            queryWrapper.like(Market::getTitle,param.getTitle());
        }

        queryWrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag());
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MarketItem::getTenantId, param.getTenantId());
        queryWrapper.eq(Objects.nonNull(param.getTenantId()), MarketItemOnsaleStrategyMapping::getTenantId, param.getTenantId());
        if (Objects.nonNull(param.getStoreId())) {
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getStrategyType, MarketItemOnsaleStrategyMappingEnum.StrategyTypeEnum.STORE.getCode());
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getTargetId, param.getStoreId());
        }

        if (Objects.isNull(param.getOnSale()) || MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode().equals(param.getOnSale())){
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getOnSale, MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode());
            queryWrapper.eq(MarketItem::getOnSale, MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode());
        }else if (MarketItemOnSaleEnum.OnSaleTypeEnum.SOLD_OUT.getCode().equals(param.getOnSale())){
            queryWrapper.eq(MarketItemOnsaleStrategyMapping::getOnSale, MarketItemOnSaleEnum.OnSaleTypeEnum.SOLD_OUT.getCode());
            queryWrapper.eq(MarketItem::getOnSale, MarketItemOnSaleEnum.OnSaleTypeEnum.SOLD_OUT.getCode());
        }
        queryWrapper.eq(Objects.nonNull(param.getItemId()), MarketItem::getId, param.getItemId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getItemIds()), MarketItem::getId, param.getItemIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getMarketIds()), MarketItem::getMarketId, param.getMarketIds());

        queryWrapper.groupBy(MarketItem::getId);
        return queryWrapper;
    }

    @Override
    @Deprecated
    public IPage<MarketItemWithClassificationDTO> queryMarketItemWithClassification(MarketItemWithClassificationQueryParam queryParam) {
        MPJLambdaWrapper<MarketItem> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.select(MarketItem::getId, MarketItem::getSpecification, MarketItem::getSpecificationUnit);
        queryWrapper.select(Market::getTitle, Market::getSubTitle, Market::getMainPicture);
        queryWrapper.selectAs(Market::getId, "marketId");
        queryWrapper.select(MarketItemClassification::getClassificationId);
        queryWrapper.leftJoin(Market.class, Market::getId, MarketItem::getMarketId);
        queryWrapper.leftJoin(MarketItemClassification.class, MarketItemClassification::getMarketId, Market::getId);

        queryWrapper.eq(MarketItem::getTenantId, queryParam.getTenantId());
        queryWrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag());
        queryWrapper.like(!StringUtils.isEmpty(queryParam.getTitle()), Market::getTitle, queryParam.getTitle());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryParam.getItemIds()), MarketItem::getId, queryParam.getItemIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryParam.getClassificationIds()), MarketItemClassification::getClassificationId, queryParam.getClassificationIds());
        queryWrapper.ne(MarketItem::getItemType, MarketItemEnum.ItemTypeEnum.COMBINE_ITEM.getCode());
        queryWrapper.orderByDesc(MarketItem::getId);
        IPage<MarketItemWithClassificationDTO> page = selectJoinListPage(new Page<>(queryParam.getPageIndex(), queryParam.getPageSize()), MarketItemWithClassificationDTO.class, queryWrapper);
        return page;
    }

    @Override
    public boolean cleanNoGoodsInfo(Long marketItemId) {
        LambdaUpdateWrapper<MarketItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MarketItem::getId, marketItemId);
        updateWrapper.set(MarketItem::getNoGoodsSupplyPrice, null);
        updateWrapper.set(MarketItem::getSupplierName, null);
        updateWrapper.set(MarketItem::getSupplierId, null);
        return update(updateWrapper);
    }

    @Override
    public Integer countOnsaleItem(Long tenantId) {
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItem::getDeleteFlag, MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag())
                .in(MarketItem::getOnSale, MarketItemOnSaleEnum.OnSaleTypeEnum.ON_SALE.getCode ())
                .eq(MarketItem::getTenantId, tenantId);
        long count = count (wrapper);
        return Integer.valueOf (count + "");
    }

//    @Override
    //需要  后台分类 前台分类， 默认去除组合包   注意返回字段 处理itemId-》id   ， itemTitle-》title
    @Deprecated
    public IPage<PageMarketItemDTO> pageMarketItem(MarketItemPageQueryParam queryParam) {
        MPJLambdaWrapper<MarketItem> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.select(MarketItem::getTenantId, MarketItem::getSpecification, MarketItem::getSpecificationUnit,
            MarketItem::getItemCode, MarketItem::getBrandName, MarketItem::getGoodsType);
        queryWrapper.selectAs(MarketItem::getId, "itemId");
        queryWrapper.select(Market::getMainPicture, Market::getCategoryId);
        queryWrapper.selectAs(Market::getId, "marketId");
        queryWrapper.selectAs(Market::getTitle, "itemTitle");
        queryWrapper.select(MarketItemClassification::getClassificationId);

        queryWrapper.leftJoin(Market.class, Market::getId, MarketItem::getMarketId);
        queryWrapper.leftJoin(MarketItemClassification.class, MarketItemClassification::getMarketId, Market::getId);

        queryWrapper.eq(MarketItem::getTenantId, queryParam.getTenantId());
        queryWrapper.in(MarketItem::getItemType, NON_COMBINE_CODES);
        queryWrapper.eq(Objects.nonNull(queryParam.getDeleteFlag()), MarketItem::getDeleteFlag, queryParam.getDeleteFlag());
        queryWrapper.eq(Objects.nonNull(queryParam.getDeleteFlag()), Market::getDeleteFlag, queryParam.getDeleteFlag());
        queryWrapper.like(StringUtils.isNotEmpty(queryParam.getTitle()), Market::getTitle, queryParam.getTitle());
        queryWrapper.eq(Objects.nonNull(queryParam.getItemId()), MarketItem::getId, queryParam.getItemId());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryParam.getClassificationIds()), MarketItemClassification::getClassificationId, queryParam.getClassificationIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryParam.getCategoryIds()), Market::getCategoryId, queryParam.getCategoryIds());
        queryWrapper.eq(StringUtils.isNotEmpty(queryParam.getItemCode()), MarketItem::getItemCode, queryParam.getItemCode());
        queryWrapper.like(Objects.nonNull(queryParam.getBrandName()), MarketItem::getBrandName, queryParam.getBrandName());
        queryWrapper.in(CollectionUtils.isNotEmpty(queryParam.getGoodsType()), MarketItem::getGoodsType, queryParam.getGoodsType());
        queryWrapper.eq(Objects.nonNull(queryParam.getOnSale()), MarketItem::getOnSale, queryParam.getOnSale());

        queryWrapper.orderByDesc(MarketItem::getId);
        queryWrapper.groupBy(MarketItem::getId);
        return selectJoinListPage(new Page<>(queryParam.getPageNum(), queryParam.getPageSize()), PageMarketItemDTO.class, queryWrapper);
    }


    @Override
    public List<MarketItem> getByItemCode(Long tenantId, String itemCode) {
        if (StringUtils.isEmpty(itemCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MarketItem::getItemCode, itemCode);
        wrapper.eq(MarketItem::getTenantId, tenantId);
        return list(wrapper);
    }


    @Override
    public List<MarketItem> getByItemCodes(Long tenantId, List<String> itemCodes) {
        if (CollectionUtils.isEmpty(itemCodes)|| ObjectUtil.isEmpty(tenantId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MarketItem> wrapper = new LambdaQueryWrapper();
        wrapper.in(MarketItem::getItemCode, itemCodes);
        wrapper.eq(MarketItem::getTenantId, tenantId);
        return list(wrapper);
    }

    @Override
    public void updateByIdWithNullAttribute(MarketItem marketItem) {
        LambdaUpdateWrapper<MarketItem> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MarketItem::getId, marketItem.getId ()); // 根据 id 选择要更新的记录

        // 根据传入的 MarketItem 对象设置需要更新的字段
        if (marketItem.getTenantId() != null) {
            updateWrapper.set(MarketItem::getTenantId, marketItem.getTenantId());
        }
        if (marketItem.getSkuId() != null) {
            updateWrapper.set(MarketItem::getSkuId, marketItem.getSkuId());
        }
        if (marketItem.getTitle() != null) {
            updateWrapper.set(MarketItem::getTitle, marketItem.getTitle());
        }
        if (marketItem.getSubTitle() != null) {
            updateWrapper.set(MarketItem::getSubTitle, marketItem.getSubTitle());
        }
        if (marketItem.getOrigin() != null) {
            updateWrapper.set(MarketItem::getOrigin, marketItem.getOrigin());
        }
        updateWrapper.set(MarketItem::getMainPicture, marketItem.getMainPicture());
        if (marketItem.getDetailPicture() != null) {
            updateWrapper.set(MarketItem::getDetailPicture, marketItem.getDetailPicture());
        }
        if (marketItem.getSpecification() != null) {
            updateWrapper.set(MarketItem::getSpecification, marketItem.getSpecification());
        }
        if (marketItem.getSpecificationUnit() != null) {
            updateWrapper.set(MarketItem::getSpecificationUnit, marketItem.getSpecificationUnit());
        }
        if (marketItem.getCreateTime() != null) {
            updateWrapper.set(MarketItem::getCreateTime, marketItem.getCreateTime());
        }
        if (marketItem.getUpdateTime() != null) {
            updateWrapper.set(MarketItem::getUpdateTime, marketItem.getUpdateTime());
        }
        if (marketItem.getMarketId() != null) {
            updateWrapper.set(MarketItem::getMarketId, marketItem.getMarketId());
        }
        if (marketItem.getBrandId() != null) {
            updateWrapper.set(MarketItem::getBrandId, marketItem.getBrandId());
        }
        if (marketItem.getBrandName() != null) {
            updateWrapper.set(MarketItem::getBrandName, marketItem.getBrandName());
        }
        if (marketItem.getSupplierName() != null) {
            updateWrapper.set(MarketItem::getSupplierName, marketItem.getSupplierName());
        }
        updateWrapper.set(MarketItem::getNoGoodsSupplyPrice, marketItem.getNoGoodsSupplyPrice());
        if (marketItem.getItemCode() != null) {
            updateWrapper.set(MarketItem::getItemCode, marketItem.getItemCode());
        }
        if (marketItem.getWeightNotes() != null) {
            updateWrapper.set(MarketItem::getWeightNotes, marketItem.getWeightNotes());
        }
        if (marketItem.getSupplierId() != null) {
            updateWrapper.set(MarketItem::getSupplierId, marketItem.getSupplierId());
        }
        if (marketItem.getMaxAfterSaleAmount() != null) {
            updateWrapper.set(MarketItem::getMaxAfterSaleAmount, marketItem.getMaxAfterSaleAmount());
        }
        if (marketItem.getAfterSaleUnit() != null) {
            updateWrapper.set(MarketItem::getAfterSaleUnit, marketItem.getAfterSaleUnit());
        }
        if (marketItem.getDeleteFlag() != null) {
            updateWrapper.set(MarketItem::getDeleteFlag, marketItem.getDeleteFlag());
        }
        if (marketItem.getMiniOrderQuantity() != null) {
            updateWrapper.set(MarketItem::getMiniOrderQuantity, marketItem.getMiniOrderQuantity());
        }
        if (marketItem.getPriceType() != null) {
            updateWrapper.set(MarketItem::getPriceType, marketItem.getPriceType());
        }
        if (marketItem.getGoodsType() != null) {
            updateWrapper.set(MarketItem::getGoodsType, marketItem.getGoodsType());
        }
        if (marketItem.getItemType() != null) {
            updateWrapper.set(MarketItem::getItemType, marketItem.getItemType());
        }
        if (marketItem.getOnSale() != null) {
            updateWrapper.set(MarketItem::getOnSale, marketItem.getOnSale());
        }
        if (marketItem.getCreateUserId() != null) {
            updateWrapper.set(MarketItem::getCreateUserId, marketItem.getCreateUserId());
        }
        if (marketItem.getEditUserId() != null) {
            updateWrapper.set(MarketItem::getEditUserId, marketItem.getEditUserId());
        }
        if (marketItem.getItemSaleMode() != null) {
            updateWrapper.set(MarketItem::getItemSaleMode, marketItem.getItemSaleMode());
        }
        if (marketItem.getBuyMultiple() != null) {
            updateWrapper.set(MarketItem::getBuyMultiple, marketItem.getBuyMultiple());
        }
        if (marketItem.getBuyMultipleSwitch() != null) {
            updateWrapper.set(MarketItem::getBuyMultipleSwitch, marketItem.getBuyMultipleSwitch());
        }
        if (marketItem.getVideoUrl() != null) {
            updateWrapper.set(MarketItem::getVideoUrl, marketItem.getVideoUrl());
        }
        if (marketItem.getAfterSaleRuleDetail() != null) {
            updateWrapper.set(MarketItem::getAfterSaleRuleDetail, marketItem.getAfterSaleRuleDetail());
        }
        if (marketItem.getVideoUploadUser() != null) {
            updateWrapper.set(MarketItem::getVideoUploadUser, marketItem.getVideoUploadUser());
        }
        if (marketItem.getVideoUploadTime() != null) {
            updateWrapper.set(MarketItem::getVideoUploadTime, marketItem.getVideoUploadTime());
        }
        if (marketItem.getPresaleSwitch() != null) {
            updateWrapper.set(MarketItem::getPresaleSwitch, marketItem.getPresaleSwitch());
        }
        if (marketItem.getStandardUnitPrice() != null) {
            updateWrapper.set(MarketItem::getStandardUnitPrice, marketItem.getStandardUnitPrice());
        }
        if (marketItem.getStoreInventoryControlFlag() != null) {
            updateWrapper.set(MarketItem::getStoreInventoryControlFlag, marketItem.getStoreInventoryControlFlag());
        }

        if (marketItem.getWeight() != null) {
            updateWrapper.set(MarketItem::getWeight, marketItem.getWeight());
        }

        // 执行更新操作
        getBaseMapper().update(null, updateWrapper);
    }
}
