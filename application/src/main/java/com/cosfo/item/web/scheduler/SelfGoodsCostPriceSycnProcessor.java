package com.cosfo.item.web.scheduler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.web.domain.service.SelfGoodsCostPriceSycnService;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * 每天凌晨跑任务，更新前一天的自营商品成本价
 */
@Component
@Slf4j
public class SelfGoodsCostPriceSycnProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private SelfGoodsCostPriceSycnService selfGoodsCostPriceSycnService;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始进行SelfGoodsCostPriceUpdateProcessor任务");
        Long tenantId = null;
        if (ObjectUtil.isNull(context)) {
            return new ProcessResult(true);
        } else {
            Map<String, String> map = Collections.emptyMap ();
            if (!StringUtils.isEmpty (context.getInstanceParameters ())) {
                map = JSON.parseObject (context.getInstanceParameters (), Map.class);
            } else if (!StringUtils.isEmpty (context.getJobParameters ())) {
                map = JSON.parseObject (context.getJobParameters (), Map.class);
            }
            if (map.containsKey ("tenant")) {
                tenantId = Long.valueOf (map.get ("tenant"));
                //-99数据初始化 全部数据
                if (tenantId.equals (-99L)) {
                    tenantId = null;
                }
            }
            selfGoodsCostPriceSycnService.sycnSelfGoodsCostPriceFromOffline (tenantId);
            return new ProcessResult (true);
        }
    }
}