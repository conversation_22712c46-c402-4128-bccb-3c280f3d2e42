package com.cosfo.item.infrastructure.item.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.item.dto.StockQueryParam;
import com.cosfo.item.infrastructure.item.model.Stock;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存表(Stock)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-05 14:19:51
 */
public interface StockDao extends IService<Stock> {

    List<Stock> listByParam(StockQueryParam param);

    /**
     * 更新前查询
     *
     * @return
     */
    Stock preUpdateQuery(@Param("tenantId") Long tenantId, @Param("itemId") Long itemId);

    /**
     * 更新库存数量
     * @param id 主键
     * @param addAmount 增加数量
     */
    Integer increaseStock(@Param("id") Long id, @Param("addAmount") Integer addAmount);
}
