package com.cosfo.item.web.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import com.cosfo.item.infrastructure.price.dao.MarketItemUnfairPriceStrategyDao;
import com.cosfo.item.infrastructure.price.model.MarketItemUnfairPriceStrategy;
import com.cosfo.item.web.domain.dto.MarketItemUnfairPriceStrategyDTO;
import com.cosfo.item.web.domain.converter.MarketItemUnfairPriceStrategyConvert;
import com.cosfo.item.web.domain.vo.UnfairPriceStrategyVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class UnfairPriceStrategyDomainService {

    @Autowired
    private MarketItemUnfairPriceStrategyDao unfairPriceStrategyDao;

    /**
     * 查询一个商品的倒挂规则
     */
    public UnfairPriceStrategyVO getStrategyValueByItemIdAndTargetType(Long tenantId, Long itemId, Integer targetType) {
        UnfairPriceStrategyVO vo = new UnfairPriceStrategyVO();
        vo.setItemId(itemId);
        vo.setTenantId(tenantId);

        if (ObjectUtil.isNull(targetType)) {
            targetType = MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode();
        }
        MarketItemUnfairPriceStrategy strategyValue = unfairPriceStrategyDao.getStrategyValueByItemIdAndTargetType(tenantId, itemId, targetType);
        if (ObjectUtil.isNull(strategyValue)) {
            MarketItemUnfairPriceStrategyEnum.StrategyValueEnum defaultStrategyValue = getDefaultStrategyValueByTenantId(tenantId);
            vo.setDefaultFlag(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.Y);
            vo.setStrategyValue(defaultStrategyValue);
        } else {
            vo.setDefaultFlag(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.N);
            vo.setStrategyValue(MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.getByCode(strategyValue.getStrategyValue()));
        }
        return vo;
    }
    /**
     * 查询一个商品的倒挂规则
     */
    public Map<Long,UnfairPriceStrategyVO> getStrategyValueByItemIdsAndTargetType(Long tenantId, List<Long> itemIds, Integer targetType) {
        MarketItemUnfairPriceStrategyEnum.StrategyValueEnum defaultStrategyValue = getDefaultStrategyValueByTenantId(tenantId);
        if (ObjectUtil.isNull(targetType)) {
            targetType = MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode();
        }
        Map<Long, MarketItemUnfairPriceStrategy> map;
        List<MarketItemUnfairPriceStrategy> strategyValues = unfairPriceStrategyDao.getStrategyValueByItemIsdAndTargetType(tenantId, itemIds, targetType);
        if(CollectionUtil.isNotEmpty (strategyValues)){
            map = strategyValues.stream().collect(Collectors.toMap(MarketItemUnfairPriceStrategy::getItemId, x -> x));
        }else{
            map = new HashMap<> ();
        }

        Map<Long,UnfairPriceStrategyVO> reslut = new HashMap<> ();
        for (Long itemId : itemIds) {
            UnfairPriceStrategyVO vo = new UnfairPriceStrategyVO();
            vo.setItemId(itemId);
            vo.setTenantId(tenantId);

            if (ObjectUtil.isNull(targetType)) {
                targetType = MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode();
            }
            MarketItemUnfairPriceStrategy strategyValue = map.get(itemId);
            if (ObjectUtil.isNull(strategyValue)) {
                vo.setDefaultFlag(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.Y);
                vo.setStrategyValue(defaultStrategyValue);
            } else {
                vo.setDefaultFlag(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.N);
                vo.setStrategyValue(MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.getByCode(strategyValue.getStrategyValue()));
            }
            reslut.put (itemId,vo);
        }
        return reslut;
    }

    /**
     * 查询租户的默认规则
     */
    public MarketItemUnfairPriceStrategyEnum.StrategyValueEnum getDefaultStrategyValueByTenantId(Long tenantId) {
        MarketItemUnfairPriceStrategy strategy = unfairPriceStrategyDao.getDefaultStrategyValueByTenantId(tenantId);
        if (ObjectUtil.isNull(strategy)) {
            strategy = MarketItemUnfairPriceStrategyConvert.buildDefaultUnfairPriceStrategy(tenantId);
            unfairPriceStrategyDao.save(strategy);
        }
        return MarketItemUnfairPriceStrategyEnum.StrategyValueEnum.getByCode(strategy.getStrategyValue());
    }

    public void upsertDefaultUnfairPriceStrategy(Long tenantId, MarketItemUnfairPriceStrategyEnum.StrategyValueEnum strategyValueEnum) {
        MarketItemUnfairPriceStrategy strategy = unfairPriceStrategyDao.getDefaultStrategyValueByTenantId(tenantId);
        if (ObjectUtil.isNull(strategy)) {
            strategy = MarketItemUnfairPriceStrategyConvert.buildDefaultUnfairPriceStrategy(tenantId);
        }
        strategy.setStrategyValue(strategyValueEnum.getCode());
        unfairPriceStrategyDao.saveOrUpdate(strategy);
    }

    /**
     * 查询未使用默认放倒挂策略的itemid
     *
     * @param tenantId
     * @return
     */
    public Set<Long> listItemIdsWithOutDefaultStrategy(Long tenantId) {
        return unfairPriceStrategyDao.listItemIdsWithOutDefaultStrategy(tenantId);
    }

    /**
     * 保存商品的 倒挂策略
     *
     * @param marketItemUnfairPriceStrategyDTO
     * @param tenantId
     * @param itemId
     * @return
     */
    public Boolean upsertUnfairPriceStrategy(MarketItemUnfairPriceStrategyDTO marketItemUnfairPriceStrategyDTO, Long tenantId, Long itemId) {
        if (ObjectUtil.isNull(marketItemUnfairPriceStrategyDTO)) {
            return false;
        }
        MarketItemUnfairPriceStrategy marketItemUnfairPriceStrategy = unfairPriceStrategyDao.getStrategyValueByItemIdAndTargetType(tenantId, itemId, MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode());
        if (Objects.nonNull(marketItemUnfairPriceStrategy)) {
            if (MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.Y.getCode().equals(marketItemUnfairPriceStrategyDTO.getDefaultFlag())) {
                return unfairPriceStrategyDao.removeById(marketItemUnfairPriceStrategy.getId());
            }
        }else {
            if(MarketItemUnfairPriceStrategyEnum.DefaultFlagEnum.Y.getCode().equals(marketItemUnfairPriceStrategyDTO.getDefaultFlag())){
                return true;
            }
            marketItemUnfairPriceStrategy = MarketItemUnfairPriceStrategyConvert.buildDefaultUnfairPriceStrategy(tenantId);
        }

        marketItemUnfairPriceStrategy.setItemId(itemId);
        marketItemUnfairPriceStrategy.setDefaultFlag(marketItemUnfairPriceStrategyDTO.getDefaultFlag());
        marketItemUnfairPriceStrategy.setStrategyValue(marketItemUnfairPriceStrategyDTO.getStrategyType());
        return unfairPriceStrategyDao.saveOrUpdate(marketItemUnfairPriceStrategy);
    }

    public List<MarketItemUnfairPriceStrategy> getByDefaultFlagAndStrategyValue(List<Integer> strategyValues, Integer defaultFlag, Integer targetType, Long tenantId) {
        if (Objects.nonNull(targetType)) {
            targetType = MarketItemUnfairPriceStrategyEnum.TargetTypeEnum.TENANT.getCode();
        }

        return unfairPriceStrategyDao.getByDefaultFlagAndStrategyValue(strategyValues, defaultFlag, targetType, tenantId);
    }
}
