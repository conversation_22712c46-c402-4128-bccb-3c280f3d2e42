package com.cosfo.item.infrastructure.price.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategyMapping;
import com.cosfo.item.infrastructure.price.mapper.MarketItemPriceStrategyMappingMapper;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyMappingDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商品item价格策略映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Service
public class MarketItemPriceStrategyMappingDaoImpl extends ServiceImpl<MarketItemPriceStrategyMappingMapper, MarketItemPriceStrategyMapping> implements MarketItemPriceStrategyMappingDao {

    @Override
    public List<MarketItemPriceStrategyMapping> listByStrategyIds(List<Long> itemPriceStrategyIds) {
        LambdaQueryWrapper<MarketItemPriceStrategyMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MarketItemPriceStrategyMapping::getItemPriceStrategyId,itemPriceStrategyIds);
        return list (wrapper);
    }

    @Override
    public void removeByStrategyIdAndTargetIds(Long strategyId, List<Long> targetIds) {
        LambdaQueryWrapper<MarketItemPriceStrategyMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MarketItemPriceStrategyMapping::getItemPriceStrategyId,strategyId);
        wrapper.in(MarketItemPriceStrategyMapping::getTargetId,targetIds);
        remove (wrapper);
    }
}
