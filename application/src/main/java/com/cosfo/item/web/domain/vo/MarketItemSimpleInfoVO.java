package com.cosfo.item.web.domain.vo;

import com.cosfo.item.common.dto.LadderPriceDTO;
import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/12 11:06
 * @Description: 查询item的简单信息（只包含主表中的内容）
 */
@Data
public class MarketItemSimpleInfoVO {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * market_id
     */
    private Long marketId;
    /**
     * market_item_id
     */
    private Long marketItemId;
    /**
     * 标题
     */
    private String title;
    /**
     * 主图
     */
    private String mainPicture;
    /**
     * 货源类型
     *
     * @see com.cofso.item.client.enums.GoodsTypeEnum
     */
    private Integer goodsType;
    /**
     * item类型
     *
     * @see com.cofso.item.client.enums.ItemTypeEnum
     */
    private Integer itemType;
    /**
     * 最小起订量
     */
    private Integer miniOrderQuantity;
    /**
     * sku
     */
    private Long skuId;
    /**
     * 规格
     */
    private String specification;
    private String specificationUnit;


    /**
     * 商品预售开关 0-不可预售 1-可预售
     */
    private Integer presaleSwitch;
    /**
     * 阶梯价
     * eg：[{"price":197.14,"unit":1}]
     */
    private List<LadderPriceDTO> ladderPrices;
}
