package com.cosfo.item.infrastructure.price.model;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 商品item价格策略表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketItemPriceStrategy implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * item id
     */
    private Long itemId;

    /**
     * 0、按成本价百分比上浮 1、按成本价定额上浮 2、固定价 3、按售价百分比下调 4、按售价定额下调
     */
    private Integer strategyType;

    /**
     * 策略值
     */
    private BigDecimal strategyValue;

    /**
     * 策略值
     */
    private String priceStrategyValue;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 价格目标 0-品牌方,1-门店,2-运营区域,3-门店分组
     */
    private Integer targetType;

    /**
     * 备注
     */
    private String remarks;

}
