package com.cosfo.item.infrastructure.item.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.item.dto.MarketItemDetailParam;
import com.cosfo.item.infrastructure.item.model.MarketItemDetail;

import java.util.List;

/**
 * <p>
 * 商品item扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
public interface MarketItemDetailDao extends IService<MarketItemDetail> {

    List<MarketItemDetail> listByParam(MarketItemDetailParam param);

    MarketItemDetail getByMarketItemId(Long itemId);

    MarketItemDetail getByOutId(Long outId);

    List<MarketItemDetail> getByOutIds(List<Long> outIds);

    List<MarketItemDetail> getByIds(List<Long> ids);

    void updateItemLabelById(MarketItemDetail marketItemDetail);
}
