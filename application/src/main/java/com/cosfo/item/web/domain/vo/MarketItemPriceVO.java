package com.cosfo.item.web.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class MarketItemPriceVO {

    private Long marketItemId;
    /**
     * 租户价格
     */
    private PriceDetailVO tenantPrice;

    /**
     * 门店价格
     * key=storeId
     * value=价格
     */
    private Map<Long,PriceDetailVO> storePriceList;

    /**
     * 运营区域价格
     * key=areano
     * value=价格
     */
    private Map<Long,PriceDetailVO> areaNoPriceList;
}
