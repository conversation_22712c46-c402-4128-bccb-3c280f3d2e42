package com.cosfo.item.infrastructure.price.dto;

import java.math.BigDecimal;
import java.util.Set;
import lombok.Data;

@Data
public class ItemChangeMessageDTO {

    private Long tenantId;
    private Long marketItemId;

    private Long storeId; // 将要废弃，改用storeIds；

    private Set<Long> storeIds;

    // 来自market_item.onsale;
    private Integer onSale;


    /**
     * 无货商品供应价
     */
    private BigDecimal noGoodsSupplyPrice;

    /**
     * sku主键
     */
    private Long skuId;

    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    private Integer goodsType;
    /**
     * 0=实物商品,1=虚拟商品,2=组合品
     */
    private Integer itemType;

    // 来自market_item.onsale;
    private Integer BodyVersion = 1;
    public static String getOrderedMessageKey(ItemChangeMessageDTO dto) {
        return String.format("%d_%d", dto.getTenantId(), dto.getMarketItemId());
    }
}
