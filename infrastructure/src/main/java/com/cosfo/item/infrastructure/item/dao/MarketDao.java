package com.cosfo.item.infrastructure.item.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketPageQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketQueryParam;
import com.cosfo.item.infrastructure.item.model.Market;

import java.util.List;
import java.util.Set;

/**
 * 销售商品(Market)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-27 14:06:10
 */
public interface MarketDao extends IService<Market> {

    /**
     * 根据条件查询Market
     *
     * @param param
     * @return
     */
    List<Market> listByParam(MarketQueryParam param);

    /**
     * 根据条件返回查询组合品分页
     *
     * @param param
     * @return
     */
    Page<Market> pageCombineByParam(MarketCombineQueryParam param);

    /**
     * 根据条件返回查询market分页
     *
     * @param param
     * @return
     */
    Page<Market> pageMarketByParam(MarketPageQueryParam param);

    Set<String> queryBrandNameByTenantId(Long tenantId);
}
