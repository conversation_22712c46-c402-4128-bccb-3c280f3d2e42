package com.cosfo.item.web.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
public class ProductAgentSkuMappingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * skuId
     */
    private Long skuId;
    /**
     * 代理skucode
     */
    private String agentSkuCode;

    /**
     * 代理skuId
     */
    private Long agentSkuId;
}
