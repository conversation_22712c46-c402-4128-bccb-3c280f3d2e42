package com.cosfo.item.web.domain.service.itempricefactory;

import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
public class MarketItemPriceBuilderFactory implements ApplicationContextAware {

    private Map<MarketItemPriceStrategyEnum.StrategyTypeEnum, MarketItemPriceBuilder> map = new HashMap<> ();
    @Autowired
    private ApplicationContext applicationContext;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    private void init() {
        applicationContext.getBeansOfType(MarketItemPriceBuilder.class).forEach((a, b) -> map.put(b.getSupportType(), b));
    }


    public Optional<MarketItemPriceBuilder> getBuilder(MarketItemPriceStrategyEnum.StrategyTypeEnum strategyTypeEnum) {
        return Optional.ofNullable(map.get(strategyTypeEnum));
    }
}
