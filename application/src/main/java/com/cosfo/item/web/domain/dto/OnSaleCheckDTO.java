package com.cosfo.item.web.domain.dto;

import com.cosfo.item.infrastructure.item.model.MarketItem;
import lombok.Builder;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/6/7 17:56
 * @Description:
 */
@Data
@Builder
public class OnSaleCheckDTO {

    /**
     * 校验失败的market_item_id
     */
    private Collection<Long> failIds;

    /**
     * 校验成功的market_item
     */
    private List<MarketItem> successItemList;
}
