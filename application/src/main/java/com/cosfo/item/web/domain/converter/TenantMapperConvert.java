package com.cosfo.item.web.domain.converter;

import com.cosfo.manage.client.tenant.resp.TenantResp;
import net.xianmu.usercenter.client.tenant.resp.TenantResultResp;

/**
 * @Author: fansongsong
 * @Date: 2023-08-03
 * @Description:
 */
public class TenantMapperConvert {
    public static TenantResp respToTenantDto(TenantResultResp tenantResultResp) {
        if ( tenantResultResp == null ) {
            return null;
        }

        TenantResp tenantResp = new TenantResp();
        tenantResp.setId(tenantResultResp.getId());
        tenantResp.setPhone(tenantResultResp.getPhone());
        tenantResp.setTenantName(tenantResultResp.getTenantName());
        tenantResp.setAdminId(tenantResultResp.getAdminId());
        return tenantResp;
    }
}
