package com.cosfo.item.common.constants;

import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;

/**
 * @author: monna.chen
 * @Date: 2023/5/9 11:56
 * @Description:
 */
public class MarketCombineConstants {
    /**
     * 组合包默认规格单位
     */
    public static final String DEFAULT_SPEC_UNIT = "件";
    /**
     * sku_id为空时，默认sku_id=-1
     */
    public static final Long NULL_SKU = -1L;
    /**
     * 组合包子商品最小数量
     */
    public static final Integer COMBINE_MIN_QUANTITY = 2;
    /**
     * 组合包子商品最大数量
     */
    public static final Integer COMBINE_MAX_QUANTITY = 20;
    /**
     * 组合包价格策略默认作用于品牌方
     */
    public static final MarketItemPriceStrategyEnum.TargetTypeEnum DEFAULT_TARGET_TYPE = MarketItemPriceStrategyEnum.TargetTypeEnum.TENANT;
}
