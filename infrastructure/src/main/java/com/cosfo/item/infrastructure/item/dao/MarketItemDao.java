package com.cosfo.item.infrastructure.item.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cosfo.item.infrastructure.item.dto.*;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.github.yulichang.base.MPJBaseService;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 商品表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
public interface MarketItemDao extends MPJBaseService<MarketItem> {

    List<MarketItem> listBySkuIdsAndTypes(Long tenantId, Set<Long> skuIds, List<Integer> goodsTypes);

    List<MarketItem> listBySkuIdsAndTenantId(Long tenantId, Collection<Long> skuIds);

    List<MarketItem> listBySkuIdAndTenantIdAndGoodsTypes(Long skuId, Long tenantId, List<Integer> goodsTypes);

    List<MarketItem> listByTenantIdAndType(Long tenantId, Integer goodsType);

    List<MarketItem> listByTenantIdAndTypes(Long tenantId, List<Integer> goodsType);

    /**
     * 根据条件查询结果
     *
     * @param param
     * @return
     */
    List<MarketItem> listByParam(MarketItemParam param);

    /**
     * 根据条件返回分页结果
     *
     * @param param
     * @return
     */
    Page<MarketItem> pageMarketItemByParam(MarketItemXmParam param);

    /**
     * 根据条件返回分页结果
     *
     * @param param
     * @return
     */
    Page<MarketItem> pageByParam(MarketItemParam param);

    /**
     * 查询门店上架商品
     *
     * @param param
     * @return
     */
    Page<MarketItem> pageStoreItemByParam(MarketItemStoreQueryParam param);

    /**
     * 查询所有商品  数据初始化时使用 其他场景严禁使用
     *
     * @return
     */
    Page<MarketItem> pageByTenantId(Long tenantId,Integer pageIndex, Integer pageSize);

    /**
     * 查询某时间之后修改过的商品  数据初始化时使用 其他场景严禁使用
     *
     * @return
     */
    List<MarketItem> listByUpdateTime(String updateTime);

    /**
     * 查询符合上下架条件的skuId信息
     *
     * @param tenantId
     * @param onSale
     * @param skuIds
     * @return
     */
    List<MarketItemOnSaleSimpleDTO> queryMarketItemOnSaleInfo(Long tenantId, Integer onSale, List<Long> skuIds);

    /**
     * 查询上下架商品信息
     * @param tenantId
     * @param onSale
     * @return
     */
    List<MarketItemOnSaleSimpleDTO> queryOnSaleMarketItems(Long tenantId, Integer onSale);

    /**
     * 批量更新上下架状态
     * @param onSale
     * @param itemIds
     * @return
     */
    Integer updateBatchOnSaleById(Integer onSale, Set<Long> itemIds);

    /**
     * 订单费用商品项列表
     * @param queryParam
     * @return
     */
    @Deprecated
    IPage<MarketItemWithClassificationDTO> queryMarketItemWithClassification(MarketItemWithClassificationQueryParam queryParam);

    /**
     * 清理无货商品切换其他类型后残留字段
     * @return
     */
    boolean cleanNoGoodsInfo(Long marketItemId);

    /**
     * 查询上架中的商品数量
     * @param tenantId
     * @return
     */
    Integer countOnsaleItem(Long tenantId);

    /**
     * 选择商品通用组件 分页查询商品项
     * @param queryParam
     * @return
     */
    @Deprecated
    IPage<PageMarketItemDTO> pageMarketItem(MarketItemPageQueryParam queryParam);

    /**
     * 根据itemCod查询商品信息
     * @param itemCode
     * @return
     */
    List<MarketItem> getByItemCode(Long tenantId, String itemCode);
    List<MarketItem> getByItemCodes(Long tenantId, List<String> itemCodes);

    void updateByIdWithNullAttribute(MarketItem marketItem);
}
