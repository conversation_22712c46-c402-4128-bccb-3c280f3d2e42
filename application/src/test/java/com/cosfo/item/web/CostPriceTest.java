package com.cosfo.item.web;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cofso.item.client.provider.PriceProvider;
import com.cofso.item.client.req.CostPriceQueryRangeReq;
import com.cofso.item.client.req.MaxCostPriceQueryReq;
import com.cofso.item.client.resp.CostPriceRangeResultResp;
import com.cosfo.item.infrastructure.price.dto.CostPriceDTO;
import com.cosfo.item.web.domain.service.AddressService;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.vo.CostPriceRangeVO;
import com.cosfo.item.web.domain.vo.MarketItemVO;
import com.cosfo.item.web.domain.vo.ProvinceCityAreaVO;
import com.cosfo.item.web.domain.vo.SummerFarmCostPriceVO;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.strategy.CosfoMarketItemHandler;
import com.cosfo.item.web.mq.consumer.binlog.strategy.SummerFarmAreaSkuHandler;
import com.cosfo.item.web.mq.consumer.binlog.strategy.SummerFarmMajorPriceHandler;
import com.cosfo.item.web.provider.converter.CostPricePrividerConverter;
import com.cosfo.item.web.scheduler.InitCostPriceProcessor;
import com.cosfo.item.web.scheduler.InvalidCostPriceUpdateProcessor;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
public class CostPriceTest {

    @Autowired
    private CosfoMarketItemHandler cosfoMarketItemHandler;
    @Autowired
    private SummerFarmAreaSkuHandler summerFarmAreaSkuHandler;
    @Autowired
    private SummerFarmMajorPriceHandler summerFarmMajorPriceHandler;
    @Autowired
    private InitCostPriceProcessor initCostPriceProcessor;
    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;
    @Autowired
    private PriceProvider priceProvider;
    @Autowired
    private InvalidCostPriceUpdateProcessor invalidCostPriceUpdateProcessor;
    @Test
    void initCostPriceProcessor() {
        try {
            initCostPriceProcessor.processResult (null);
        } catch (Exception e) {
            System.out.println (e);
        }
    }
    @Test
    void testPrice(){
//        4672/4671
//        4672	2	12424	************	西湖区	浙江省	杭州市	1	2000-12-31 00:00:00	2050-12-31 00:00:00	1	0	2023-07-26 17:34:03	2023-07-26 17:34:03
        CostPriceDTO dto = new CostPriceDTO();
        dto.setSkuCode ("************");
        dto.setSkuId (12424L);
        dto.setArea ("西湖区");
        dto.setCity ("杭州市");
        dto.setProvince ("浙江省");
        dto.setTenantId (2L);
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        dto.setValidTime(LocalDateTime.parse("2000-12-31 00:00:00",df));
        dto.setInvalidTime(LocalDateTime.parse("2050-12-31 00:00:00",df));
        dto.setValidType(1);
        dto.setPrice(new BigDecimal (10));
        costPriceDomianService.saveOrUpdateByCityAreaAndTenantId(dto);
    }
    @Test
    void summerFarmAreaSkuHandler() {
//        DtsModelEvent dtsModelEvent = JSON.parseObject ("{\"id\":609715,\"database\":\"xianmudb\",\"table\":\"area_sku\",\"pkNames\":[\"id\"],\"isDdl\":false,\"type\":\"UPDATE\",\"es\":1679569021000,\"ts\":1679569021797,\"sql\":\"\",\"sqlType\":{\"id\":4,\"sku\":12,\"area_no\":4,\"quantity\":4,\"share\":-6,\"original_price\":3,\"price\":3,\"update_time\":93,\"on_sale\":-6,\"add_time\":93,\"priority\":4,\"pd_priority\":4,\"ladder_price\":12,\"limited_quantity\":4,\"sales_mode\":4,\"show\":4,\"info\":12,\"m_type\":4,\"show_advance\":-6,\"advance\":12,\"corner_status\":4,\"corner_open_time\":93,\"open_sale\":4,\"open_sale_time\":93,\"close_sale\":4,\"close_sale_time\":93,\"fix_flag\":4,\"fix_num\":4,\"updater\":12},\"mysqlType\":{\"id\":\"int(11)\",\"sku\":\"varchar(30)\",\"area_no\":\"int(11)\",\"quantity\":\"int(10)\",\"share\":\"tinyint(1)\",\"original_price\":\"decimal(10,2)\",\"price\":\"decimal(10,2)\",\"update_time\":\"datetime\",\"on_sale\":\"tinyint(1)\",\"add_time\":\"datetime\",\"priority\":\"int(11)\",\"pd_priority\":\"int(11)\",\"ladder_price\":\"varchar(500)\",\"limited_quantity\":\"int(11)\",\"sales_mode\":\"int(2)\",\"show\":\"int(11)\",\"info\":\"varchar(255)\",\"m_type\":\"int(2)\",\"show_advance\":\"tinyint(1)\",\"advance\":\"varchar(50)\",\"corner_status\":\"int(11)\",\"corner_open_time\":\"datetime\",\"open_sale\":\"int(11)\",\"open_sale_time\":\"datetime\",\"close_sale\":\"int(11)\",\"close_sale_time\":\"datetime\",\"fix_flag\":\"int(11)\",\"fix_num\":\"int(11)\",\"updater\":\"varchar(64)\"},\"data\":[{\"id\":\"2080\",\"sku\":\"19335603756\",\"area_no\":\"1001\",\"quantity\":\"0\",\"share\":\"1\",\"original_price\":null,\"price\":\"9.80\",\"update_time\":\"2022-08-25 15:59:59\",\"on_sale\":\"1\",\"add_time\":\"2020-08-25 15:06:29\",\"priority\":\"99\",\"pd_priority\":\"1\",\"ladder_price\":\"[]\",\"limited_quantity\":null,\"sales_mode\":\"0\",\"show\":\"1\",\"info\":null,\"m_type\":\"0\",\"show_advance\":\"0\",\"advance\":null,\"corner_status\":\"0\",\"corner_open_time\":null,\"open_sale\":\"0\",\"open_sale_time\":null,\"close_sale\":null,\"close_sale_time\":null,\"fix_flag\":\"0\",\"fix_num\":null,\"updater\":null}],\"old\":[{\"price\":\"9.70\"}]}",DtsModelEvent.class);
//        summerFarmAreaSkuHandler.tableDml (dtsModelEvent);

        DtsModelEvent dtsModelEvent = JSON.parseObject ("{\"id\":641895,\"database\":\"xianmudb\",\"table\":\"area_sku\",\"pkNames\":[\"id\"],\"isDdl\":false,\"type\":\"UPDATE\",\"es\":1680144599000,\"ts\":1680144599327,\"sql\":\"\",\"sqlType\":{\"id\":4,\"sku\":12,\"area_no\":4,\"quantity\":4,\"share\":-6,\"original_price\":3,\"price\":3,\"update_time\":93,\"on_sale\":-6,\"add_time\":93,\"priority\":4,\"pd_priority\":4,\"ladder_price\":12,\"limited_quantity\":4,\"sales_mode\":4,\"show\":4,\"info\":12,\"m_type\":4,\"show_advance\":-6,\"advance\":12,\"corner_status\":4,\"corner_open_time\":93,\"open_sale\":4,\"open_sale_time\":93,\"close_sale\":4,\"close_sale_time\":93,\"fix_flag\":4,\"fix_num\":4,\"updater\":12},\"mysqlType\":{\"id\":\"int(11)\",\"sku\":\"varchar(30)\",\"area_no\":\"int(11)\",\"quantity\":\"int(10)\",\"share\":\"tinyint(1)\",\"original_price\":\"decimal(10,2)\",\"price\":\"decimal(10,2)\",\"update_time\":\"datetime\",\"on_sale\":\"tinyint(1)\",\"add_time\":\"datetime\",\"priority\":\"int(11)\",\"pd_priority\":\"int(11)\",\"ladder_price\":\"varchar(500)\",\"limited_quantity\":\"int(11)\",\"sales_mode\":\"int(2)\",\"show\":\"int(11)\",\"info\":\"varchar(255)\",\"m_type\":\"int(2)\",\"show_advance\":\"tinyint(1)\",\"advance\":\"varchar(50)\",\"corner_status\":\"int(11)\",\"corner_open_time\":\"datetime\",\"open_sale\":\"int(11)\",\"open_sale_time\":\"datetime\",\"close_sale\":\"int(11)\",\"close_sale_time\":\"datetime\",\"fix_flag\":\"int(11)\",\"fix_num\":\"int(11)\",\"updater\":\"varchar(64)\"},\"data\":[{\"id\":\"61281\",\"sku\":\"6153867337\",\"area_no\":\"1001\",\"quantity\":\"0\",\"share\":\"1\",\"original_price\":null,\"price\":\"80.0\",\"update_time\":\"2023-03-30 10:49:59\",\"on_sale\":\"1\",\"add_time\":\"2023-03-28 13:46:19\",\"priority\":\"99\",\"pd_priority\":\"1\",\"ladder_price\":\"[]\",\"limited_quantity\":null,\"sales_mode\":\"0\",\"show\":\"1\",\"info\":\"\",\"m_type\":\"0\",\"show_advance\":\"0\",\"advance\":null,\"corner_status\":\"1\",\"corner_open_time\":null,\"open_sale\":\"3\",\"open_sale_time\":null,\"close_sale\":null,\"close_sale_time\":null,\"fix_flag\":\"0\",\"fix_num\":null,\"updater\":\"系统默认\"}],\"old\":[{\"price\":\"60.0\",\"update_time\":\"2023-03-29 16:23:54\"}]}",DtsModelEvent.class);
        summerFarmAreaSkuHandler.tableDml (dtsModelEvent);
    }
    @Test
    void costpricemax() {
        String  a  = "{\"addressReqs\":[{\"area\":\"上城区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"余杭区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"闵行区\",\"city\":\"上海市\",\"cityId\":310100,\"province\":\"上海市\"},{\"area\":\"雨花区\",\"city\":\"长沙市\",\"cityId\":430100,\"province\":\"湖南省\"},{\"area\":\"普陀区\",\"city\":\"上海市\",\"cityId\":310100,\"province\":\"上海市\"},{\"area\":\"南昌县\",\"city\":\"南昌市\",\"cityId\":360100,\"province\":\"江西省\"},{\"area\":\"拱墅区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"栾川县\",\"city\":\"洛阳市\",\"cityId\":410300,\"province\":\"河南省\"},{\"area\":\"茂南区\",\"city\":\"茂名市\",\"cityId\":440900,\"province\":\"广东省\"},{\"area\":\"青山湖区\",\"city\":\"南昌市\",\"cityId\":360100,\"province\":\"江西省\"},{\"area\":\"西区街道\",\"city\":\"中山市\",\"cityId\":442000,\"province\":\"广东省\"},{\"area\":\"虎丘区\",\"city\":\"苏州市\",\"cityId\":320500,\"province\":\"江苏省\"},{\"area\":\"南海区\",\"city\":\"佛山市\",\"cityId\":440600,\"province\":\"广东省\"},{\"area\":\"袁州区\",\"city\":\"宜春市\",\"cityId\":360900,\"province\":\"江西省\"},{\"area\":\"榕江县\",\"city\":\"黔东南苗族侗族自治州\",\"cityId\":522600,\"province\":\"贵州省\"},{\"area\":\"金堂县\",\"city\":\"成都市\",\"cityId\":510100,\"province\":\"四川省\"},{\"area\":\"金山区\",\"city\":\"上海市\",\"cityId\":310100,\"province\":\"上海市\"},{\"area\":\"德清县\",\"city\":\"湖州市\",\"cityId\":330500,\"province\":\"浙江省\"},{\"area\":\"浦东新区\",\"city\":\"上海市\",\"cityId\":310100,\"province\":\"上海市\"},{\"area\":\"宜秀区\",\"city\":\"安庆市\",\"cityId\":340800,\"province\":\"安徽省\"},{\"area\":\"铁西区\",\"city\":\"鞍山市\",\"cityId\":210300,\"province\":\"辽宁省\"},{\"area\":\"福田区\",\"city\":\"深圳市\",\"cityId\":440300,\"province\":\"广东省\"},{\"area\":\"溧水区\",\"city\":\"南京市\",\"cityId\":320100,\"province\":\"江苏省\"},{\"area\":\"义乌市\",\"city\":\"金华市\",\"cityId\":330700,\"province\":\"浙江省\"},{\"area\":\"江干区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"柯桥区\",\"city\":\"绍兴市\",\"cityId\":330600,\"province\":\"浙江省\"},{\"area\":\"章贡区\",\"city\":\"赣州市\",\"cityId\":360700,\"province\":\"江西省\"},{\"area\":\"东城区\",\"city\":\"北京市\",\"cityId\":110100,\"province\":\"北京市\"},{\"area\":\"宿城区\",\"city\":\"宿迁市\",\"cityId\":321300,\"province\":\"江苏省\"},{\"area\":\"晋江市\",\"city\":\"泉州市\",\"cityId\":350500,\"province\":\"福建省\"},{\"area\":\"鹿城区\",\"city\":\"温州市\",\"cityId\":330300,\"province\":\"浙江省\"},{\"area\":\"赤坎区\",\"city\":\"湛江市\",\"cityId\":440800,\"province\":\"广东省\"},{\"area\":\"高淳区\",\"city\":\"南京市\",\"cityId\":320100,\"province\":\"江苏省\"},{\"area\":\"顺德区\",\"city\":\"佛山市\",\"cityId\":440600,\"province\":\"广东省\"},{\"area\":\"崇川区\",\"city\":\"南通市\",\"cityId\":320600,\"province\":\"江苏省\"},{\"area\":\"洛江区\",\"city\":\"泉州市\",\"cityId\":350500,\"province\":\"福建省\"},{\"area\":\"涪城区\",\"city\":\"绵阳市\",\"cityId\":510700,\"province\":\"四川省\"},{\"area\":\"慈溪市\",\"city\":\"宁波市\",\"cityId\":330200,\"province\":\"浙江省\"},{\"area\":\"宜兴市\",\"city\":\"无锡市\",\"cityId\":320200,\"province\":\"江苏省\"},{\"area\":\"武侯区\",\"city\":\"成都市\",\"cityId\":510100,\"province\":\"四川省\"},{\"area\":\"南湖区\",\"city\":\"嘉兴市\",\"cityId\":330400,\"province\":\"浙江省\"},{\"area\":\"西湖区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"阆中市\",\"city\":\"南充市\",\"cityId\":511300,\"province\":\"四川省\"},{\"area\":\"巢湖市\",\"city\":\"合肥市\",\"cityId\":340100,\"province\":\"安徽省\"},{\"area\":\"成华区\",\"city\":\"成都市\",\"cityId\":510100,\"province\":\"四川省\"},{\"area\":\"李沧区\",\"city\":\"青岛市\",\"cityId\":370200,\"province\":\"山东省\"},{\"area\":\"黄埔区\",\"city\":\"广州市\",\"cityId\":440100,\"province\":\"广东省\"},{\"area\":\"嘉定区\",\"city\":\"上海市\",\"cityId\":310100,\"province\":\"上海市\"},{\"area\":\"信州区\",\"city\":\"上饶市\",\"cityId\":361100,\"province\":\"江西省\"},{\"area\":\"黄岛区\",\"city\":\"青岛市\",\"cityId\":370200,\"province\":\"山东省\"},{\"area\":\"岳麓区\",\"city\":\"长沙市\",\"cityId\":430100,\"province\":\"湖南省\"},{\"area\":\"海定区\",\"city\":\"嘉兴市\",\"cityId\":330400,\"province\":\"浙江省\"},{\"area\":\"三水区\",\"city\":\"佛山市\",\"cityId\":440600,\"province\":\"广东省\"},{\"area\":\"淳安县\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"临平区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"新城区\",\"city\":\"呼和浩特市\",\"cityId\":150100,\"province\":\"内蒙古自治区\"},{\"area\":\"东区街道\",\"city\":\"中山市\",\"cityId\":442000,\"province\":\"广东省\"},{\"area\":\"和平区\",\"city\":\"沈阳市\",\"cityId\":210100,\"province\":\"辽宁省\"},{\"area\":\"富阳区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"和县\",\"city\":\"马鞍山市\",\"cityId\":340500,\"province\":\"安徽省\"},{\"area\":\"渑池县\",\"city\":\"三门峡市\",\"cityId\":411200,\"province\":\"河南省\"},{\"area\":\"澄海区\",\"city\":\"汕头市\",\"cityId\":440500,\"province\":\"广东省\"},{\"area\":\"番禺区\",\"city\":\"广州市\",\"cityId\":440100,\"province\":\"广东省\"},{\"area\":\"莲都区\",\"city\":\"丽水市\",\"cityId\":331100,\"province\":\"浙江省\"},{\"area\":\"寮步镇\",\"city\":\"东莞市\",\"cityId\":441900,\"province\":\"广东省\"},{\"area\":\"\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"福清市\",\"city\":\"福州市\",\"cityId\":350100,\"province\":\"福建省\"},{\"area\":\"湖滨区\",\"city\":\"三门峡市\",\"cityId\":411200,\"province\":\"河南省\"},{\"area\":\"宝山区\",\"city\":\"上海市\",\"cityId\":310100,\"province\":\"上海市\"},{\"area\":\"朝阳区\",\"city\":\"北京市\",\"cityId\":110100,\"province\":\"北京市\"},{\"area\":\"姑苏区\",\"city\":\"苏州市\",\"cityId\":320500,\"province\":\"江苏省\"},{\"area\":\"广德市\",\"city\":\"宣城市\",\"cityId\":341800,\"province\":\"安徽省\"},{\"area\":\"金平区\",\"city\":\"汕头市\",\"cityId\":440500,\"province\":\"广东省\"},{\"area\":\"建德市\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"临安区\",\"city\":\"杭州市\",\"cityId\":330100,\"province\":\"浙江省\"},{\"area\":\"南城街道\",\"city\":\"东莞市\",\"cityId\":441900,\"province\":\"广东省\"},{\"area\":\"歙县\",\"city\":\"黄山市\",\"cityId\":341000,\"province\":\"安徽省\"},{\"area\":\"水磨沟区\",\"city\":\"乌鲁木齐市\",\"cityId\":650100,\"province\":\"新疆维吾尔自治区\"},{\"area\":\"西乡塘区\",\"city\":\"南宁市\",\"cityId\":450100,\"province\":\"广西壮族自治区\"},{\"area\":\"岳阳楼区\",\"city\":\"岳阳市\",\"cityId\":430600,\"province\":\"湖南省\"},{\"area\":\"中西区\",\"city\":\"香港特别行政区\",\"cityId\":810000,\"province\":\"香港特别行政区\"},{\"area\":\"油尖旺区\",\"city\":\"香港特别行政区\",\"cityId\":810000,\"province\":\"香港特别行政区\"},{\"area\":\"嘉模堂区\",\"city\":\"澳门特别行政区\",\"cityId\":820000,\"province\":\"澳门特别行政区\"},{\"area\":\"玄武区\",\"city\":\"南京市\",\"cityId\":320100,\"province\":\"江苏省\"},{\"area\":\"城关区\",\"city\":\"拉萨市\",\"cityId\":540100,\"province\":\"西藏自治区\"},{\"area\":\"深水埗区\",\"city\":\"香港特别行政区\",\"cityId\":810000,\"province\":\"香港特别行政区\"},{\"area\":\"仙居县\",\"city\":\"台州市\",\"cityId\":331000,\"province\":\"浙江省\"},{\"area\":\"美兰区\",\"city\":\"海口市\",\"cityId\":460100,\"province\":\"海南省\"},{\"area\":\"琼海市\",\"city\":\"琼海市\",\"cityId\":469002,\"province\":\"海南省\"},{\"area\":\"赛罕区\",\"city\":\"呼和浩特市\",\"cityId\":150100,\"province\":\"内蒙古自治区\"},{\"area\":\"沙依巴克区\",\"city\":\"乌鲁木齐市\",\"cityId\":650100,\"province\":\"新疆维吾尔自治区\"},{\"area\":\"长寿区\",\"city\":\"重庆市\",\"cityId\":500100,\"province\":\"重庆市\"},{\"area\":\"湾仔区\",\"city\":\"香港特别行政区\",\"cityId\":810000,\"province\":\"香港特别行政区\"},{\"area\":\"禅城区\",\"city\":\"佛山市\",\"cityId\":440600,\"province\":\"广东省\"},{\"area\":\"渝北区\",\"city\":\"重庆市\",\"cityId\":500100,\"province\":\"重庆市\"},{\"area\":\"沙坪坝区\",\"city\":\"重庆市\",\"cityId\":500100,\"province\":\"重庆市\"},{\"area\":\"南安市\",\"city\":\"泉州市\",\"cityId\":350500,\"province\":\"福建省\"}],\"skuId\":23358,\"tenantId\":2}";
        MaxCostPriceQueryReq maxCostPriceQueryReq = JSON.parseObject (a, MaxCostPriceQueryReq.class);
        priceProvider.queryMaxCostPrice (maxCostPriceQueryReq);
    }
    @Test
    void queryCostPriceRange() {

        CostPriceQueryRangeReq req = new CostPriceQueryRangeReq ();
        req.setSkuId(70L);
        req.setCitys(Arrays.asList ("杭州市","北京市","安庆市"));
        CostPriceQueryRangeReq req1 = new CostPriceQueryRangeReq ();
        req1.setSkuId(99L);
        req1.setCitys(Arrays.asList ("杭州市"));
        List<CostPriceQueryRangeReq> list = Arrays.asList  (req,req1);
        Long tenantId = 2L;
        List<CostPriceRangeVO> costPriceRangeVOS = costPriceDomianService.queryCostPriceRange(list.stream ().map (CostPricePrividerConverter::costPriceQueryRangeReq2DTO).collect(Collectors.toList()),tenantId);
        List<CostPriceRangeResultResp> result = costPriceRangeVOS.stream().map(e -> CostPricePrividerConverter.costPriceRangeVO2Resp(e)).collect(Collectors.toList());
        System.out.println (result);
    }
    @Test
    void compareCity() {
//        //查询品牌方覆盖的省市区
//        List<String> areasFT = tenantFacade.listAddress (2L);
//        if(CollectionUtil.isEmpty(areasFT)){
//            return;
//        }
//
//        //查询运营区域
//        Map<Integer, List<ProvinceCityAreaVO>> areaNoMap = summerfarmMallFacade.listAreaNoByAddressString (areasFT);
//        System.out.println (areaNoMap);
//
//
//
//
//        //查询运营区域覆盖的省市区
//        List<ProvinceCityAreaVO> citys = summerfarmMallFacade.getAddressInfoByAreaNo (1001);
//        if (CollectionUtil.isEmpty (citys)) {
//            return;
//        }
//        Set<String> areasXMc = citys.stream ().filter (e-> ObjectUtil.isNull (e.getArea ())).map (ProvinceCityAreaVO::getCity).collect (Collectors.toSet ());
//        Set<String> areasXMca = citys.stream ().filter (e->ObjectUtil.isNotNull (e.getArea ())).map (e ->e.getCity () + "-" + e.getArea ()).collect (Collectors.toSet ());
//
//
//        Map<Long, Set<String>> resultMap = new LinkedHashMap<> ();
//        //查询品牌方覆盖的省市区
//        List<String> addresses = tenantFacade.listAddress (2L);
//        Set<String> addressResult = addressService.filterAddress (addresses,areasXMca,areasXMc);
//        if(CollectionUtil.isNotEmpty (addressResult)) {
//            resultMap.put (2L, addressResult);
//        }
//        System.out.println (resultMap);

        SummerFarmCostPriceVO summerFarmCostPriceVO = sfMallManageFacade.queryMallPriceInfo4Saas(1001,"6153867337",880L);
        System.out.println (summerFarmCostPriceVO);
        try {
            invalidCostPriceUpdateProcessor.processResult (null);
        } catch (Exception e) {
            throw new RuntimeException (e);
        }

    }
    @Test
    void invalidCostPriceUpdateProcessor() {
        try {
            invalidCostPriceUpdateProcessor.processResult (null);
        } catch (Exception e) {
            throw new RuntimeException (e);
        }
    }
}
