package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


public interface MarketItemPriceEnum {
    @Getter
    @AllArgsConstructor
    public enum TargetTypeEnum {

        TENANT(0, "品牌方"),

        STORE(1, "门店"),

        AREA_NO(2, "运营区域"),

        STORE_GROUP(3, "门店分组"),

        ;
        private Integer code;

        /**
         * 描述
         */
        private String desc;
    }
}
