package com.cosfo.item.web.domain.converter;

import com.cofso.item.client.req.MarketItemWithClassificationQueryReq;
import com.cofso.item.client.req.PageMarketItemQueryReq;
import com.cosfo.item.infrastructure.item.dto.MarketItemPageQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketItemWithClassificationQueryParam;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ItemQueryConverter {

    ItemQueryConverter INSTANCE = Mappers.getMapper(ItemQueryConverter.class);

    MarketItemWithClassificationQueryParam reqToParam(MarketItemWithClassificationQueryReq req);

    @Mapping(source = "pageIndex",target = "pageNum")
    MarketItemPageQueryParam req2Param(PageMarketItemQueryReq req);
}
