package com.cosfo.item.infrastructure.price.dao.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.price.model.FutureValidCostPrice;
import com.cosfo.item.infrastructure.price.model.MarketAreaItemMapping;
import com.cosfo.item.infrastructure.price.mapper.MarketAreaItemMappingMapper;
import com.cosfo.item.infrastructure.price.dao.MarketAreaItemMappingDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 价格映射配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Service
public class MarketAreaItemMappingDaoImpl extends ServiceImpl<MarketAreaItemMappingMapper, MarketAreaItemMapping> implements MarketAreaItemMappingDao {

    @Override
    public List<MarketAreaItemMapping> listByAreaItemIds(Set<Long> areaItemIds) {
        LambdaQueryWrapper<MarketAreaItemMapping> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MarketAreaItemMapping::getAreaItemId,areaItemIds);
        return list(wrapper);
    }
}
