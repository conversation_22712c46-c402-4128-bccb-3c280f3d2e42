package com.cosfo.item.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.common.result.ResultDTOEnum;
import com.cosfo.item.common.utils.RpcResponseUtil;
import com.cosfo.item.web.domain.converter.ProductDomainConvert;
import com.cosfo.item.web.domain.dto.ProductQueryDTO;
import com.cosfo.item.web.domain.vo.ProductAgentSkuMappingVO;
import com.cosfo.item.web.domain.vo.ProductPricingSupplyCityMappingVO;
import com.cosfo.item.web.domain.vo.ProductSkuVO;
import com.cosfo.item.web.facade.converter.ProductConvert;
import com.cosfo.manage.client.product.ProductProvider;
import com.cosfo.manage.client.product.req.QuerySupplyReq;
import com.cosfo.manage.client.product.req.UpdateAssociatedReq;
import com.cosfo.manage.client.product.resp.ProductPricingSupplyCityMappingResp;
import net.summerfarm.goods.client.provider.ProductsMappingQueryProvider;
import net.summerfarm.goods.client.provider.ProductsSkuQueryProvider;
import net.summerfarm.goods.client.req.ProductMappingQueryReq;
import net.summerfarm.goods.client.resp.ProductSkuDetailResp;
import net.summerfarm.goods.client.resp.ProductsMappingResp;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: monna.chen
 * @Date: 2023/5/5 13:53
 * @Description:
 */
@Service
public class ProductFacade {

    @DubboReference
    private ProductsMappingQueryProvider productsMappingQueryProvider;
    @DubboReference
    private ProductsSkuQueryProvider productsSkuQueryProvider;
    @DubboReference
    private ProductProvider productProvider;


    public List<ProductAgentSkuMappingVO> listProductMappingByAgentSkuCode(String agentSkuCode, Long agentTenantId, Long tenantId) {
        if(ObjectUtil.isEmpty(agentSkuCode) || ObjectUtil.isEmpty(agentTenantId) || ObjectUtil.isEmpty(tenantId)){
            return Collections.emptyList();
        }
        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setAgentTenantId(agentTenantId);
        queryReq.setSkuList(Collections.singletonList(agentSkuCode));
        List<ProductsMappingResp> list = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
        return list.stream().map(ProductDomainConvert.INSTANCE::convert2VO).collect(Collectors.toList());
    }

    public List<ProductAgentSkuMappingVO> listProductMappingByAgentSkuCodes(Set<String> agentSkuCodes, Long agentTenantId, Long tenantId) {
        if(CollectionUtils.isEmpty(agentSkuCodes) || ObjectUtil.isEmpty(agentTenantId) || ObjectUtil.isEmpty(tenantId)){
            return Collections.emptyList();
        }
        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setAgentTenantId(agentTenantId);
        queryReq.setSkuList(new ArrayList<>(agentSkuCodes));
        List<ProductsMappingResp> list = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
        return list.stream().map(ProductDomainConvert.INSTANCE::convert2VO).collect(Collectors.toList());
    }

    /**
     * 根据skuid 和 供应商id 查询货品信息
     * @param skuId
     * @param agentTenantId
     * @return
     */
    public ProductAgentSkuMappingVO getProductMappingBySkuIdAndTenantId(Long skuId, Long agentTenantId,Long tenantId) {
        if(ObjectUtil.isEmpty(skuId) || ObjectUtil.isEmpty(agentTenantId) || ObjectUtil.isEmpty(tenantId)){
            return null;
        }
        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setTenantId(tenantId);
        queryReq.setAgentTenantId(agentTenantId);
        queryReq.setSkuIds(Collections.singletonList(skuId));
        List<ProductsMappingResp> mapping = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));

        if (CollectionUtils.isEmpty(mapping)){
            return null;
        }
        return ProductDomainConvert.INSTANCE.convert2VO(mapping.get(0));
    }

    /**
     * 根据skuid 和 tenant 查询货品信息
     * @param skuIds
     * @param tenantId
     * @return
     */
    public List<ProductAgentSkuMappingVO> listProductMappingBySkuIdsAndTenantId(Set<Long> skuIdSet,Long tenantId) {
        if(CollectionUtils.isEmpty(skuIdSet) || ObjectUtil.isEmpty(tenantId)){
            return Collections.emptyList();
        }

        ProductMappingQueryReq queryReq = new ProductMappingQueryReq();
        queryReq.setTenantId(tenantId);
        ArrayList<Long> skuIds = new ArrayList<> (skuIdSet);
        List<List<Long>> split = ListUtil.split (skuIds, 500);
        List<ProductAgentSkuMappingVO> result = new ArrayList<> ();
        split.forEach (skus->{
            queryReq.setSkuIds(skus);
            List<ProductsMappingResp> list = RpcResponseUtil.handler(productsMappingQueryProvider.selectMappingList(queryReq));
            List<ProductAgentSkuMappingVO> collect = list.stream ().map (ProductDomainConvert.INSTANCE::convert2VO).collect (Collectors.toList ());
            if(CollectionUtil.isNotEmpty (collect)){
                result.addAll (collect);
            }
        });
        return result;
    }

    /**
     * 根据skuids 和 供应商id 查询货品信息
     * @param skuIds
     * @param agentTenantId
     * @return
     */
    public List<ProductAgentSkuMappingVO> listProductMappingBySkuIdsAndAgentTenantId(Set<Long> skuIdSet, Long agentTenantId) {
        if (CollectionUtils.isEmpty (skuIdSet) || ObjectUtil.isEmpty (agentTenantId)) {
            return Collections.emptyList ();
        }
        ProductMappingQueryReq queryReq = new ProductMappingQueryReq ();
        queryReq.setAgentTenantId (agentTenantId);
        ArrayList<Long> skuIds = new ArrayList<> (skuIdSet);
        List<List<Long>> split = ListUtil.split (skuIds, 500);
        List<ProductAgentSkuMappingVO> result = new ArrayList<> ();
        split.forEach (skus -> {
            queryReq.setSkuIds (skus);
            List<ProductsMappingResp> list = RpcResponseUtil.handler (productsMappingQueryProvider.selectMappingList (queryReq));
            List<ProductAgentSkuMappingVO> collect = list.stream ().map (ProductDomainConvert.INSTANCE::convert2VO).collect (Collectors.toList ());
            if (CollectionUtil.isNotEmpty (collect)) {
                result.addAll (collect);
            }
        });
        return result;
    }



    /**
     * 查询sku信息
     *
     * @param skuId
     * @return
     */
    public ProductSkuVO querySkuInfo(Long skuId) {
        if (Objects.isNull(skuId)) {
            throw new ParamsException(ResultDTOEnum.PARAMETER_MISSING.getMessage(),ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.name());
        }

        List<ProductSkuDetailResp> skuDetailResps = RpcResponseUtil.handler(productsSkuQueryProvider.selectProductSkuDetailById(Collections.singletonList(skuId)));
        if (CollectionUtils.isEmpty(skuDetailResps)){
            return null;
        }
        return ProductConvert.INSTANCE.convert2SkuVO(skuDetailResps.get(0));
    }

    /**
     * 更新是否关联商品字段（自营货品）
     *
     * @param skuId
     * @param tenantId
     * @param associated
     */
    public void updateAssociated(Long skuId, Long tenantId, Integer associated) {
        if (Objects.isNull(skuId) || Objects.isNull(tenantId) || Objects.isNull(associated)) {
            throw new ParamsException(ResultDTOEnum.PARAMETER_MISSING.getMessage(),ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.name());
        }
        productProvider.updateAssociated(UpdateAssociatedReq.builder()
            .skuId(skuId)
            .tenantId(tenantId)
            .associated(associated)
            .build());
    }

    /**
     * 更新是否关联商品字段（报价货品）
     *
     * @param skuId
     * @param tenantId
     * @param associated
     */
    public void updateSupplyAssociated(Long skuId, Long tenantId, Integer associated) {
        if (Objects.isNull(skuId) || Objects.isNull(tenantId) || Objects.isNull(associated)) {
            throw new ParamsException(ResultDTOEnum.PARAMETER_MISSING.getMessage(),ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.name());
        }
        productProvider.updateSupplyAssociated(UpdateAssociatedReq.builder()
            .skuId(skuId)
            .tenantId(tenantId)
            .associated(associated)
            .build());
    }

    /**
     * 查询供应城市
     *
     * @param skuIds
     * @param tenantId
     * @return
     */
    public List<ProductPricingSupplyCityMappingVO> querySupplyCityBySkuId(List<Long> skuIds, Long tenantId) {
        if (CollectionUtils.isEmpty(skuIds) || Objects.isNull(tenantId)) {
            throw new ParamsException(ResultDTOEnum.PARAMETER_MISSING.getMessage(),ResultDTOEnum.PARAMETER_MISSING.getCode(), ResultDTOEnum.PARAMETER_MISSING.name());
        }
        DubboResponse<List<ProductPricingSupplyCityMappingResp>> response = productProvider.querySupplyCityBySkuId(QuerySupplyReq.builder()
            .skuIds(skuIds)
            .tenantId(tenantId)
            .build());
        if (!response.isSuccess()) {
            throw new BizException(response.getMsg());
        }
        return ProductConvert.INSTANCE.convert2VOs(response.getData());
    }

}
