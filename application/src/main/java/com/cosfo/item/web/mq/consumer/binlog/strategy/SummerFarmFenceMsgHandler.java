package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.lang.Pair;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.*;

@Slf4j
@Component
public class SummerFarmFenceMsgHandler implements DbTableDmlStrategy {

    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;

    @Override
    public String getTableDmlName() {
        return DBTableName.SummerfarmTable.FENCE;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals (dtsModelEvent.getType (), BinlogEventEnum.Status.INSERT.name ())) {
            dtsModelEvent.consumerData (map -> {
                Integer areaNo = Integer.parseInt (map.get ("area_no"));
                summerfarmMallFacade.invalidateAreaCache (areaNo);
            });
        } else if (Objects.equals (dtsModelEvent.getType (), BinlogEventEnum.Status.UPDATE.name ())) {
            //todo 不准确，可能修改的是围栏状态 此时拿不到areano，后续改成根据围栏id查询areanolist来失效
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                Integer areaNo = Integer.valueOf (dataMap.get ("area_no"));
                summerfarmMallFacade.invalidateAreaCache (areaNo);
                Map<String, String> oldMap = pair.getValue ();
                if (oldMap.containsKey ("area_no") ) {
                    summerfarmMallFacade.invalidateAreaCache (Integer.valueOf (oldMap.get ("area_no")));
                }
            }
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.DELETE.name())) {
        List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getOnlyOldData(dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> oldMap = pair.getValue ();
                summerfarmMallFacade.invalidateAreaCache (Integer.valueOf (oldMap.get ("area_no")));
            }
        }
    }
}
