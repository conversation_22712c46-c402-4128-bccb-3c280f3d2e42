package com.cosfo.item.infrastructure.price.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CostPriceQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 货品主键
     */
    private List<Long> skuIds;

    /**
     * 货品编码
     */
    private List<String> skuCodes;

    /**
     * 区域
     */
    private String area;

    /**
     * 城市
     */
    private String city;


    /**
     * 省
     */
    private String province;

    /**
     * 是否生效 true=生效
     */
    private Boolean validFlag;

    /**
     * 价格来源
     */
    private Integer sysType;
}
