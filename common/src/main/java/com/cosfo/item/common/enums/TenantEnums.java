package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
public interface TenantEnums {

    @Getter
    @AllArgsConstructor
    enum type {
        /**
         * 品牌方
         */
        BRAND_PARTY(0, "品牌方"),

        /**
         * 供应商
         */
        SUPPLIER(1, "供应商"),

        /**
         * 帆台
         */
        FANTAI(2, "帆台");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;
    }
}
