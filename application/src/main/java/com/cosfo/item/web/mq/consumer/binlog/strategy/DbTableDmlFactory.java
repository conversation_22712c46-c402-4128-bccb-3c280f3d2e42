package com.cosfo.item.web.mq.consumer.binlog.strategy;

import com.cosfo.item.starter.config.SpringContextUtil;
import com.cosfo.item.web.mq.consumer.binlog.strategy.DbTableDmlStrategy;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DbTableDmlFactory implements ApplicationListener<ContextRefreshedEvent> {

    /**
     * 获取实现
     */
    private static Collection<DbTableDmlStrategy> dbTableDmlList;

    /**
     * 通过注册好的表名,获取实现类对象
     *
     * @param dbTableNameEnum 表名
     * @return 实现类对象
     */
    public DbTableDmlStrategy creator(String dbTableNameEnum) {
        if (CollectionUtils.isEmpty(dbTableDmlList)) {
            dbTableDmlList = SpringContextUtil.getApplicationContext()
                    .getBeansOfType(DbTableDmlStrategy.class)
                    .values();
        }
        DbTableDmlStrategy dbTableDml = dbTableDmlList.stream().filter(x -> Objects.equals(x.getTableDmlName(), dbTableNameEnum))
                .findFirst()
                .orElse(null);
        return dbTableDml;
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        //防止重复执行。
        if (event.getApplicationContext().getParent() == null) {
            dbTableDmlList = SpringContextUtil.getApplicationContext()
                    .getBeansOfType(DbTableDmlStrategy.class)
                    .values();
        }
    }
}