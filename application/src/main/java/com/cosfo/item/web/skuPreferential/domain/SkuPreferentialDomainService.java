package com.cosfo.item.web.skuPreferential.domain;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.page.PageResp;
import com.cofso.preferential.client.enums.ProductSkuPreferentialStatusEnum;
import com.cofso.preferential.client.req.OptAvailableQuantityReq;
import com.cofso.preferential.client.req.ProductPreferentialCostPriceCommandReq;
import com.cofso.preferential.client.req.ProductSkuPreferentialDeleteReq;
import com.cofso.preferential.client.req.ProductSkuPreferentialQueryReq;
import com.cofso.preferential.client.resp.ProductSkuCityPreferentialCostPriceResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialBasicResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceRangeResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialPageResp;
import com.cosfo.item.common.constants.NumberConstants;
import com.cosfo.item.common.skuPreferential.constants.RedisKeyConstants;
import com.cosfo.item.common.skuPreferential.dto.ProductSkuPreferentialBasicDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.infrastructure.skuPreferential.dao.ProductSkuPreferentialCostPriceDao;
import com.cosfo.item.infrastructure.skuPreferential.dao.ProductSkuPreferentialCostPriceMappingDao;
import com.cosfo.item.infrastructure.skuPreferential.dao.ProductSkuPreferentialCostPriceOccupyDao;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPrice;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceMapping;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPriceOccupy;
import com.cosfo.item.infrastructure.skuPreferential.param.ProductSkuPreferentialQueryParam;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.skuPreferential.converter.SkuPreferentialConverter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SkuPreferentialDomainService {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ProductSkuPreferentialCostPriceDao preferentialCostPriceDao;

    @Resource
    private ProductSkuPreferentialCostPriceMappingDao preferentialCostPriceMappingDao;

    @Resource
    private ProductSkuPreferentialCostPriceOccupyDao occupyDao;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Autowired
    private TenantFacade tenantFacade;
    /**
     * 分页查询租户省心订列表
     *
     * @param queryReq
     * @return
     */
    public PageResp<ProductSkuPreferentialPageResp> pagePreferentialCostPrice(ProductSkuPreferentialQueryReq queryReq) {
        ProductSkuPreferentialQueryParam queryParam = SkuPreferentialConverter.INSTANCE.reqToParam(queryReq);
        ProductSkuPreferentialQueryParam param = new ProductSkuPreferentialQueryParam();
        Optional.ofNullable(queryReq.getStatusEnum()).ifPresent(enums -> {
            if (Objects.nonNull(enums.getAvailableFlag())) {
                param.setAvailableFlag(enums.getAvailableFlag());
                queryParam.setAvailableFlag(enums.getAvailableFlag());
            }
            if (ProductSkuPreferentialStatusEnum.WAIT_VALID == enums) {
                param.setWaitValid(Boolean.TRUE);
                queryParam.setWaitValid(Boolean.TRUE);
            }
        });
        Page<ProductSkuPreferentialCostPrice> pageInfo = preferentialCostPriceDao.pagePreferentialCostPriceByParam(queryParam);

        if (CollectionUtils.isEmpty(pageInfo.getRecords())) {
            return PageResp.emptyPage(queryReq.getPageIndex(), queryReq.getPageSize());
        }

        List<Long> skuIds = pageInfo.getRecords().stream().map(ProductSkuPreferentialCostPrice::getSkuId).collect(Collectors.toList());

        // 查询sku下所有的报价信息
        param.setSkuIds(skuIds);
        param.setTenantId(queryReq.getTenantId());
        List<ProductSkuPreferentialCostPrice> costPriceList = preferentialCostPriceDao.listPreferentialCostPriceByParam(param);
        List<Long> skuPreferentialIds = costPriceList.stream().map(ProductSkuPreferentialCostPrice::getId).collect(Collectors.toList());
        Map<Long, List<ProductSkuPreferentialCostPrice>> skuPriceMap = costPriceList.stream().collect(Collectors.groupingBy(ProductSkuPreferentialCostPrice::getSkuId));

        List<ProductSkuPreferentialCostPriceMapping> mappingList = preferentialCostPriceMappingDao.listByCostPriceIds(queryReq.getTenantId(), skuPreferentialIds);
        Map<Long, List<ProductSkuPreferentialCostPriceMapping>> mappingMap = mappingList.stream().collect(Collectors.groupingBy(ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId));


        List<ProductSkuPreferentialPageResp> costPriceResps = pageInfo.getRecords().stream().map(costPrice -> {
            ProductSkuPreferentialPageResp costPriceResp = new ProductSkuPreferentialPageResp();
            costPriceResp.setSkuId(costPrice.getSkuId());
            costPriceResp.setTenantId(costPrice.getTenantId());
            // 组装sku下所有省心订配置
            List<ProductSkuPreferentialCostPrice> prices = skuPriceMap.get(costPrice.getSkuId());
            if (CollectionUtils.isNotEmpty(prices)) {
                costPriceResp.setStartTime(prices.get(NumberConstants.ZERO).getStartTime());
                costPriceResp.setEndTime(prices.get(NumberConstants.ZERO).getEndTime());
                List<ProductSkuPreferentialCostPriceResp> costPriceRespList = SkuPreferentialConverter.INSTANCE.entityListToSkuPreferentialCostPriceList(prices);
                for (ProductSkuPreferentialCostPriceResp priceResp : costPriceRespList) {
                    // 组装配置下城市ids
                    List<ProductSkuPreferentialCostPriceMapping> costPriceMappingList = mappingMap.get(priceResp.getId());
                    List<Long> cityIds = Collections.EMPTY_LIST;
                    if (CollectionUtils.isNotEmpty(costPriceMappingList)) {
                        cityIds = costPriceMappingList.stream().map(ProductSkuPreferentialCostPriceMapping::getCityId).collect(Collectors.toList());
                    }
                    priceResp.setCityIds(cityIds);
                }
                costPriceResp.setCostPriceResps(costPriceRespList);
            }
            return costPriceResp;
        }).collect(Collectors.toList());

        return PageResp.toPageList(costPriceResps,
                Math.toIntExact(pageInfo.getTotal()), queryReq.getPageIndex(), queryReq.getPageSize());
    }


    /**
     * map<CityId,dto>
     * @param tenantId
     * @param skuId
     * @return
     */
    public Map<Long, ProductSkuCityPreferentialCostPriceResp> queryCityPreferentialCostPriceMap(Long tenantId, Long skuId, List<Long> cityIds) {
        //查询省心定开关
        if (tenantFacade.getSaveWorrySwitchByTenantId (tenantId)) {
            List<ProductSkuCityPreferentialCostPriceResp> preferentialCostPriceList = queryCityPreferentialCostPrice (tenantId, skuId, cityIds).stream().filter (e->ObjectUtil.isNotNull (e.getPrice ())).collect(Collectors.toList());
            if (!CollectionUtil.isEmpty (preferentialCostPriceList)) {
                return preferentialCostPriceList.stream ().collect (Collectors.toMap (ProductSkuCityPreferentialCostPriceResp::getCityId, Function.identity ()));
            }
        }
        return Collections.emptyMap ();
    }
    public List<ProductSkuCityPreferentialCostPriceResp> queryCityPreferentialCostPrice(Long tenantId, Long skuId, List<Long> cityIds) {
        List<ProductSkuPreferentialCostPriceMapping> costPriceMappingList = preferentialCostPriceMappingDao.queryBySkuIdAndCityIds(tenantId, skuId, cityIds);
        Map<Long, Long> idMap;
        Map<Long, ProductSkuPreferentialCostPrice> costPriceMap;
        if (CollectionUtils.isNotEmpty(costPriceMappingList)) {
            idMap = costPriceMappingList.stream().collect(Collectors.toMap(ProductSkuPreferentialCostPriceMapping::getCityId, ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId));
            ProductSkuPreferentialQueryParam param = new ProductSkuPreferentialQueryParam();
            param.setSkuIds(Collections.singletonList(skuId));
            param.setTenantId(tenantId);
            param.setAvailableFlag(true);
            param.setIds(costPriceMappingList.stream().map(ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId).collect(Collectors.toSet()));
            List<ProductSkuPreferentialCostPrice> preferentialCostPrices = preferentialCostPriceDao.listPreferentialCostPriceByParam(param);
            costPriceMap = preferentialCostPrices.stream().collect(Collectors.toMap(ProductSkuPreferentialCostPrice::getId, Function.identity()));
        } else {
            costPriceMap = Collections.emptyMap();
            idMap = Collections.emptyMap();
        }

        return cityIds.stream().map(cityId -> {
            ProductSkuCityPreferentialCostPriceResp resp = new ProductSkuCityPreferentialCostPriceResp();
            Long priceId = idMap.get(cityId);
            if (ObjectUtil.isNotEmpty(priceId)) {
                ProductSkuPreferentialCostPrice costPrice = costPriceMap.get(priceId);
                if (ObjectUtil.isNotEmpty(costPrice)) {
                    resp.setPrice(costPrice.getPrice());
                    resp.setStartTime(costPrice.getStartTime());
                    resp.setEndTime(costPrice.getEndTime());
                    resp.setAvailableQuantity (costPrice.getAvailableQuantity ());
                    resp.setSkuId (costPrice.getSkuId ());
                    resp.setProductSkuPreferentialCostPriceId (priceId);
                }
            }
            resp.setCityId(cityId);
            resp.setSkuId (skuId);
            return resp;
        }).collect(Collectors.toList());
    }

    public List<ProductSkuCityPreferentialCostPriceResp> queryPreferentialCostPriceBySkuIds4City(Long tenantId, Long cityId, Set<Long> skuIds) {
        Map<Long, ProductSkuPreferentialCostPrice> costPriceMap = getSkuPreferentialCostPriceMap (tenantId, cityId, skuIds);
        return skuIds.stream().map(skuId -> {
            ProductSkuCityPreferentialCostPriceResp resp = new ProductSkuCityPreferentialCostPriceResp();
            ProductSkuPreferentialCostPrice costPrice = costPriceMap.get (skuId);
            if (ObjectUtil.isNotEmpty(costPrice)) {
                resp.setPrice(costPrice.getPrice());
                resp.setStartTime(costPrice.getStartTime());
                resp.setEndTime(costPrice.getEndTime());
                resp.setAvailableQuantity (costPrice.getAvailableQuantity ());
                resp.setSkuId (costPrice.getSkuId ());
                resp.setProductSkuPreferentialCostPriceId (costPrice.getId ());
            }
            resp.setCityId(cityId);
            resp.setSkuId (skuId);
            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * map<skuId,ProductSkuPreferentialCostPrice>
     * @param tenantId
     * @param cityId
     * @param skuIds
     * @return
     */
    private Map<Long, ProductSkuPreferentialCostPrice> getSkuPreferentialCostPriceMap(Long tenantId, Long cityId, Set<Long> skuIds) {
        List<ProductSkuPreferentialCostPriceMapping> costPriceMappingList = preferentialCostPriceMappingDao.queryBySkuIdsAndCityId(tenantId, cityId, skuIds);
        Map<Long, ProductSkuPreferentialCostPrice> costPriceMap;
        if (CollectionUtils.isNotEmpty(costPriceMappingList)) {
            ProductSkuPreferentialQueryParam param = new ProductSkuPreferentialQueryParam();
            param.setSkuIds(new ArrayList<> (skuIds));
            param.setTenantId(tenantId);
            param.setAvailableFlag(true);
            param.setIds(costPriceMappingList.stream().map(ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId).collect(Collectors.toSet()));
            List<ProductSkuPreferentialCostPrice> preferentialCostPrices = preferentialCostPriceDao.listPreferentialCostPriceByParam(param);
            costPriceMap = preferentialCostPrices.stream().collect(Collectors.toMap(ProductSkuPreferentialCostPrice::getSkuId, Function.identity()));
        } else {
            costPriceMap = Collections.emptyMap();
        }
        return costPriceMap;
    }


    public List<ProductSkuPreferentialCostPriceRangeResp> queryPreferentialCostPriceRange(Long tenantId, Map<Long,List<Long>> skuIdCityIdMap) {
        ProductSkuPreferentialQueryParam param = new ProductSkuPreferentialQueryParam();
        param.setSkuIds(new ArrayList<> (skuIdCityIdMap.keySet ()));
        param.setTenantId(tenantId);
        param.setAvailableFlag(true);
        List<ProductSkuPreferentialCostPrice> preferentialCostPrices = preferentialCostPriceDao.listPreferentialCostPriceByParam(param);
        Map<Long, List<ProductSkuPreferentialCostPriceMapping>> mappingMap;
        if(CollectionUtils.isNotEmpty (preferentialCostPrices)){
            List<ProductSkuPreferentialCostPriceMapping> mappingList = preferentialCostPriceMappingDao.listByCostPriceIds(tenantId,preferentialCostPrices.stream ().map (ProductSkuPreferentialCostPrice::getId).collect(Collectors.toList()));
            mappingMap = mappingList.stream().collect(Collectors.groupingBy(ProductSkuPreferentialCostPriceMapping::getSkuId));
        } else {
            mappingMap = Collections.emptyMap ();
        }

        List<ProductSkuPreferentialCostPriceRangeResp> respList = new ArrayList<> (skuIdCityIdMap.size ());
        skuIdCityIdMap.forEach ((skuId,cityIds)->{
            ProductSkuPreferentialCostPriceRangeResp resp = new ProductSkuPreferentialCostPriceRangeResp ();
            resp.setSkuId (skuId);
            List<ProductSkuPreferentialCostPriceMapping> mapping = mappingMap.get (skuId);
            List<ProductSkuPreferentialCostPrice> costPriceList = Collections.emptyList ();
            if(CollectionUtils.isNotEmpty (mapping)){
                if(CollectionUtils.isNotEmpty (cityIds)) {
                    Set<Long> costPriceIds = mapping.stream ().filter (m -> cityIds.contains (m.getCityId ())).collect (Collectors.toList ()).stream ().map (ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId).collect (Collectors.toSet ());
                    if(CollectionUtils.isNotEmpty (costPriceIds)) {
                        costPriceList = preferentialCostPrices.stream ().filter (c -> costPriceIds.contains (c.getId ())).collect (Collectors.toList ());
                    }
                }else{
                    costPriceList = preferentialCostPrices.stream ().filter (c -> Objects.equals (c.getSkuId (), skuId)).collect (Collectors.toList ());
                }
            }
            if(CollectionUtils.isNotEmpty (costPriceList)){
                resp.setMaxPrice (costPriceList.stream().map(ProductSkuPreferentialCostPrice::getPrice).max(Comparator.comparing(x -> x)).orElse(null));
                resp.setMinPrice (costPriceList.stream().map(ProductSkuPreferentialCostPrice::getPrice).min(Comparator.comparing(x -> x)).orElse(null));
            }
            respList.add (resp);
        });

        return respList;
    }

    public ProductSkuPreferentialBasicResp queryBasicData(ProductSkuPreferentialQueryReq queryReq) {
        if (Objects.isNull(queryReq.getTenantId())) {
            throw new ParamsException("tenantIds不能为空");
        }
        Boolean availableFlag = null;
        Boolean waitValid = null;
        if (Objects.nonNull(queryReq.getStatusEnum())) {
            availableFlag = queryReq.getStatusEnum().getAvailableFlag();
            if (ProductSkuPreferentialStatusEnum.WAIT_VALID == queryReq.getStatusEnum()) {
                waitValid = true;
            }
        }
        ProductSkuPreferentialBasicDTO basicDTO = preferentialCostPriceDao.queryBasicData(queryReq.getTenantId(), queryReq.getSkuIds(), availableFlag, waitValid);
        return SkuPreferentialConverter.INSTANCE.basicDtoToBasicResp(basicDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteSkuPreferentialCostPrice(ProductSkuPreferentialDeleteReq deleteReq) {
        ProductSkuPreferentialQueryParam param = new ProductSkuPreferentialQueryParam();
        param.setTenantId(deleteReq.getTenantId());
        param.setSkuIds(Collections.singletonList(deleteReq.getSkuId()));
        List<ProductSkuPreferentialCostPrice> costPriceList = preferentialCostPriceDao.listPreferentialCostPriceByParam(param);
        if (CollectionUtils.isEmpty(costPriceList)) {
            throw new BizException("货品省心订信息不存在");
        }

        List<Long> productSkuPreferentialIds = costPriceList.stream().map(ProductSkuPreferentialCostPrice::getId).collect(Collectors.toList());
        return executeDeleteSkuPreferentialCostPriceId(deleteReq.getTenantId(), productSkuPreferentialIds, deleteReq.getSkuId());
    }

    public Boolean lockUpsertSkuPreferentialCostPrice(ProductPreferentialCostPriceCommandReq commandReq) {
        RLock lock = redissonClient.getLock(RedisKeyConstants.PREFERENTIAL_SKU_LOCK + commandReq.getTenantId());
        if (!lock.tryLock()) {
            throw new BizException("租户" + commandReq.getTenantId() + "正在操作中，请稍后提交");
        }
        try {
            return insertSkuPreferentialCostPrice(commandReq);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 批量更新省心订数据
     * @param commandReq
     * @return
     */
    private Boolean insertSkuPreferentialCostPrice(ProductPreferentialCostPriceCommandReq commandReq) {
        List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq> costPriceCommandReqs = commandReq.getCostPriceCommandReqs();
        List<Long> skuIds = costPriceCommandReqs.stream().map(ProductPreferentialCostPriceCommandReq.CostPriceCommandReq::getSkuId).collect(Collectors.toList());

        // 批量查询已存在的数据
        ProductSkuPreferentialQueryParam queryParam = ProductSkuPreferentialQueryParam.builder().tenantId(commandReq.getTenantId()).skuIds(skuIds).build();
        List<ProductSkuPreferentialCostPrice> existCostPriceList = preferentialCostPriceDao.listPreferentialCostPriceByParam(queryParam);
        Map<Long, List<ProductSkuPreferentialCostPrice>> existCostPriceSkuMap = existCostPriceList.stream().collect(Collectors.groupingBy(ProductSkuPreferentialCostPrice::getSkuId));
        List<ProductPreferentialCostPriceCommandReq.CityDataReq> cityDataReqs = commandReq.getCityDataReqs();
        Map<Long, String> cityMap = cityDataReqs.stream().collect(Collectors.toMap(ProductPreferentialCostPriceCommandReq.CityDataReq::getCityId,
                ProductPreferentialCostPriceCommandReq.CityDataReq::getCityName, (v1, v2) -> v1));

        Optional<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq> exist = costPriceCommandReqs.stream().filter(req ->
                Objects.nonNull(existCostPriceSkuMap.get(req.getSkuId()))).findFirst();
        if (exist.isPresent()) {
            log.warn("写入省心订价格失败，已存在省心订数据,exist:{}", JSON.toJSONString(exist.get()));
            throw new ParamsException("货品:" + exist.get().getSkuId() + "已配置省心订，请删除配置后添加");
        }

        Boolean executeResult = transactionTemplate.execute(status -> {
            Boolean result = true;
            try {
                for (ProductPreferentialCostPriceCommandReq.CostPriceCommandReq costPriceCommandReq : costPriceCommandReqs) {
                    List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> cityPriceReqList = costPriceCommandReq.getCityPriceReqList();
                    // 新增
                    boolean success = executeInsertSkuPreferentialCostPrice(commandReq.getTenantId(), costPriceCommandReq.getSkuId(), costPriceCommandReq, cityPriceReqList, cityMap);
                    if (!success) {
                        log.error("写入省心订价格异常，executeInsertSkuPreferentialCostPrice 执行失败，tenantId:{},skuId:{},costPriceCommandReq:{},insertList:{},cityMap:{}",
                                commandReq.getTenantId(), costPriceCommandReq.getSkuId(), JSON.toJSONString(costPriceCommandReq), JSON.toJSONString(costPriceCommandReq), JSON.toJSONString(cityMap));
                        throw new BizException(costPriceCommandReq.getSkuId() + "写入数据有误，请刷新页面重新修改");
                    }
                }
            } catch (Exception e) {
                log.error("写入品牌方省心定价格失败", e);
                status.setRollbackOnly();
                result = false;
            }
            return result;
        });
        return executeResult;
    }

//    /**
//     * 批量更新省心订数据
//     * @param commandReq
//     * @return
//     */
//    private Boolean upsertSkuPreferentialCostPrice(ProductPreferentialCostPriceCommandReq commandReq) {
//        List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq> costPriceCommandReqs = commandReq.getCostPriceCommandReqs();
//        List<Long> skuIds = costPriceCommandReqs.stream().map(ProductPreferentialCostPriceCommandReq.CostPriceCommandReq::getSkuId).collect(Collectors.toList());
//
//        // 批量查询已存在的数据
//        ProductSkuPreferentialQueryParam queryParam = ProductSkuPreferentialQueryParam.builder().tenantId(commandReq.getTenantId()).skuIds(skuIds).build();
//        List<ProductSkuPreferentialCostPrice> existCostPriceList = preferentialCostPriceDao.listPreferentialCostPriceByParam(queryParam);
//        Map<Long, List<ProductSkuPreferentialCostPrice>> existCostPriceSkuMap = existCostPriceList.stream().collect(Collectors.groupingBy(ProductSkuPreferentialCostPrice::getSkuId));
//        List<Long> skuPreferentialIds = existCostPriceList.stream().map(ProductSkuPreferentialCostPrice::getId).collect(Collectors.toList());
//        List<ProductSkuPreferentialCostPriceMapping> existMappingList = preferentialCostPriceMappingDao.listByCostPriceIds(commandReq.getTenantId(), skuPreferentialIds);
//        Map<Long, List<ProductSkuPreferentialCostPriceMapping>> existMappingPreferentialIdMap = existMappingList.stream().collect(Collectors.groupingBy(ProductSkuPreferentialCostPriceMapping::getSkuPreferentialCostPriceId));
//        List<ProductPreferentialCostPriceCommandReq.CityDataReq> cityDataReqs = commandReq.getCityDataReqs();
//        Map<Long, String> cityMap = cityDataReqs.stream().collect(Collectors.toMap(ProductPreferentialCostPriceCommandReq.CityDataReq::getCityId,
//                ProductPreferentialCostPriceCommandReq.CityDataReq::getCityName, (v1, v2) -> v1));
//
//        Boolean executeResult = transactionTemplate.execute(status -> {
//            Boolean result = true;
//            try {
//                for (ProductPreferentialCostPriceCommandReq.CostPriceCommandReq costPriceCommandReq : costPriceCommandReqs) {
//                    List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> cityPriceReqList = costPriceCommandReq.getCityPriceReqList();
//                    List<ProductSkuPreferentialCostPrice> skuCostPriceList = existCostPriceSkuMap.get(costPriceCommandReq.getSkuId());
//                    List<Long> existCostPriceId = Optional.ofNullable(skuCostPriceList).orElse(Lists.newArrayList()).stream().map(ProductSkuPreferentialCostPrice::getId).collect(Collectors.toList());
//
//                    // 更新数据
//                    List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> updateList = cityPriceReqList.stream().filter(req -> Objects.nonNull(req.getId())).collect(Collectors.toList());
//                    Optional<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> errorReq = updateList.stream().filter(req -> !existCostPriceId.contains(req.getId())).findFirst();
//                    if (errorReq.isPresent()) {
//                        log.error("写入省心订价格异常，数据有误,skuCostPriceList:{},errorReq:{}", JSON.toJSONString(skuCostPriceList), JSON.toJSONString(errorReq));
//                        throw new BizException(costPriceCommandReq.getSkuId() + "写入数据有误，请刷新页面重新修改");
//                    }
//                    // 新增数据
//                    List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> insertList = cityPriceReqList.stream().filter(req -> Objects.isNull(req.getId())).collect(Collectors.toList());
//
//                    List<Long> updateCostPriceIds = updateList.stream().map(ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq::getId).collect(Collectors.toList());
//                    // 需删除数据
//                    List<Long> deleteCostPriceIds = existCostPriceId.stream().filter(existId -> !updateCostPriceIds.contains(existId)).collect(Collectors.toList());
//
//                    // 增、删、改
//                    boolean success = executeInsertSkuPreferentialCostPrice(commandReq.getTenantId(), costPriceCommandReq.getSkuId(), costPriceCommandReq, insertList, cityMap);
//                    if (!success) {
//                        log.error("写入省心订价格异常，executeInsertSkuPreferentialCostPrice 执行失败，tenantId:{},skuId:{},costPriceCommandReq:{},insertList:{},cityMap:{}",
//                                commandReq.getTenantId(), costPriceCommandReq.getSkuId(), JSON.toJSONString(costPriceCommandReq), JSON.toJSONString(insertList), JSON.toJSONString(cityMap));
//                        throw new BizException(costPriceCommandReq.getSkuId() + "写入数据有误，请刷新页面重新修改");
//                    }
//                    executeDeleteSkuPreferentialCostPriceId(commandReq.getTenantId(), deleteCostPriceIds, costPriceCommandReq.getSkuId());
//                    executeUpdateSkuPreferentialCostPriceId(commandReq.getTenantId(), updateList, costPriceCommandReq, existMappingPreferentialIdMap, cityMap, skuCostPriceList);
//                }
//
//            } catch (Exception e) {
//                log.error("写入品牌方省心定价格失败", e);
//                status.setRollbackOnly();
//                result = false;
//            }
//            return result;
//        });
//        return executeResult;
//    }

    /**
     * 执行更新sku的省心订配置操作
     *
     * @param tenantId
     * @param updateList
     * @param costPriceCommandReq
     * @param existMappingPreferentialIdMap
     * @param cityMap
     * @param skuCostPriceList              已存在的省心订数据(用来比较锁定数量是否变化)
     */
    private void executeUpdateSkuPreferentialCostPriceId(Long tenantId, List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> updateList,
                                                         ProductPreferentialCostPriceCommandReq.CostPriceCommandReq costPriceCommandReq,
                                                         Map<Long, List<ProductSkuPreferentialCostPriceMapping>> existMappingPreferentialIdMap,
                                                         Map<Long, String> cityMap, List<ProductSkuPreferentialCostPrice> skuCostPriceList) {
        // 已存在的省心订数据
        Map<Long, ProductSkuPreferentialCostPrice> existCostPriceMap = skuCostPriceList.stream().collect(Collectors.toMap(ProductSkuPreferentialCostPrice::getId, Function.identity(), (v1, v2) -> v1));

        for (ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq cityPriceReq : updateList) {
            ProductSkuPreferentialCostPrice costPrice = new ProductSkuPreferentialCostPrice();
            costPrice.setId(cityPriceReq.getId());
            costPrice.setTenantId(costPriceCommandReq.getTenantId());
            costPrice.setSkuId(costPriceCommandReq.getSkuId());
            costPrice.setPrice(cityPriceReq.getPrice());
            costPrice.setQuantity(cityPriceReq.getQuantity());
            // 锁定数量修改了，变更数量
            Integer currentQuantity = Optional.ofNullable(existCostPriceMap.get(cityPriceReq.getId())).map(ProductSkuPreferentialCostPrice::getQuantity).orElse(NumberConstants.ZERO);
            if (!cityPriceReq.getQuantity().equals(currentQuantity)) {
                costPrice.setAvailableQuantity(NumberConstants.ZERO);
            }
            costPrice.setStartTime(costPriceCommandReq.getStartTime());
            costPrice.setEndTime(costPriceCommandReq.getEndTime());
            boolean success = preferentialCostPriceDao.updateById(costPrice);
            if (!success) {
                log.error("写入省心订价格异常，更新preferentialCostPrice数据失败，costPrice:{}", JSON.toJSONString(costPrice));
                throw new BizException(costPriceCommandReq.getSkuId() + "写入数据有误，请稍后再试");
            }

            List<ProductSkuPreferentialCostPriceMapping> costPriceMappings = existMappingPreferentialIdMap.get(cityPriceReq.getId());
            if (CollectionUtils.isEmpty(costPriceMappings)) {
                log.error("写入省心订价格异常，executeUpdateSkuPreferentialCostPriceId 数据异常，不存在mapping数据，costPriceCommandReq:{},cityPriceReqId:{}",
                        JSON.toJSONString(costPriceCommandReq), cityPriceReq.getId());
                throw new BizException(costPriceCommandReq.getSkuId() + "写入数据有误，请刷新页面重新修改");
            }
            List<Long> existCityIds = costPriceMappings.stream().map(ProductSkuPreferentialCostPriceMapping::getCityId).collect(Collectors.toList());
            List<Long> newCityIds = cityPriceReq.getCityIds().stream().filter(cityId -> !existCityIds.contains(cityId)).collect(Collectors.toList());
            List<Long> deleteCityIds = existCityIds.stream().filter(cityId -> !cityPriceReq.getCityIds().contains(cityId)).collect(Collectors.toList());
            preferentialCostPriceMappingDao.updateDeletedStatusByCityIds(deleteCityIds, costPriceCommandReq.getTenantId(), costPriceCommandReq.getSkuId());
            List<ProductSkuPreferentialCostPriceMapping> mappingList = newCityIds.stream().map(cityId -> {
                ProductSkuPreferentialCostPriceMapping mapping = new ProductSkuPreferentialCostPriceMapping();
                mapping.setTenantId(costPriceCommandReq.getTenantId());
                mapping.setSkuId(costPriceCommandReq.getSkuId());
                mapping.setSkuPreferentialCostPriceId(cityPriceReq.getId());
                mapping.setCityId(cityId);
                mapping.setCityName(cityMap.get(cityId));
                return mapping;
            }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(mappingList)) {
                success = preferentialCostPriceMappingDao.batchSave(mappingList);
                if (!success) {
                    log.error("写入省心订价格异常，更新preferentialCostPriceMapping数据失败，mappingList:{}", JSON.toJSONString(mappingList));
                    throw new BizException(costPriceCommandReq.getSkuId() + "写入数据有误，请稍后再试");
                }
            }
        }
    }

    /**
     * 执行插入操作
     *
     * @param tenantId
     * @param skuId
     * @param costPriceCommandReq
     * @param insertList
     * @param cityMap             城市id、name的Map
     */
    private boolean executeInsertSkuPreferentialCostPrice(Long tenantId, Long skuId, ProductPreferentialCostPriceCommandReq.CostPriceCommandReq costPriceCommandReq,
                                                          List<ProductPreferentialCostPriceCommandReq.CostPriceCommandReq.CityPriceReq> insertList,
                                                          Map<Long, String> cityMap) {
        List<ProductSkuPreferentialCostPriceMapping> mappingList = insertList.stream().map(
                cityPriceReq -> {
                    ProductSkuPreferentialCostPrice costPrice = new ProductSkuPreferentialCostPrice();
                    costPrice.setTenantId(costPriceCommandReq.getTenantId());
                    costPrice.setSkuId(costPriceCommandReq.getSkuId());
                    costPrice.setPrice(cityPriceReq.getPrice());
                    costPrice.setQuantity(cityPriceReq.getQuantity());
                    costPrice.setAvailableQuantity(cityPriceReq.getQuantity());
                    costPrice.setStartTime(costPriceCommandReq.getStartTime());
                    costPrice.setEndTime(costPriceCommandReq.getEndTime());
                    preferentialCostPriceDao.save(costPrice);

                    List<ProductSkuPreferentialCostPriceMapping> costPriceMappings = cityPriceReq.getCityIds().stream().map(cityId -> {
                        ProductSkuPreferentialCostPriceMapping costPriceMapping = new ProductSkuPreferentialCostPriceMapping();
                        costPriceMapping.setTenantId(costPriceCommandReq.getTenantId());
                        costPriceMapping.setSkuId(costPriceCommandReq.getSkuId());
                        costPriceMapping.setSkuPreferentialCostPriceId(costPrice.getId());
                        costPriceMapping.setCityId(cityId);
                        costPriceMapping.setCityName(cityMap.get(cityId));
                        return costPriceMapping;
                    }).collect(Collectors.toList());
                    return costPriceMappings;
                }
        ).flatMap(mappings -> mappings.stream()).collect(Collectors.toList());

        boolean result = true;
        List<List<ProductSkuPreferentialCostPriceMapping>> partition = Lists.partition(mappingList, 100);
        for (List<ProductSkuPreferentialCostPriceMapping> costPriceMappings : partition) {
            result = result && preferentialCostPriceMappingDao.batchSave(mappingList);
        }
        return result;
    }

    /**
     * 根据deleteCostPriceIds删除skuId的省心订配置
     *
     * @param tenantId
     * @param deleteCostPriceIds
     * @param skuId
     * @return
     */
    private Boolean executeDeleteSkuPreferentialCostPriceId(Long tenantId, List<Long> deleteCostPriceIds, Long skuId) {
        if (CollectionUtils.isEmpty(deleteCostPriceIds)) {
            return Boolean.FALSE;
        }
        Boolean priceResult = preferentialCostPriceDao.updateDeletedStatus(deleteCostPriceIds, tenantId, skuId);

        Boolean mappingResult = preferentialCostPriceMappingDao.updateDeletedStatus(deleteCostPriceIds, tenantId, skuId);
        if (priceResult && mappingResult) {
            return Boolean.TRUE;
        }
        throw new ProviderException("删除货品省心订信息失败");
    }

    /**
     * 查询所有生效中的省心定 skuId
     * @param tenantId
     * @return
     */
    public Set<Long> queryAvailableSkuIdByTenantId(Long tenantId) {
        return preferentialCostPriceDao.queryAvailableSkuIdByTenantId (tenantId);
    }

    public List<ProductPricingMessageDTO> selectFutureEndTime() {
        return preferentialCostPriceDao.selectFutureEndTime();
    }

    public List<ProductPricingMessageDTO> selectFutureStartTime() {
        return preferentialCostPriceDao.selectFutureStartTime();
    }

    @Transactional(rollbackFor = Exception.class)
    public void occupyAvailableQuantity(Long tenantId, Long cityId,Long orderId, List<OptAvailableQuantityReq> req) {
        List<Long> skuIds = req.stream ().map (OptAvailableQuantityReq::getSkuId).collect (Collectors.toList ());
        List<ProductSkuPreferentialCostPriceOccupy> dbOrderItemIds = occupyDao.listByOrderIdAndSkuIds (tenantId, orderId,skuIds);
        if(CollectionUtils.isNotEmpty (dbOrderItemIds)){
            log.warn ("sku省心定重复扣减，tenantId={}, cityId={},dbOrderItemIds={}",tenantId,cityId,JSON.toJSONString (dbOrderItemIds));
            throw new BizException ("订单重复扣减省心定库存");
        }
        Map<Long, Integer> skuMap = req.stream().collect(Collectors.groupingBy(OptAvailableQuantityReq::getSkuId, Collectors.summingInt(OptAvailableQuantityReq::getOptQuantity)));
        Map<Long, ProductSkuPreferentialCostPrice> costPriceMap = getSkuPreferentialCostPriceMap (tenantId, cityId, skuMap.keySet ());

        List<ProductSkuPreferentialCostPriceOccupy> priceOccupyList = new ArrayList<> ();

        skuMap.forEach ((skuId,optQuantity)->{
            ProductSkuPreferentialCostPrice preferentialCostPrice = costPriceMap.get (skuId);
            if(ObjectUtil.isEmpty (preferentialCostPrice)){
                log.warn ("occupyAvailableQuantity 省心定已失效");
                return;
            }
            int i = preferentialCostPrice.getAvailableQuantity () - optQuantity;
            if(i<0){
                throw new BizException ("库存不足");
            }
            Integer integer = preferentialCostPriceDao.optAvailableQuantity (preferentialCostPrice.getId (), optQuantity *-1);
            if(integer <= 0){
                throw new BizException ("库存不足");
            }
            priceOccupyList.add (extracted (tenantId, orderId,skuId,optQuantity,preferentialCostPrice.getId ()));
        });
        occupyDao.saveBatch (priceOccupyList);
    }

    private static ProductSkuPreferentialCostPriceOccupy extracted(Long tenantId,Long orderId,Long skuId, Integer optQuantity,Long preferentialCostPriceId) {
        ProductSkuPreferentialCostPriceOccupy priceOccupy = new ProductSkuPreferentialCostPriceOccupy ();
        priceOccupy.setTenantId(tenantId);
        priceOccupy.setSkuPreferentialCostPriceId(preferentialCostPriceId);
        priceOccupy.setSkuId(skuId);
        priceOccupy.setOrderId(orderId);
        priceOccupy.setOccupyQuantity(optQuantity);
        return priceOccupy;
    }


    @Transactional(rollbackFor = Exception.class)
    public void releaseAvailableQuantity(Long tenantId,Long orderId, List<OptAvailableQuantityReq> req) {
        List<Long> skuIds = req.stream ().map (OptAvailableQuantityReq::getSkuId).collect (Collectors.toList ());
        List<ProductSkuPreferentialCostPriceOccupy> costPriceOccupies = occupyDao.listByOrderIdAndSkuIds (tenantId, orderId,skuIds);
        if(CollectionUtils.isEmpty (costPriceOccupies)){
            return;
        }

        Map<Long, ProductSkuPreferentialCostPrice> costPriceMap = preferentialCostPriceDao.listByIds (costPriceOccupies.stream ().map (ProductSkuPreferentialCostPriceOccupy::getSkuPreferentialCostPriceId).collect (Collectors.toList ())).stream ().collect (Collectors.toMap (ProductSkuPreferentialCostPrice::getId, Function.identity ()));
        Map<Long, ProductSkuPreferentialCostPriceOccupy> occupyMap = costPriceOccupies.stream ().collect (Collectors.toMap (ProductSkuPreferentialCostPriceOccupy::getSkuId, Function.identity ()));

        req.forEach (e->{
            ProductSkuPreferentialCostPriceOccupy priceOccupy = occupyMap.get (e.getSkuId ());
            if(ObjectUtil.isNotEmpty (priceOccupy)){
                if(occupyDao.optOccupyQuantity (priceOccupy.getId (), e.getOptQuantity ()  *  -1) <= 0) {
                    log.warn ("重复释放库存,orderId={},skuId={}",orderId,e.getSkuId ());
                    throw new BizException ("重复释放库存");
                }
                ProductSkuPreferentialCostPrice costPrice = costPriceMap.get (priceOccupy.getSkuPreferentialCostPriceId ());
                int i = costPrice.getAvailableQuantity () + e.getOptQuantity ();
                if (i <= costPrice.getQuantity ()) {
                    preferentialCostPriceDao.optAvailableQuantity (costPrice.getId (), e.getOptQuantity ());
                }
            }
        });
    }
}
