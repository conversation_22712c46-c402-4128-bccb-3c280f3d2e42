package com.cosfo.item.web.domain.converter;

import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.common.enums.MarketItemPriceLogEnum;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceLogDTO;
import com.cosfo.item.infrastructure.price.model.MarketItemPrice;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceLog;
import com.cosfo.item.web.domain.vo.ItemPriceDetailVO;
import com.cosfo.item.web.domain.vo.PriceDetailVO;

import java.math.BigDecimal;

public class PriceConvert {
    public static PriceDetailVO marketItemPrice2PriceDetailVO(MarketItemPrice marketItemPrice){
        if(ObjectUtil.isEmpty (marketItemPrice)){
            return null;
        }
        PriceDetailVO vo = new PriceDetailVO ();
        vo.setCostPrice(marketItemPrice.getBasePrice ());
        vo.setPrice (marketItemPrice.getPrice ());
        vo.setMarketItemPrice(marketItemPrice.getPrice ());
        return vo;
    }

    public static ItemPriceDetailVO priceDetailVO2ItemPriceDetailVO(PriceDetailVO priceDetailVO,Long id) {
        ItemPriceDetailVO vo = new ItemPriceDetailVO ();
        if(ObjectUtil.isEmpty (priceDetailVO)){
            return vo;
        }
        vo.setPrice(priceDetailVO.getPrice ());
        vo.setMarketItemPrice (priceDetailVO.getPrice ());
        vo.setCostPrice(priceDetailVO.getCostPrice ());
        vo.setItemId(id);
        return vo;
    }

    public static MarketItemPrice marketItemPriceLogDTO2PriceEntity(MarketItemPriceLogDTO dto) {
        if(ObjectUtil.isEmpty (dto)){
            return null;
        }
        MarketItemPrice entity = new MarketItemPrice ();
        entity.setTenantId(dto.getTenantId ());
        entity.setMarketItemId(dto.getMarketItemId ());
        entity.setTargetType(dto.getTargetType ());
        entity.setTargetId(dto.getTargetId ());
        entity.setPrice(ObjectUtil.isNotEmpty (dto.getPrice ()) && dto.getPrice ().compareTo (BigDecimal.ZERO)<0?BigDecimal.ZERO:dto.getPrice ());
        entity.setPriceStrategy(dto.getPriceStrategy ());
        entity.setBasePrice(dto.getBasePrice ());
        return entity;
    }

    public static MarketItemPriceLog marketItemPriceLogDTO2PriceLogEntity(MarketItemPriceLogDTO dto) {
        if(ObjectUtil.isEmpty (dto)){
            return null;
        }
        MarketItemPriceLog entity = new MarketItemPriceLog ();
        entity.setTenantId(dto.getTenantId ());
        entity.setTargetType(dto.getTargetType ());
        entity.setTargetId(dto.getTargetId ());
        entity.setMarketItemId(dto.getMarketItemId ());
        entity.setSkuId(dto.getSkuId ());
        entity.setPrice(dto.getPrice ());
        entity.setPriceStrategy(dto.getPriceStrategy ());
        entity.setOpsType(dto.getOpsType ());
        entity.setBasePrice (dto.getBasePrice ());
        entity.setPriceRule (dto.getPriceRule ());
        return entity;
    }

    public static MarketItemPriceLog marketItemPrice2PriceLogEntity4Del(MarketItemPrice marketItemPrice) {
        MarketItemPriceLog entity = new MarketItemPriceLog ();
        entity.setTenantId(marketItemPrice.getTenantId ());
        entity.setTargetType(marketItemPrice.getTargetType ());
        entity.setTargetId(marketItemPrice.getTargetId ());
        entity.setMarketItemId(marketItemPrice.getMarketItemId ());
        entity.setPrice(marketItemPrice.getPrice ());
        entity.setPriceStrategy(marketItemPrice.getPriceStrategy ());
        entity.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode ());
        entity.setBasePrice (marketItemPrice.getBasePrice ());
        return entity;
    }
}
