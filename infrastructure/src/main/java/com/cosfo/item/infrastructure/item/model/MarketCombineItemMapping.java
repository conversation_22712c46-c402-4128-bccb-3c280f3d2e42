package com.cosfo.item.infrastructure.item.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 组合品映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketCombineItemMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 组合品itemid
     */
    private Long itemId;

    /**
     * 组合品子itemid
     */
    private Long combineItemId;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
