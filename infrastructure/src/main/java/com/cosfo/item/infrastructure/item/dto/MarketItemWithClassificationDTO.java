package com.cosfo.item.infrastructure.item.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class MarketItemWithClassificationDTO implements Serializable {

    private Long id;

    /**
     * 标题
     */
    private String title;
    /**
     * 副标题
     */
    private String subTitle;
    /**
     * 主图
     */
    private String mainPicture;

    /**
     * 规格
     */
    private String specification;
    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 前台分类
     */
    private Long classificationId;

    /**
     * 商品id
     */
    private Long marketId;

}
