package com.cosfo.item.web.mq.consumer;
import com.cosfo.item.common.constants.RocketMqConstant;

import com.cosfo.item.web.domain.service.SelfGoodsCostPriceSycnService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 描述: 接收到更新自营商品成本价通知
 */
@Slf4j
@Component
@MqListener(topic = RocketMqConstant.Topic.TOPIC_SELF_GOODS_COST_PRICE,
    consumerGroup = RocketMqConstant.ConsumeGroup.SELF_GOODS_COST_PRICE,
    tag = RocketMqConstant.Tag.SELF_GOODS_COST_PRICE,
    consumeThreadMin = 1,consumeThreadMax = 1
)

public class SelfGoodsCostPriceSycnListener extends AbstractMqListener<String> {

    @Autowired
    private SelfGoodsCostPriceSycnService selfGoodsCostPriceSycnService;

    @Override
    public void process(String tenantIdString) {
        Long tenantId = null;
        if(!StringUtils.isEmpty (tenantIdString)){
            tenantId = Long.valueOf (tenantIdString);
        }
        selfGoodsCostPriceSycnService.sycnSelfGoodsCostPriceFromOffline (tenantId);
    }
}
