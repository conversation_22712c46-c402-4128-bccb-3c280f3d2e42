package com.cosfo.item.infrastructure.price.model;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 价格映射配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketAreaItemMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long tenantId;

    /**
     * 映射区域item id
     */
    private Long areaItemId;

    /**
     * 0、百分比上浮 1、定额上浮 2、固定价
     */
    private Integer type;

    /**
     * 价格配置数额
     */
    private BigDecimal mappingNumber;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 定价方式0统一价1其他价
     */
    private Integer storePriceType;


}
