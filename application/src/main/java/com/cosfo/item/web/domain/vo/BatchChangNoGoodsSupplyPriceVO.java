package com.cofso.item.client.resp;

import com.cosfo.item.web.domain.vo.ChangNoGoodsSupplyPriceFailResultVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/31 11:54
 * @Description:
 */
@Data
public class BatchChangNoGoodsSupplyPriceVO implements Serializable {
    private static final long serialVersionUID = -2751310347098972061L;
    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 成功列表
     */
    private List<Long> successItemIds;

    /**
     * 失败原因
     */
    private List<ChangNoGoodsSupplyPriceFailResultVO> resultList;
}
