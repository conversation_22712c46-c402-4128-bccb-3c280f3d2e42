package com.cosfo.item.infrastructure.price.dto;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <p>
 * 成本价格表补偿表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
public class CompensateCostPriceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    List<CostPriceDTO> dtoList;

    List<Long> compensateCostPriceIds;

    /**
     * 更新类型
     */
    private Integer type;


}
