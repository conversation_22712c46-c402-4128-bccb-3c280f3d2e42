package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class InitCostPriceProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private TenantFacade tenantFacade;
    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始进行InitCostPriceProcessor任务,instanceParameters={}",JSON.toJSONString (context.getInstanceParameters()));
        List<TenantVO> tenantVOS = new ArrayList<> ();
        if(ObjectUtil.isNull (context) || ObjectUtil.isNull (context.getInstanceParameters ())){
            return new ProcessResult(true);
        }else {
            Long tenantId = Long.valueOf (context.getInstanceParameters ());
            //-99数据初始化 全部数据
            if(tenantId.equals (-99L)){
                tenantVOS = tenantFacade.listAllTenant ();
            }else{
                TenantVO tenantVO = tenantFacade.getTenantByTenantId (tenantId);
                tenantVOS.add (tenantVO);
            }
        }

        if(!ObjectUtil.isEmpty(tenantVOS)){
            //循环租户
            for(TenantVO tenant : tenantVOS) {
                handle (tenant);
            }
        }
        return new ProcessResult(true);
    }

    public void handle(TenantVO tenant) {
        List<ProductAgentSkuMappingVO> productAgentSkuMappingVOS = null;
        Map<Integer, List<ProvinceCityAreaVO>> areaNoMap = null;

        Long tenantId = tenant.getId ();
        try {
            List<String> areasFT = tenantFacade.listAddress (tenant.getId ());
            if(ObjectUtil.isEmpty(areasFT)){
                log.info("进行InitCostPriceProcessor任务,tenantId={}没有对应的省市区，跳过数据初始化",tenantId);
                return;
            }

            //查询运营区域 和 省市区的关系
            areaNoMap = summerfarmMallFacade.listAreaNoByAddressString (areasFT);
            if(CollectionUtil.isEmpty(areaNoMap)){
                log.info("进行InitCostPriceProcessor任务,tenantId={}的省市区没有查询到对应的运营区域，跳过数据初始化",tenantId);
                return;
            }
            List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemByTenantIdAndType (tenantId,MarketItemEnum.GoodsType.QUOTATION.getCode ());
            if(ObjectUtil.isEmpty(marketItemVOS)){
                log.info("进行InitCostPriceProcessor任务,tenantId={}没有商品，跳过数据初始化",tenantId);
                return;
            }

            Set<Long> skuIds = marketItemVOS.stream ().filter (e->ObjectUtil.isNotNull (e.getSkuId ())).map (MarketItemVO::getSkuId).collect (Collectors.toSet ());
            productAgentSkuMappingVOS = productFacade.listProductMappingBySkuIdsAndAgentTenantId (skuIds, xmTenantId);
            if(ObjectUtil.isEmpty(productAgentSkuMappingVOS)){
                log.info("进行InitCostPriceProcessor任务,tenantId={}没有XM货品，跳过数据初始化",tenantId);
                return;
            }
        }catch (Exception e){
            log.info("进行InitCostPriceProcessor任务,tenantId={}初始化准备失败",tenantId,e);
            return;
        }
        for (ProductAgentSkuMappingVO productAgentSkuMapping: productAgentSkuMappingVOS) {
            String sku = productAgentSkuMapping.getAgentSkuCode();
            areaNoMap.forEach((areaNo,addresses) -> {
                SummerFarmCostPriceVO summerFarmCostPriceVO = null;
                String province = "";
                String city = "";
                String area = "";
                try{
                    summerFarmCostPriceVO = sfMallManageFacade.queryMallPriceInfo4Saas(areaNo,sku, tenant.getAdminId());
                    SummerFarmCostPriceVO finalSummerFarmCostPriceVO = summerFarmCostPriceVO;
                    for (ProvinceCityAreaVO address : addresses) {
                        province = address.getProvince ();
                        city = address.getCity ();
                        area = address.getArea ();
                        costPriceDomianService.synchronize4Summerfarm (sku, tenantId, area, city, province, finalSummerFarmCostPriceVO);
                    }
                }catch (Exception e){
                    log.error("进行InitCostPriceProcessor任务 cost_price更新失败，sku={},tenantId={},area={},city={},province={},summerFarmCostPriceVO={}",sku,tenantId, area, city, province, JSON.toJSONString(summerFarmCostPriceVO),e);
                }
            });
        }
    }
}