package com.cosfo.item.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.xianmu.common.enums.base.Enum2StringArgs;

/**
 * <AUTHOR>
 */
public interface BinlogEventEnum {
    /**
     * binlog类型
     */
    @Getter
    @AllArgsConstructor
    enum Status implements Enum2StringArgs {
        INSERT("INSERT", "新增"),
        UPDATE("UPDATE", "修改"),
        DELETE("DELETE", "删除"),
        ;
        /**
         * 事件
         */
        private String event;
        /**
         * 事件描述
         */
        private String eventDesc;

        @Override
        public String getContent() {
            return null;
        }

        @Override
        public String getValue() {
            return null;
        }
    }
}
