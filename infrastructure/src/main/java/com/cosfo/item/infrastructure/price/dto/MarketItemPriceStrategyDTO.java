package com.cosfo.item.infrastructure.price.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemPriceStrategyDTO {
    /**
     * item id
     */
    private Long itemId;
    /**
     * 0、按成本价百分比上浮 1、按成本价定额上浮 2、固定价 3、按售价百分比下调 4、按售价定额下调
     */
    private Integer strategyType;
    /**
     * 策略值
     */
    private BigDecimal strategyValue;
    /**
     * 策略值
     */
    private String priceStrategyValue;

    /**
     * 价格目标 0-品牌方,1-门店,2-运营区域,3-门店分组
     */
    private Integer targetType;
    /**
     * 报价目标Id
     */
    private List<Long> targetIds;


    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}
