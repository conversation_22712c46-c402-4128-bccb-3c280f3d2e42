package com.cosfo.item.web.domain.converter;

import com.cosfo.item.infrastructure.item.model.MarketItemOnsaleStrategyMapping;
import com.cosfo.item.infrastructure.item.dto.MarketItemOnsaleStrategyDTO;
import com.cosfo.item.web.domain.vo.MarketItemOnsaleStrategyMappingVO;
import com.cosfo.item.web.domain.vo.MarketItemOnsaleStrategyVO;

import java.util.List;

public class MarketItemOnsaleStrategyMappingConverter {
    public static MarketItemOnsaleStrategyMapping marketItemOnsaleStrategyDTO2Entity(Long tenantId, MarketItemOnsaleStrategyDTO dto) {
        MarketItemOnsaleStrategyMapping entity = new MarketItemOnsaleStrategyMapping ();
        entity.setTenantId(tenantId);
        entity.setMType(dto.getMType ());
        entity.setShowFlag(dto.getShow ());
        entity.setItemId(dto.getItemId ());
        entity.setTargetId(dto.getTargetId ());
        entity.setOnSale(dto.getOnSale ());
        entity.setStrategyType(dto.getStrategyType ());
        return entity;
    }

    public static MarketItemOnsaleStrategyVO marketItemOnsaleStrategy2VO(MarketItemOnsaleStrategyMapping entity) {
        MarketItemOnsaleStrategyVO vo = new MarketItemOnsaleStrategyVO ();
        vo.setTenantId(entity.getTenantId ());
        vo.setMType(entity.getMType ());
        vo.setShow(entity.getShowFlag());
        vo.setItemId(entity.getItemId ());
        vo.setTargetId(entity.getTargetId ());
        vo.setOnSale(entity.getOnSale ());
        vo.setStrategyType(entity.getStrategyType ());
        return vo;
    }
    public static MarketItemOnsaleStrategyMappingVO marketItemOnsaleStrategy2VO2(MarketItemOnsaleStrategyMapping entity) {
        MarketItemOnsaleStrategyMappingVO vo = new MarketItemOnsaleStrategyMappingVO ();
        vo.setMType(entity.getMType());
        vo.setShowFlag(entity.getShowFlag());
        vo.setItemId(entity.getItemId());
        vo.setTargetId(entity.getTargetId());
        vo.setOnSale(entity.getOnSale());
        vo.setStrategyType(entity.getStrategyType());
        return vo;
    }
}
