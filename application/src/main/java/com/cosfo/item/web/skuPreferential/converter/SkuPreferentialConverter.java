package com.cosfo.item.web.skuPreferential.converter;

import com.cofso.preferential.client.req.ProductSkuPreferentialQueryReq;
import com.cofso.preferential.client.resp.ProductSkuPreferentialBasicResp;
import com.cofso.preferential.client.resp.ProductSkuPreferentialCostPriceResp;
import com.cosfo.item.common.skuPreferential.dto.ProductSkuPreferentialBasicDTO;
import com.cosfo.item.infrastructure.skuPreferential.model.ProductSkuPreferentialCostPrice;
import com.cosfo.item.infrastructure.skuPreferential.param.ProductSkuPreferentialQueryParam;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author: fansongsong
 * @Date: 2024-02-22
 * @Description:
 */
@Mapper
public interface SkuPreferentialConverter {

    SkuPreferentialConverter INSTANCE = Mappers.getMapper(SkuPreferentialConverter.class);

    ProductSkuPreferentialQueryParam reqToParam(ProductSkuPreferentialQueryReq queryReq);

    List<ProductSkuPreferentialCostPriceResp> entityListToSkuPreferentialCostPriceList(List<ProductSkuPreferentialCostPrice> prices);

    ProductSkuPreferentialCostPriceResp entityToProductSkuPreferentialCostPriceResp(ProductSkuPreferentialCostPrice costPrice);

    ProductSkuPreferentialBasicResp basicDtoToBasicResp(ProductSkuPreferentialBasicDTO basicDTO);
}
