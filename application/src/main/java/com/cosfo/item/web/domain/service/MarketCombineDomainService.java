package com.cosfo.item.web.domain.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cofso.item.client.enums.GoodsTypeEnum;
import com.cosfo.item.common.dto.PriceRangeDTO;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.common.utils.PriceUtil;
import com.cosfo.item.infrastructure.classification.dao.MarketItemClassificationDao;
import com.cosfo.item.infrastructure.classification.dto.MarketItemClassificationQueryParam;
import com.cosfo.item.infrastructure.classification.model.MarketItemClassification;
import com.cosfo.item.infrastructure.item.dao.MarketCombineItemMappingDao;
import com.cosfo.item.infrastructure.item.dao.MarketDao;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.dto.MarketCombineQueryParam;
import com.cosfo.item.infrastructure.item.dto.MarketItemParam;
import com.cosfo.item.infrastructure.item.dto.MarketQueryParam;
import com.cosfo.item.infrastructure.item.model.Market;
import com.cosfo.item.infrastructure.item.model.MarketCombineItemMapping;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dao.MarketItemPriceStrategyDao;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceStrategyDTO;
import com.cosfo.item.infrastructure.price.model.MarketItemPriceStrategy;
import com.cosfo.item.web.domain.converter.MarketCombineConvert;
import com.cosfo.item.web.domain.converter.MarketDomainConvert;
import com.cosfo.item.web.domain.dto.CombineAddDTO;
import com.cosfo.item.web.domain.dto.CombineMarketQueryDTO;
import com.cosfo.item.web.domain.dto.ItemClassificationDTO;
import com.cosfo.item.web.domain.vo.CombineMarketDetailVO;
import com.cosfo.item.web.domain.vo.CombineMarketListVO;
import com.cosfo.item.web.domain.vo.MarketCombineVO;
import com.cosfo.item.web.provider.converter.CombineMarketConvert;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cosfo.item.common.constants.MarketCombineConstants.*;
import static com.cosfo.item.common.constants.NumberConstants.PAGE_NUM;
import static com.cosfo.item.common.constants.NumberConstants.PAGE_SIZE;

/**
 * @author: monna.chen
 * @Date: 2023/4/28 18:10
 * @Description:
 */
@Service
public class MarketCombineDomainService {
    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private MarketDao marketDao;
    @Autowired
    private MarketItemClassificationDao marketItemClassificationDao;
    @Autowired
    private MarketCombineItemMappingDao combineItemMappingDao;
    @Autowired
    private MarketClassificationDomainService classificationDomainService;
    @Autowired
    private MarketItemPriceStrategyDomainService priceStrategyDomianService;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketItemPriceStrategyDao priceStrategyDao;

    /**
     * 根据组合品的itemId 查询 所有子item
     *
     * @param marketItemId 商品item主键
     * @return
     */
    public List<MarketCombineVO> queryCombineSubItemList(Long marketItemId) {
        List<MarketCombineItemMapping> subItemMappings = combineItemMappingDao.listByParam(MarketCombineQueryParam.builder()
            .itemId(marketItemId)
            .build());
        if (CollectionUtils.isEmpty(subItemMappings)) {
            return Collections.emptyList();
        }
        List<Long> subItemIds = subItemMappings.stream()
            .map(MarketCombineItemMapping::getCombineItemId)
            .collect(Collectors.toList());
        List<MarketItem> marketItems = marketItemDao.listByIds(subItemIds);
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyList();
        }
        Map<Long, MarketItem> marketItemMap = marketItems.stream().collect(Collectors.toMap(MarketItem::getId, Function.identity()));
        List<Long> marketIds = marketItems.stream().map(MarketItem::getMarketId).collect(Collectors.toList());
        List<Market> markets = marketDao.listByIds(marketIds);
        if (CollectionUtils.isEmpty(markets)) {
            return Collections.emptyList();
        }
        Map<Long, Market> marketMap = markets.stream().collect(Collectors.toMap(Market::getId, Function.identity()));

        /**
         * query price info
         */
        Map<Long, PriceRangeDTO> priceRangeMap = itemDomainService.getPriceRangeMap(MarketDomainConvert.INSTANCE.convert2VOs(marketItems));

        return getSubItemCollect(marketItemMap, marketMap, subItemMappings, priceRangeMap);
    }

    /**
     * 根据组合品的itemId批量 查询 所有子item
     *
     * @param marketItemIds
     * @return
     */
    public Map<Long, List<MarketCombineVO>> batchQueryCombineSubItemList(List<Long> marketItemIds) {
        if (CollectionUtils.isEmpty(marketItemIds)) {
            return Collections.emptyMap();
        }
        List<MarketCombineItemMapping> subItemMappings = combineItemMappingDao.listByParam(MarketCombineQueryParam.builder()
            .itemIds(marketItemIds)
            .build());
        if (CollectionUtils.isEmpty(subItemMappings)) {
            return Collections.emptyMap();
        }
        Map<Long, List<MarketCombineItemMapping>> subItemMap = subItemMappings.stream().collect(Collectors.groupingBy(MarketCombineItemMapping::getItemId));

        List<Long> subItemIds = subItemMappings.stream()
            .map(MarketCombineItemMapping::getCombineItemId)
            .distinct()
            .collect(Collectors.toList());
        List<MarketItem> marketItems = marketItemDao.listByIds(subItemIds);
        if (CollectionUtils.isEmpty(marketItems)) {
            return Collections.emptyMap();
        }
        Map<Long, MarketItem> marketItemMap = marketItems.stream().collect(Collectors.toMap(MarketItem::getId, Function.identity()));
        List<Long> marketIds = marketItems.stream().map(MarketItem::getMarketId).collect(Collectors.toList());
        List<Market> markets = marketDao.listByIds(marketIds);
        if (CollectionUtils.isEmpty(markets)) {
            return Collections.emptyMap();
        }
        Map<Long, Market> marketMap = markets.stream().collect(Collectors.toMap(Market::getId, Function.identity()));

        /**
         * query price info
         */
        Map<Long, PriceRangeDTO> priceRangeMap = itemDomainService.getPriceRangeMap(MarketDomainConvert.INSTANCE.convert2VOs(marketItems));

        return marketItemIds.stream().collect(Collectors.toMap(id -> id, id -> {
            List<MarketCombineItemMapping> subItemList = subItemMap.get(id);
            return getSubItemCollect(marketItemMap, marketMap, subItemList, priceRangeMap);
        }));
    }

    /**
     *
     * @param marketItemMap  子商品item
     * @param marketMap      子商品的market
     * @param subItemList    组合包子商品关系
     * @param priceRangeDTOMap  价格
     * @return
     */
    private List<MarketCombineVO> getSubItemCollect(Map<Long, MarketItem> marketItemMap, Map<Long, Market> marketMap,
                                                    List<MarketCombineItemMapping> subItemList,
                                                    Map<Long, PriceRangeDTO> priceRangeDTOMap) {
        return subItemList.stream()
            .map(i -> {
                MarketItem subItem = marketItemMap.get(i.getCombineItemId());
                if (Objects.isNull(subItem)) {
                    return null;
                }
                MarketCombineVO combineVO = MarketCombineConvert.INSTANCE.convert2Combines(subItem);
                combineVO.setMappingId(i.getId());
                combineVO.setQuantity(i.getQuantity());

                Market market = marketMap.get(subItem.getMarketId());
                if (Objects.isNull(market)) {
                    return combineVO;
                }
                combineVO.setItemTitle(market.getTitle());
                combineVO.setMainPicture(market.getMainPicture());

                PriceRangeDTO rangeDTO = Optional.ofNullable(priceRangeDTOMap.get(subItem.getId())).orElse(new PriceRangeDTO());
                combineVO.setPriceStr(rangeDTO.getPriceStr());
                combineVO.setMaxPrice(rangeDTO.getMaxPrice());
                combineVO.setMinPrice(rangeDTO.getMinPrice());
                return combineVO;
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }


    /**
     * 组合商品 列表
     *
     * @param queryDTO
     * @return
     */
    public Page<CombineMarketListVO> combineList(CombineMarketQueryDTO queryDTO) {
        MarketCombineQueryParam queryParam = buildQueryParam(queryDTO);
        if (Objects.isNull(queryParam)) {
            return null;
        }
        Page<Market> marketPage = marketDao.pageCombineByParam(queryParam);
        Page<CombineMarketListVO> pageInfo = new Page<>();
        pageInfo.setTotal(marketPage.getTotal());
        pageInfo.setRecords(fillCombineMarketList(marketPage.getRecords()));
        return pageInfo;
    }

    /**
     * 组装查询参数
     *
     * @param queryDTO
     * @return
     */
    private MarketCombineQueryParam buildQueryParam(CombineMarketQueryDTO queryDTO) {
        MarketCombineQueryParam queryParam = MarketCombineConvert.INSTANCE.convert2QueryParam(queryDTO);
        // 根据商品名称查询subitem的id
        if (Objects.nonNull(queryDTO.getItemTitle())) {
            List<Market> markets = marketDao.listByParam(MarketQueryParam.builder()
                .title(queryDTO.getItemTitle())
                .tenantId(queryDTO.getTenantId())
                .build());
            if (CollectionUtils.isEmpty(markets)) {
                return null;
            }
            List<MarketItem> marketItems = marketItemDao.listByParam(MarketItemParam.builder()
                .marketIds(markets.stream()
                    .map(Market::getId)
                    .collect(Collectors.toList()))
                .build());
            if (CollectionUtils.isEmpty(marketItems)) {
                return null;
            }
            queryParam.setSubItemIds(marketItems.stream()
                .map(MarketItem::getId)
                .collect(Collectors.toList()));
        }
        if (Objects.nonNull(queryDTO.getItemId())){
            MarketItem marketItem = marketItemDao.getById(queryDTO.getItemId());
            if (Objects.isNull(marketItem)){
                return null;
            }
            queryParam.setSubItemId(marketItem.getId());
        }
        return queryParam;
    }

    /**
     * 填充查询结果
     *
     * @param markets
     * @return
     */
    private List<CombineMarketListVO> fillCombineMarketList(List<Market> markets) {
        if (CollectionUtils.isEmpty(markets)) {
            return Collections.emptyList();
        }
        List<Long> marketIds = markets.stream()
            .map(Market::getId)
            .collect(Collectors.toList());
        /**
         * 查询分类
         */
        Map<Long, ItemClassificationDTO> classMap = classificationDomainService.getItemClassification(null, marketIds);

        /**
         * 上下架状态
         */
        List<MarketItem> marketItems = marketItemDao.listByParam(MarketItemParam.builder()
            .marketIds(marketIds)
            .build());
        // 组合包只有一个item,所以拿market_id作为key方便下面fill
        Map<Long, Integer> onSaleMap = marketItems.stream().collect(Collectors.toMap(MarketItem::getMarketId, MarketItem::getOnSale, (k1, k2) -> k1));
        /**
         * fill all info
         */
        return markets.stream()
            .map(market -> {
                CombineMarketListVO combineMarketListVO = MarketCombineConvert.INSTANCE.convert2ListVO(market);
                combineMarketListVO.setOnSale(onSaleMap.get(market.getId()));
                ItemClassificationDTO itemClassificationDTO = classMap.get(market.getId());
                if (Objects.nonNull(itemClassificationDTO) && market.getTenantId().equals(itemClassificationDTO.getFirstTenantId())) {
                    combineMarketListVO.setFirstClassificationId(itemClassificationDTO.getFirstClassificationId());
                    combineMarketListVO.setFirstClassificationName(itemClassificationDTO.getFirstClassificationName());
                }

                if (Objects.nonNull(itemClassificationDTO) && market.getTenantId().equals(itemClassificationDTO.getSecondTenantId())) {
                    combineMarketListVO.setSecondClassificationId(itemClassificationDTO.getSecondClassificationId());
                    combineMarketListVO.setSecondClassificationName(itemClassificationDTO.getSecondClassificationName());
                }
                return combineMarketListVO;
            })
            .collect(Collectors.toList());
    }

    /**
     * 组合商品 详情
     *
     * @param req combineMarketId+tenant_id
     * @return
     */
    public CombineMarketDetailVO combineDetail(CombineMarketQueryDTO req) {
        if (Objects.isNull(req) || Objects.isNull(req.getTenantId())) {
            throw new ParamsException("请输入租户ID");
        }
        if (Objects.isNull(req.getCombineMarketId()) && Objects.isNull(req.getItemId())) {
            throw new ParamsException("请输入组合包编码或组合包Item编码");
        }

        Market market;
        MarketItem marketItem;
        if (Objects.nonNull(req.getItemId())) {
            marketItem = marketItemDao.getById(req.getItemId());
            market = marketDao.getById(marketItem.getMarketId());
            if (Objects.isNull(market) || !market.getTenantId().equals(req.getTenantId())) {
                return null;
            }
        } else {
            market = marketDao.getById(req.getCombineMarketId());
            if (Objects.isNull(market) || !market.getTenantId().equals(req.getTenantId())) {
                return null;
            }
            List<MarketItem> marketItems = marketItemDao.listByParam(MarketItemParam.builder()
                .marketIds(Collections.singletonList(market.getId()))
                .tenantId(req.getTenantId())
                .build());
            if (CollectionUtils.isEmpty(marketItems)) {
                return null;
            }
            marketItem = marketItems.get(0);
        }

        // 基础信息
        CombineMarketDetailVO combineMarketDetailVO = MarketCombineConvert.INSTANCE.convert2CombineMarket(market);
        combineMarketDetailVO.setCombineItemId(marketItem.getId());
        combineMarketDetailVO.setOnSale(marketItem.getOnSale());
        combineMarketDetailVO.setPriceType(marketItem.getPriceType());
        // 分类
        Map<Long, ItemClassificationDTO> itemClassMap = classificationDomainService.getItemClassification(req.getTenantId(), Collections.singletonList(req.getCombineMarketId()));
        ItemClassificationDTO itemClassificationDTO = itemClassMap.get(req.getCombineMarketId());
        if (Objects.nonNull(itemClassificationDTO)) {
            combineMarketDetailVO.setFirstClassificationId(itemClassificationDTO.getFirstClassificationId());
            combineMarketDetailVO.setFirstClassificationName(itemClassificationDTO.getSecondClassificationName());
            combineMarketDetailVO.setSecondClassificationId(itemClassificationDTO.getSecondClassificationId());
            combineMarketDetailVO.setSecondClassificationName(itemClassificationDTO.getSecondClassificationName());
        }
        // 价格策略
        List<MarketItemPriceStrategy> priceStrategyList = priceStrategyDao.listByItemIds(req.getTenantId(), Collections.singletonList(marketItem.getId()));
        if (CollectionUtils.isNotEmpty(priceStrategyList)) {
            combineMarketDetailVO.setStrategyValue(priceStrategyList.get(0).getStrategyValue());
        }

        // 组合包 subitem
        List<MarketCombineVO> marketCombineVOS = queryCombineSubItemList(marketItem.getId());
        combineMarketDetailVO.setCombineItemList(marketCombineVOS);

        // 总价
        PriceRangeDTO priceRangeDTO = PriceUtil.calCombinePriceRange(combineMarketDetailVO.getCombineItemList().stream()
            .map(MarketCombineConvert.INSTANCE::convert2CombinePrice)
            .collect(Collectors.toList()));
        combineMarketDetailVO.setMaxTotalPrice(priceRangeDTO.getMaxPrice());
        combineMarketDetailVO.setMinTotalPrice(priceRangeDTO.getMinPrice());
        combineMarketDetailVO.setTotalPriceStr(priceRangeDTO.getPriceStr());

        return combineMarketDetailVO;
    }

    /**
     * 组合商品 新增
     *
     * @param addDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long combineAdd(CombineAddDTO addDTO) {
        // 校验入参
        if (Objects.nonNull(addDTO.getCombineMarketId())) {
            throw new ParamsException("已存在ID，请更新");
        }
        checkValid(addDTO);
        // save market
        Market market = CombineMarketConvert.INSTANCE.convert2Market(addDTO);
        marketDao.save(market);
        // save market_item
        MarketItem marketItem = new MarketItem();
        marketItem.setSkuId(NULL_SKU);
        marketItem.setMarketId(market.getId());
        marketItem.setSpecificationUnit(DEFAULT_SPEC_UNIT);
        marketItem.setGoodsType(GoodsTypeEnum.COMBINE_ITEM.getCode());
        marketItem.setItemType(MarketItemEnum.ItemTypeEnum.COMBINE_ITEM.getCode());
        marketItem.setOnSale(addDTO.getOnSale());
        marketItem.setPriceType(addDTO.getPriceType());
        marketItem.setTenantId(addDTO.getTenantId());
        marketItem.setCreateUserId(addDTO.getCreateUserId());
        marketItem.setEditUserId(addDTO.getEditUserId());
        marketItemDao.save(marketItem);
        // save market class
        if (Objects.nonNull(addDTO.getClassificationId())) {
            MarketItemClassification marketItemClassification = new MarketItemClassification();
            marketItemClassification.setTenantId(addDTO.getTenantId());
            marketItemClassification.setClassificationId(addDTO.getClassificationId());
            marketItemClassification.setMarketId(market.getId());
            marketItemClassification.setItemId(marketItem.getId());
            marketItemClassificationDao.save(marketItemClassification);
        }
        // save combine_mapping
        List<MarketCombineItemMapping> records = addDTO.getCombineItemList().stream()
            .map(item -> {
                MarketCombineItemMapping record = new MarketCombineItemMapping();
                record.setTenantId(addDTO.getTenantId());
                record.setItemId(marketItem.getId());
                record.setCombineItemId(item.getMarketItemId());
                record.setQuantity(item.getQuantity());
                return record;
            })
            .collect(Collectors.toList());
        combineItemMappingDao.saveBatch(records);
        // save price
        MarketItemPriceStrategyDTO priceStrategyDTO = MarketItemPriceStrategyDTO.builder()
            .itemId(marketItem.getId())
            .strategyType(MarketItemPriceStrategyEnum.StrategyTypeEnum.getCodeByPriceType(addDTO.getPriceType()))
            .strategyValue(addDTO.getStrategyValue())
            .targetType(DEFAULT_TARGET_TYPE.getCode())
            .targetIds(Collections.singletonList(addDTO.getTenantId()))
            .build();
        priceStrategyDomianService.saveOrUpdateMarketItemPriceStrategByItemId(addDTO.getTenantId(), Collections.singletonList(priceStrategyDTO));

        return market.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void combineUpdate(CombineAddDTO updateDTO) {
        if (Objects.isNull(updateDTO.getCombineMarketId())) {
            throw new ParamsException("ID不可为空");
        }
        checkValid(updateDTO);
        // update market
        Market market = CombineMarketConvert.INSTANCE.convert2Market(updateDTO);
        marketDao.updateById(market);
        // update market class
        List<MarketItemClassification> marketItemClassifications = marketItemClassificationDao.listByParam(MarketItemClassificationQueryParam.builder()
            .tenantId(updateDTO.getTenantId())
            .marketId(market.getId())
            .build());
        if (CollectionUtils.isEmpty(marketItemClassifications)) {
            MarketItemClassification marketItemClassification = new MarketItemClassification();
            marketItemClassification.setTenantId(updateDTO.getTenantId());
            marketItemClassification.setClassificationId(updateDTO.getClassificationId());
            marketItemClassification.setMarketId(market.getId());
            marketItemClassificationDao.save(marketItemClassification);
        } else if (1 != marketItemClassifications.size()) {
            throw new BizException("组合包商品分类信息错误！");
        } else {
            MarketItemClassification updateRecord = marketItemClassifications.get(0);
            updateRecord.setClassificationId(updateDTO.getClassificationId());
            marketItemClassificationDao.updateById(updateRecord);
        }
        // update market_item
        MarketItem marketItem = marketItemDao.getById(updateDTO.getCombineItemId());
        marketItem.setOnSale(updateDTO.getOnSale());
        marketItem.setPriceType(updateDTO.getPriceType());
        marketItem.setEditUserId(updateDTO.getEditUserId());
        marketItemDao.updateById(marketItem);
        // update combine_mapping
        updateCombineMapping(updateDTO, marketItem.getId());
        // update price
        MarketItemPriceStrategyDTO priceStrategyDTO = MarketItemPriceStrategyDTO.builder()
            .itemId(marketItem.getId())
            .strategyType(MarketItemPriceStrategyEnum.StrategyTypeEnum.getCodeByPriceType(updateDTO.getPriceType()))
            .strategyValue(updateDTO.getStrategyValue())
            .targetType(DEFAULT_TARGET_TYPE.getCode())
            .targetIds(Collections.singletonList(updateDTO.getTenantId()))
            .build();
        priceStrategyDomianService.saveOrUpdateMarketItemPriceStrategByItemId(updateDTO.getTenantId(), Collections.singletonList(priceStrategyDTO));
    }

    /**
     * 更新组合包sub item映射
     *
     * @param updateDTO
     * @param marketItemId 组合包的item_id
     */
    private void updateCombineMapping(CombineAddDTO updateDTO, Long marketItemId) {
        // 需要更新数据
        List<MarketCombineItemMapping> updateMappings = new ArrayList<>();
        // 新增数据
        List<MarketCombineItemMapping> saveMappings = new ArrayList<>();
        // 更新的ID，用于比较需要删除的ID
        List<Long> updateIds = new ArrayList<>();
        for (MarketCombineVO combineVO : updateDTO.getCombineItemList()) {
            MarketCombineItemMapping record = new MarketCombineItemMapping();
            if (Objects.isNull(combineVO.getMappingId())) {
                record.setTenantId(updateDTO.getTenantId());
                record.setItemId(marketItemId);
                record.setCombineItemId(combineVO.getMarketItemId());
                record.setQuantity(combineVO.getQuantity());
                saveMappings.add(record);
            } else {
                record.setId(combineVO.getMappingId());
                record.setQuantity(combineVO.getQuantity());
                updateMappings.add(record);
                updateIds.add(combineVO.getMappingId());
            }
        }
        // fixme 查询前加行锁 DB锁
        List<MarketCombineItemMapping> marketCombineItemMappings = combineItemMappingDao.listByParam(MarketCombineQueryParam.builder()
            .tenantId(updateDTO.getTenantId())
            .itemId(marketItemId)
            .build());
        List<Long> existIds = marketCombineItemMappings.stream()
            .map(MarketCombineItemMapping::getId)
            .sorted()
            .collect(Collectors.toList());
        List<Long> deleteIds = existIds.stream()
            .filter(id -> !updateIds.contains(id))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteIds)) {
            combineItemMappingDao.removeByIds(deleteIds);
        }
        if (CollectionUtils.isNotEmpty(saveMappings)) {
            combineItemMappingDao.saveBatch(saveMappings);
        }
        if (CollectionUtils.isNotEmpty(updateMappings)) {
            combineItemMappingDao.updateBatchById(updateMappings);
        }
    }

    private void checkValid(CombineAddDTO addDTO) {
        if (Objects.isNull(addDTO.getTenantId())) {
            throw new ParamsException("租户ID不可为空");
        }
        if (Objects.isNull(addDTO.getClassificationId())){
            throw new ParamsException("商品分组不可为空");
        }
        if (CollectionUtils.isEmpty(addDTO.getCombineItemList())) {
            throw new ParamsException("组合品子商品不可为空！");
        }
        if (!(addDTO.getCombineItemList().size() >= COMBINE_MIN_QUANTITY && addDTO.getCombineItemList().size() <= COMBINE_MAX_QUANTITY)) {
            throw new ParamsException("子商品至少添加" + COMBINE_MIN_QUANTITY + "个，最多" + COMBINE_MAX_QUANTITY + "个");
        }
        Page<MarketItem> marketItems = marketItemDao.pageByParam(MarketItemParam.builder()
            .eqTitle(addDTO.getCombineMarketTitle())
            .itemType(MarketItemEnum.ItemTypeEnum.COMBINE_ITEM.getCode())
            .tenantId(addDTO.getTenantId())
            .pageNum(Integer.valueOf(PAGE_NUM))
            .pageSize(Integer.valueOf(PAGE_SIZE))
            .build());
        if (CollectionUtils.isNotEmpty(marketItems.getRecords()) && marketItems.getRecords().stream()
            .map(MarketItem::getMarketId)
            .anyMatch(i -> !i.equals(addDTO.getCombineMarketId()))) {
            throw new BizException("组合品名称不可重复！");
        }
    }
}
