package com.cosfo.item.infrastructure.item.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/11/10 16:05
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemXmParam {
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * item_code
     */
    private List<String> itemCodeList;

    /**
     * maker_id
     */
    private List<Long> marketIds;

    /**
     * 0-已删除 1-正常使用
     */
    private Integer deleteFlag;

    /**
     * 上架区域
     */
    private Long onSaleTargetId;

    /**
     * 是否上架 1-上线 0-下架
     */
    private Integer onSaleFlag;

    /**
     * 商品标题
     */
    private String marketTitle;

    /**
     * 商品标题 是否模糊查询，默认是=true
     * true = 模糊
     * false = 精确
     */
    private Boolean marketTitleLikeFlag;

    /**
     * 后台类目
     */
    private List<Long> categoryIds;


    /**
     * 大客户展示
     * null -> m_type=0 and sku in
     * 1    -> sku in
     * 2    -> m_type=0 or sku in
     */
    private Integer adminShow;

    private Integer pageNum;
    private Integer pageSize;
    private List<String> sortDescList;
}
