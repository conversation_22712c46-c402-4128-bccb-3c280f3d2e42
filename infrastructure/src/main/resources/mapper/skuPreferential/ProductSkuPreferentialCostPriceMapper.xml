<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.item.infrastructure.skuPreferential.mapper.ProductSkuPreferentialCostPriceMapper">
    <update id="optAvailableQuantity">
        update product_sku_preferential_cost_price set
       available_quantity = available_quantity + #{optQuantity}
       where id = #{id}
         AND start_time &lt;= now() and end_time &gt;= now() and available_quantity + #{optQuantity} >= 0
         and available_quantity + #{optQuantity} &lt;= quantity

    </update>

    <select id="queryBasicData"
            resultType="com.cosfo.item.common.skuPreferential.dto.ProductSkuPreferentialBasicDTO">
        SELECT
            IFNULL(SUM(quantity) - SUM(available_quantity), 0) AS usedQuantity,
            IFNULL(SUM(available_quantity), 0) AS availableQuantity
        FROM product_sku_preferential_cost_price
        WHERE tenant_id = #{tenantId}
        AND deleted = 0
        <if test="skuIds != null and skuIds.size() > 0">
            AND sku_id IN
            <foreach item="skuId" index="index" collection="skuIds" open="(" separator="," close=")">
                #{skuId}
            </foreach>
        </if>
        <if test="availableFlag != null and availableFlag">
            AND start_time &lt;= now() and end_time &gt;= now() and available_quantity > 0
        </if>
        <if test="availableFlag != null and !availableFlag">
            AND (
            end_time &lt; now()
            OR
            available_quantity = 0
            )
        </if>
        <if test="waitValid != null and waitValid">
            AND start_time > now()
        </if>
    </select>

    <select id="selectFutureEndTime" resultType="com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO">
        SELECT
            tenant_id AS tenantId,
            sku_id AS skuId,
            end_time AS dealTime
        FROM
            product_sku_preferential_cost_price
        WHERE
            available_quantity > 0
            and end_time between date_format(DATE_ADD(now(), INTERVAL 1 DAY), '%Y-%m-%d 00:00:00' ) and date_format(DATE_ADD(now(), INTERVAL 1 DAY),'%Y-%m-%d 23:59:59' )
    </select>
    <select id="selectFutureStartTime" resultType="com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO">
        SELECT
            tenant_id AS tenantId,
            sku_id AS skuId,
            start_time AS dealTime
        FROM
            product_sku_preferential_cost_price
        WHERE
            available_quantity > 0
            and start_time between date_format(DATE_ADD(now(), INTERVAL 1 DAY), '%Y-%m-%d 00:00:00' ) and date_format(DATE_ADD(now(), INTERVAL 1 DAY),'%Y-%m-%d 23:59:59' )
    </select>
</mapper>
