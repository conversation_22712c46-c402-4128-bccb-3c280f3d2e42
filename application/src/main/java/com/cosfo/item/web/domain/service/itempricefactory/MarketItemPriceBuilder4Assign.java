package com.cosfo.item.web.domain.service.itempricefactory;

import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.common.enums.MarketItemPriceLogEnum;
import com.cosfo.item.common.enums.MarketItemPriceStrategyEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.MarketItemPriceLogDTO;
import com.cosfo.item.web.domain.vo.MarketItemPriceStrategyVO;
import com.cosfo.item.web.domain.vo.MerchantStoreAddressVO;
import com.cosfo.item.web.domain.vo.UnfairPriceStrategyVO;
import com.cofso.item.client.enums.MarketItemUnfairPriceStrategyEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 固定价 售价生成
 */
@Service
public class MarketItemPriceBuilder4Assign implements MarketItemPriceBuilder {
    @Autowired
    private MarketItemPriceBaseBuilder builder;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Override
    public MarketItemPriceStrategyEnum.StrategyTypeEnum getSupportType() {
        return MarketItemPriceStrategyEnum.StrategyTypeEnum.ASSIGN;
    }

    @Override
    public List<MarketItemPriceLogDTO> buildMarketItemPriceLogDTOS(List<MarketItemPriceStrategyVO> strategyVOS, MarketItem marketItem, List<MerchantStoreAddressVO> merchantStoreAddressVOS, UnfairPriceStrategyVO unfairPriceStrategyVO) {
        Long marketItemId = marketItem.getId ();
        Long tenantId = marketItem.getTenantId ();
        if(!xmTenantId.equals (tenantId)) {
            if (Objects.equals (MarketItemEnum.GoodsType.VIRTUAL.getCode (), marketItem.getGoodsType ())) {
                List<MarketItemPriceLogDTO> result = new ArrayList<> ();
                strategyVOS.forEach (strategyVO -> strategyVO.getTargetIds ().forEach (targetId -> {
                    MarketItemPriceLogDTO marketItemPriceLogDTO = builder.initMarketItemPriceLogDTO (marketItemId, tenantId, strategyVO, targetId, strategyVO.getTargetType (), marketItem.getSkuId ());
                    marketItemPriceLogDTO.setPrice (marketItemPriceLogDTO.getBasePrice ());
                    marketItemPriceLogDTO.setBasePrice (marketItem.getNoGoodsSupplyPrice () == null ? marketItemPriceLogDTO.getPrice () : marketItem.getNoGoodsSupplyPrice ());
                    fillPrice (marketItemPriceLogDTO, strategyVO,unfairPriceStrategyVO.getStrategyValue ());
                    result.add (marketItemPriceLogDTO);
                }));
                return result;
            } else {
                List<MarketItemPriceStrategyVO> strategyVOSCopy = JSON.parseArray (JSON.toJSONString (strategyVOS), MarketItemPriceStrategyVO.class);
                List<MarketItemPriceLogDTO> marketItemPriceLogDTOS = Collections.emptyList ();
                if (Objects.equals (MarketItemEnum.GoodsType.QUOTATION.getCode (), marketItem.getGoodsType ())) {
                    marketItemPriceLogDTOS = builder.buildMarketItemPriceLogDTOS4Quotation (strategyVOS, marketItem, merchantStoreAddressVOS);
                    marketItemPriceLogDTOS.stream ().filter (logdto -> !logdto.getOpsType ().equals (MarketItemPriceLogEnum.OptType.DELETE.getCode ())).forEach (logdto -> {
                        Long storeId = logdto.getTargetId ();
                        for (MarketItemPriceStrategyVO strategyVO : strategyVOSCopy) {
                            if (strategyVO.getTargetIds ().contains (storeId)) {
                                fillPrice (logdto, strategyVO,unfairPriceStrategyVO.getStrategyValue ());
                                break;
                            }
                        }
                    });
                } else if (Objects.equals (MarketItemEnum.GoodsType.SELF_SUPPORT.getCode (), marketItem.getGoodsType ())) {
                    marketItemPriceLogDTOS = builder.buildMarketItemPriceLogDTOS4SelfSupport (strategyVOS, marketItem, merchantStoreAddressVOS);
                    marketItemPriceLogDTOS.stream ().forEach (logdto -> {
                        Long storeId = logdto.getTargetId ();
                        if (!logdto.getOpsType ().equals (MarketItemPriceLogEnum.OptType.DELETE.getCode ())) {
                            for (MarketItemPriceStrategyVO strategyVO : strategyVOSCopy) {
                                if (strategyVO.getTargetIds ().contains (storeId)) {
                                    fillPrice (logdto, strategyVO,unfairPriceStrategyVO.getStrategyValue ());
                                    break;
                                }
                            }
                        } else {
                            logdto.setOpsType (MarketItemPriceLogEnum.OptType.ADD.getCode ());
                            logdto.setPrice (logdto.getBasePrice ());
                        }
                    });
                }

                return marketItemPriceLogDTOS;
            }
        }else{
            List<MarketItemPriceLogDTO> result = new ArrayList<>();
            strategyVOS.forEach(strategyVO -> strategyVO.getTargetIds().forEach(targetId -> {
                MarketItemPriceLogDTO marketItemPriceLogDTO = builder.initMarketItemPriceLogDTO(marketItemId, tenantId, strategyVO, targetId, strategyVO.getTargetType(), marketItem.getSkuId());
                marketItemPriceLogDTO.setPrice(marketItemPriceLogDTO.getBasePrice());
                result.add(marketItemPriceLogDTO);
            }));
            return result;
        }
    }

    private void fillPrice(MarketItemPriceLogDTO logdto, MarketItemPriceStrategyVO strategyVO, MarketItemUnfairPriceStrategyEnum.StrategyValueEnum strategyValue) {
        BigDecimal basePrice = logdto.getBasePrice();
        BigDecimal price = strategyVO.getStrategyValue();
        logdto.setPriceRule(String.valueOf (strategyValue.getCode()));
        //发生倒挂
        if (price.compareTo(basePrice) < 0) {
            switch (strategyValue) {
                case USE_COST_PRICE:
                    logdto.setPrice(basePrice);
                    break;
                case USE_ITEM_PRICE:
                    logdto.setPrice(price);
                    break;
                case AUTO_SOLD_OUT:
                    logdto.setOpsType(MarketItemPriceLogEnum.OptType.DELETE.getCode());
                    break;
            }
        } else {
            logdto.setPrice(price);
        }
    }
}
