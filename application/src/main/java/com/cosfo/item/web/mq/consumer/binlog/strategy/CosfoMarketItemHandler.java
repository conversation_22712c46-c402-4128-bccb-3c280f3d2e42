package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.item.dao.MarketItemDao;
import com.cosfo.item.infrastructure.item.model.MarketItem;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.service.MarketItemOnsalePriceDealService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class CosfoMarketItemHandler implements DbTableDmlStrategy {

    @Autowired
    private MarketItemDao marketItemDao;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private TenantFacade tenantFacade;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Override
    public String getTableDmlName() {
        return DBTableName.CosfoTable.MARKET_ITEM;
    }
    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                String skuIdString = map.get ("sku_id");
                Long marketItemId = Long.valueOf (map.get ("id"));
                Long tenantId = Long.valueOf (map.get ("tenant_id"));
                Integer deleteFlag = Integer.valueOf (map.get ("delete_flag"));
                if(ObjectUtil.isNotNull (skuIdString)) {
                    Long skuId = Long.valueOf (skuIdString);
                    handleCostPrice (skuId, tenantId, deleteFlag);
                }
                handleItemPrice (marketItemId, tenantId, deleteFlag);
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                Map<String, String> oldMap = pair.getValue ();

                Long tenantId = Long.valueOf (dataMap.get ("tenant_id"));
                Integer deleteFlag = Integer.valueOf (dataMap.get ("delete_flag"));

                String skuIdString = dataMap.get ("sku_id");
                Long marketItemId = Long.valueOf (dataMap.get ("id"));
                if(ObjectUtil.isNotNull (skuIdString)) {
                    Long skuId = Long.valueOf (skuIdString);
                    //修改了对应货品才做变更，否则不需要同步
                    if (oldMap.containsKey ("sku_id")) {
                        handleCostPrice (skuId, tenantId, deleteFlag);
                    }
                }
                if (oldMap.containsKey ("sku_id") || oldMap.containsKey ("on_sale") || oldMap.containsKey("no_goods_supply_price")) {
                    handleItemPrice (marketItemId, tenantId, deleteFlag);
                }
            }
        }
    }

    private void handleItemPrice(Long marketItemId, Long tenantId, Integer deleteFlag) {
        if(xmTenantId.equals (tenantId)) {
            return;
        }
        //不是删除 商品并且 有货品id
        if(Objects.equals(deleteFlag,MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag())) {
            MarketItem item = marketItemDao.getById (marketItemId);
            ItemChangeMessageDTO dto = ItemConverter.marketItem2MsgDTO (item);
            marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
        }
    }

    private void handleCostPrice(Long skuId,Long tenantId,Integer deleteFlag){
        if(xmTenantId.equals (tenantId)) {
            return;
        }
        //不是删除 商品并且 有货品id
        if(Objects.equals(deleteFlag,MarketItemEnum.DeleteFlagEnum.NORMAL.getFlag()) && ObjectUtil.isNotNull(skuId)){
            //查询这个货品是否是xm的
            ProductAgentSkuMappingVO productAgentSkuMapping = productFacade.getProductMappingBySkuIdAndTenantId(skuId, xmTenantId, xmTenantId);
            if(ObjectUtil.isEmpty(productAgentSkuMapping)){
                return;
            }
            String sku = productAgentSkuMapping.getAgentSkuCode();
            //查询品牌方覆盖的省市区
            List<String> areasFT = tenantFacade.listAddress (tenantId);
            if(CollectionUtil.isEmpty(areasFT)){
                return;
            }

            //查询运营区域
            Map<Integer, List<ProvinceCityAreaVO>> areaNoMap = summerfarmMallFacade.listAreaNoByAddressString (areasFT);
            if(CollectionUtil.isEmpty(areaNoMap)){
                return;
            }

            TenantVO tenantVO = tenantFacade.getTenantByTenantId(tenantId);
            if(ObjectUtil.isEmpty(tenantVO)){
                return;
            }
            areaNoMap.forEach((areaNo,addresses) -> {
                SummerFarmCostPriceVO summerFarmCostPriceVO = sfMallManageFacade.queryMallPriceInfo4Saas(areaNo,sku,tenantVO.getAdminId());

                addresses.forEach(address->{
                    String city = address.getCity ();
                    String area = address.getArea ();
                    String province = address.getProvince ();
                    try {
                        costPriceDomianService.synchronize4Summerfarm(sku,tenantVO.getId(), area ,city, province,summerFarmCostPriceVO);
                    }catch (Exception e){
                        costPriceDomianService.saveCompenstateCostPrice (sku,tenantVO.getId(), area ,city, province,e.toString ());
                        log.error("MarketItem变更 cost_price更新失败，sku={},tenantId={},area={},city={},province={},summerFarmCostPriceVO={}",sku,tenantVO.getId(),area,city,province, JSON.toJSONString(summerFarmCostPriceVO),e);
                    }
                });
            });
        }
    }
}
