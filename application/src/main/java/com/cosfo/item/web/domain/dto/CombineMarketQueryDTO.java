package com.cosfo.item.web.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: monna.chen
 * @Date: 2023/5/8 10:49
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CombineMarketQueryDTO {

    /**
     * 组合包标题
     */
    private String combineMarketTitle;

    /**
     * 组合包编码
     */
    private Long combineMarketId;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 商品编码
     */
    private Long itemId;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;
}
