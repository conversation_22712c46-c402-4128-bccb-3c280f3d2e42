package com.cosfo.item.infrastructure.price.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.item.infrastructure.price.model.CompensateCostPrice;
import com.cosfo.item.infrastructure.price.mapper.CompensateCostPriceMapper;
import com.cosfo.item.infrastructure.price.dao.CompensateCostPriceDao;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 成本价格表补偿表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service
public class CompensateCostPriceDaoImpl extends ServiceImpl<CompensateCostPriceMapper, CompensateCostPrice> implements CompensateCostPriceDao {

    @Override
    public List<CompensateCostPrice> listCompenstateCostPriceByIds(List<Long> ids) {
        LambdaQueryWrapper<CompensateCostPrice> wrapper = new LambdaQueryWrapper<>();
        if(!CollectionUtil.isEmpty (ids)){
            wrapper.in(CompensateCostPrice::getId);
        }
        wrapper.select (CompensateCostPrice::getTenantId,CompensateCostPrice::getSkuCode,CompensateCostPrice::getArea,CompensateCostPrice::getProvince,CompensateCostPrice::getCity);
        return list(wrapper);
    }
}
