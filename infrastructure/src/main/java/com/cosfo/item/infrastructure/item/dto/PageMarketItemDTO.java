package com.cosfo.item.infrastructure.item.dto;

import lombok.Data;

/**
 * @author: monna.chen
 * @Date: 2024/2/27 15:58
 * @Description:
 */
@Data
public class PageMarketItemDTO {

    /**
     * tenant_id
     */
    private Long tenantId;

    /**
     * 商品item主键
     */
    private Long itemId;

    /**
     * 商品主键
     */
    private Long marketId;

    /**
     * 商品头图
     */
    private String mainPicture;

    /**
     * 商品名称
     */
    private String itemTitle;

    /**
     * 规格
     */
    private String specification;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 类目Id
     */
    private Long categoryId;
    /**
     * 一级分类Id
     */
    private Long classificationId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 货品类型 0无货商品 1报价货品 2自营货品
     */
    private Integer goodsType;
}
