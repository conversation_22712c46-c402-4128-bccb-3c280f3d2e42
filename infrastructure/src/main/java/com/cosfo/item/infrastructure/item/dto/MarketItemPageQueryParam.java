package com.cosfo.item.infrastructure.item.dto;

import lombok.Data;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2024/2/27 14:32
 * @Description:
 */
@Data
public class MarketItemPageQueryParam {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 是否在使用
     */
    private Integer deleteFlag;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 商品编码
     */
    private String itemId;

    /**
     * 前台分类
     */
    private List<Long> classificationIds;

    /**
     * 后台类目
     */
    private List<Long> categoryIds;

    /**
     * 自有编码
     */
    private String itemCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 货品类型
     */
    private List<Integer> goodsType;

    /**
     * 上下架状态
     */
    private Integer onSale;


    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;
}
