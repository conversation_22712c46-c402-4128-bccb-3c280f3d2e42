package com.cosfo.item.web.domain.converter;

import com.cosfo.item.common.enums.CostPriceEnum;
import com.cosfo.item.infrastructure.price.dto.CostPriceDTO;
import com.cosfo.item.infrastructure.price.dto.ProductPricingSupplyDTO;
import com.cosfo.item.infrastructure.price.model.CompensateCostPrice;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import com.cosfo.item.infrastructure.price.model.CostPriceLog;
import com.cosfo.item.web.domain.dto.MerchantAddressDTO;
import com.cosfo.item.web.domain.vo.CostPriceVO;

import java.time.LocalDateTime;
import java.time.Month;
import java.util.Objects;

public class CostPriceConverter {

    public static CostPriceVO costPrice2VO(CostPrice e) {
        CostPriceVO vo = new CostPriceVO();
        vo.setId(e.getId());
        vo.setTenantId(e.getTenantId());
        vo.setSkuCode(e.getSkuCode());
        vo.setArea(e.getArea());
        vo.setCity(e.getCity());
        vo.setPrice(e.getPrice());
        vo.setProvince (e.getProvince ());
        vo.setSkuId (e.getSkuId ());
        vo.setAddressKey(buildCityAreaKey(e.getCity (),e.getArea ()));
        return vo;
    }

    public static CostPrice costPriceDTO2Entity(CostPriceDTO dto) {
        CostPrice costPrice = new CostPrice();
        costPrice.setTenantId(dto.getTenantId());
        costPrice.setSkuId(dto.getSkuId());
        costPrice.setSkuCode(dto.getSkuCode());
        costPrice.setArea(dto.getArea());
        costPrice.setCity(dto.getCity());
        costPrice.setProvince (dto.getProvince ());
        costPrice.setPrice(dto.getPrice());
        costPrice.setValidTime(dto.getValidTime());
        costPrice.setInvalidTime(dto.getInvalidTime());
        costPrice.setValidType(dto.getValidType());
        if(Objects.equals (dto.getValidType(), CostPriceEnum.ValidType.FOREVER.getCode ())){
            costPrice.setInvalidTime(LocalDateTime.of(2050, Month.DECEMBER,31,00,00,00));
            costPrice.setValidTime(LocalDateTime.of(2000, Month.DECEMBER,31,00,00,00));
        }
        costPrice.setSysType(dto.getSysType());
        return costPrice;
    }

    public static CostPriceLog costPriceDTO2LogEntity(CostPriceDTO dto,Integer optType) {
        CostPriceLog log = new CostPriceLog();
        log.setTenantId(dto.getTenantId());
        log.setSkuId(dto.getSkuId());
        log.setSkuCode(dto.getSkuCode());
        log.setArea(dto.getArea());
        log.setCity(dto.getCity());
        log.setProvince (dto.getProvince ());
        log.setPrice(dto.getPrice());
        log.setValidTime(dto.getValidTime());
        log.setInvalidTime(dto.getInvalidTime());
        log.setValidType(dto.getValidType());
        log.setOptType(optType);
        return log;
    }

    public static CompensateCostPrice costPriceDTO2CompensateCostPrice(CostPriceDTO costPriceDTO) {
        CompensateCostPrice compensateCostPrice = new CompensateCostPrice ();
        compensateCostPrice.setTenantId(costPriceDTO.getTenantId ());
        compensateCostPrice.setSkuCode(costPriceDTO.getSkuCode ());
        compensateCostPrice.setArea(costPriceDTO.getArea ());
        compensateCostPrice.setProvince(costPriceDTO.getProvince ());
        compensateCostPrice.setCity(costPriceDTO.getCity ());
        return compensateCostPrice;
    }

    public static CostPriceLog costPrice2LogEntity(CostPrice e, Integer optType) {
        CostPriceLog log = new CostPriceLog();
        log.setTenantId(e.getTenantId());
        log.setSkuId(e.getSkuId());
        log.setSkuCode(e.getSkuCode());
        log.setArea(e.getArea());
        log.setCity(e.getCity());
        log.setProvince (e.getProvince ());
        log.setPrice(e.getPrice());
        log.setValidTime(e.getValidTime());
        log.setInvalidTime(e.getInvalidTime());
        log.setValidType(e.getValidType());
        log.setOptType(optType);
        return log;
    }

    public static CostPriceVO buildAreaCostPriceDTO(ProductPricingSupplyDTO productPricingSupplyDTO, MerchantAddressDTO addressDTO) {
        CostPriceVO costPriceVO = new CostPriceVO ();
        costPriceVO.setProvince(addressDTO.getProvince ());
        costPriceVO.setCity(addressDTO.getCity ());
        costPriceVO.setArea(addressDTO.getArea ());
        costPriceVO.setAddressKey(buildCityAreaKey(addressDTO.getCity (),addressDTO.getArea ()));

        costPriceVO.setTenantId(productPricingSupplyDTO.getTenantId ());
        costPriceVO.setSkuId(productPricingSupplyDTO.getSkuId ());
        costPriceVO.setCityId(productPricingSupplyDTO.getCityId ());
        costPriceVO.setPrice(productPricingSupplyDTO.getPrice ());

        return costPriceVO;
    }
    /**
     * 用《城市_区》 来做Map和Set的key；
     * @param city
     * @param area
     * @return
     */
    public static String buildCityAreaKey(String city, String area) {
        return String.format("%s_%s", city, area);
    }
}
