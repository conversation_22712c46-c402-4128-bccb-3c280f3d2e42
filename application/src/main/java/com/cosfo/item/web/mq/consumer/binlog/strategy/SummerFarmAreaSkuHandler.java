package com.cosfo.item.web.mq.consumer.binlog.strategy;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cosfo.item.common.constants.DBTableName;
import com.cosfo.item.common.enums.BinlogEventEnum;
import com.cosfo.item.common.enums.MarketAreaItemEnum;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.web.domain.service.AddressService;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.ProductFacade;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelEvent;
import com.cosfo.item.web.mq.consumer.binlog.input.DtsModelHandler;
import com.google.common.base.Splitter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SummerFarmAreaSkuHandler implements DbTableDmlStrategy {

    @Autowired
    private AddressService addressService;
    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private ProductFacade productFacade;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private TenantFacade tenantFacade;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;

    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Override
    public String getTableDmlName() {
        return DBTableName.SummerfarmTable.AREA_SKU;
    }

    @Override
    public void tableDml(DtsModelEvent dtsModelEvent) {
        if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.INSERT.name())) {
            dtsModelEvent.consumerData (map -> {
                Integer areaNo = Integer.parseInt (map.get ("area_no"));
                String sku = map.get ("sku");
                handle(areaNo,sku);
            });
        } else if (Objects.equals(dtsModelEvent.getType(), BinlogEventEnum.Status.UPDATE.name())) {
            List<Pair<Map<String, String>, Map<String, String>>> pairList = DtsModelHandler.getAlignedData (dtsModelEvent);
            for (Pair<Map<String, String>, Map<String, String>> pair : pairList) {
                Map<String, String> dataMap = pair.getKey ();
                String sku = dataMap.get ("sku");
                Integer areaNo = Integer.valueOf (dataMap.get ("area_no"));

                Map<String, String> oldMap = pair.getValue ();
                if(oldMap.containsKey ("area_no") || oldMap.containsKey ("price") || oldMap.containsKey ("on_sale")){
                    handle(areaNo,sku);
                }
            }
        }
    }


    private void handle(Integer areaNo, String sku) {
        //查询用过这个sku的品牌方
        List<ProductAgentSkuMappingVO> productAgentSkuMappingVOS = productFacade.listProductMappingByAgentSkuCode (sku, xmTenantId, xmTenantId);
        if (CollectionUtil.isEmpty (productAgentSkuMappingVOS)) {
            return;
        }
        Set<Long> skuIds = productAgentSkuMappingVOS.stream ().map (ProductAgentSkuMappingVO::getSkuId).collect (Collectors.toSet ());
        List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemBySkuIdsAndTypes(null, skuIds, Collections.singletonList (MarketItemEnum.GoodsType.QUOTATION.getCode()));
        if (CollectionUtil.isEmpty (marketItemVOS)) {
            return;
        }

        //查询运营区域覆盖的省市区
        List<ProvinceCityAreaVO> citys = summerfarmMallFacade.getAddressInfoByAreaNo (areaNo);
        if (CollectionUtil.isEmpty (citys)) {
            return;
        }
        Set<String> areasXMc = citys.stream ().filter (e-> StringUtil.isEmpty (e.getArea ())).map (ProvinceCityAreaVO::getCity).collect (Collectors.toSet ());
        Set<String> areasXMca = citys.stream ().filter (e->StringUtil.isNotEmpty (e.getArea ())).map (e ->e.getCity () + "-" + e.getArea ()).collect (Collectors.toSet ());


        Map<Long, Set<String>> resultMap = new LinkedHashMap<> ();
        Set<Long> tenantIds = marketItemVOS.stream ().map (MarketItemVO::getTenantId).collect (Collectors.toSet ());
        tenantIds.forEach (tenantId -> {
            //查询品牌方覆盖的省市区
            List<String> addresses = tenantFacade.listAddress (tenantId);
            Set<String> addressResult = addressService.filterAddress (addresses,areasXMca,areasXMc);
            if(CollectionUtil.isNotEmpty (addressResult)) {
                resultMap.put (tenantId, addressResult);
            }
        });

        if (CollectionUtil.isEmpty (resultMap)) {
            return;
        }

        resultMap.forEach ((tenantId, addresses) -> handle (areaNo, sku, tenantId, addresses));
    }

    public void handle(Integer areaNo, String sku, Long tenantId, Set<String> addresses) {
        TenantVO tenantVO = tenantFacade.getTenantByTenantId (tenantId);
//      根据这三个参数查询到一个价格he skuid 生效时间，生效类型
        SummerFarmCostPriceVO summerFarmCostPriceVO  = sfMallManageFacade.queryMallPriceInfo4Saas(areaNo,sku,tenantVO.getAdminId());

//      根据areaNo查询到他下面的所有省市区（注意没有区的市）循环所有的省市区结果落库
        addresses.forEach (address -> {

            String province = "";
            String city = "";
            String area = "";
            try {
                List<String> pca = Splitter.on ("-").splitToStream (address).map (String::valueOf).collect (Collectors.toList ());
                province = pca.get (0);
                if(pca.size () > 0) {
                    city = pca.get (1);
                }
                if(pca.size () > 1) {
                    area = pca.get (2);
                }
                costPriceDomianService.synchronize4Summerfarm (sku, tenantVO.getId (), area, city,province, summerFarmCostPriceVO);
            } catch (Exception e) {
                costPriceDomianService.saveCompenstateCostPrice (sku,tenantVO.getId(), area ,city, province,e.toString ());
                log.error ("AreaSku变更 cost_price更新失败，sku={},tenantId={},area={},city={},province={},summerFarmCostPriceVO={}", sku, tenantVO.getId (), area, city,province, JSON.toJSONString (summerFarmCostPriceVO), e);
            }
        });
    }
}
