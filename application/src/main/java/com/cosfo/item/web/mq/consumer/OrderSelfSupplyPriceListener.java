package com.cosfo.item.web.mq.consumer;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.item.common.constants.RocketMqConstant;
import com.cosfo.item.common.enums.MarketItemEnum;
import com.cosfo.item.infrastructure.price.dto.ItemChangeMessageDTO;
import com.cosfo.item.starter.config.SpringContextUtil;
import com.cosfo.item.infrastructure.price.dto.ProductPricingMessageDTO;
import com.cosfo.item.web.domain.converter.ItemConverter;
import com.cosfo.item.web.domain.service.ItemDomainService;
import com.cosfo.item.web.domain.service.MarketItemOnsalePriceDealService;
import com.cosfo.item.web.domain.vo.MarketItemVO;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import net.xianmu.rocketmq.support.producer.MqProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;

/**
 * 描述: 处理城市报价单生效、失效 重新刷新对应的商品价格
 */
@Slf4j
@Component
@MqListener(topic = RocketMqConstant.Topic.SUPPLY_PRICE,
    consumerGroup = RocketMqConstant.ConsumeGroup.ITEM_SUPPLY_PRICE,
    tag = RocketMqConstant.Tag.SUPPLY_PRICE,
    consumeThreadMin = 1,consumeThreadMax = 1
)
public class OrderSelfSupplyPriceListener extends AbstractMqListener<ProductPricingMessageDTO> {

    @Autowired
    private ItemDomainService itemDomainService;
    @Autowired
    private MarketItemOnsalePriceDealService marketItemOnsalePriceDealService;
    @Autowired
    private MqProducer mqProducer;

    @Override
    public void process(ProductPricingMessageDTO productPricingMessageDTO) {
        log.info ("rocketmq 城市报价单生效、失效 重新刷新对应的商品价格，消息内容：{}", JSONObject.toJSONString (productPricingMessageDTO));
        LocalDateTime dealTime = productPricingMessageDTO.getDealTime ();
        LocalDateTime now = LocalDateTime.now ();
        LocalDate today = LocalDate.now ();

        if (ObjectUtil.isNull (dealTime)) {
            return;
        }
        log.info ("OrderSelfSupplyPriceListener - dealTime={},now={},today={}", dealTime, now, today);
        if (dealTime.equals (now) || dealTime.isBefore (now)) {
            log.info ("OrderSelfSupplyPriceListener - dealTime <= now,开始处理关联商品");
            List<MarketItemVO> marketItemVOS = itemDomainService.listMarketItemBySkuIdAndTenantIdAndGoodsTypes (productPricingMessageDTO.getSkuId (),
                    productPricingMessageDTO.getTenantId (), Collections.singletonList (MarketItemEnum.GoodsType.QUOTATION.getCode ()));
            log.info ("OrderSelfSupplyPriceListener - marketItemVOS={}", JSON.toJSONString (marketItemVOS));
            for (MarketItemVO marketItemVO : marketItemVOS) {
                ItemChangeMessageDTO dto = ItemConverter.marketItemVO2MsgDTO (marketItemVO);
                marketItemOnsalePriceDealService.sendToOrderedQueue (dto);
            }
        } else {
            log.info ("OrderSelfSupplyPriceListener - dealTime > now");
            if (!SpringContextUtil.isProduct ()) {
                log.info ("OrderSelfSupplyPriceListener - 测试环境");
                if (ChronoUnit.DAYS.between (dealTime.toLocalDate (), today) < 2) {
                    log.info ("OrderSelfSupplyPriceListener - dealTime 和 today，之间的时间间隔小于2天，重新投递，递消息内容：{}", JSONObject.toJSONString (productPricingMessageDTO));
                    LocalDateTime day39 = now.plusDays (39);
                    mqProducer.sendStartDeliver (RocketMqConstant.Topic.SUPPLY_PRICE, RocketMqConstant.Tag.SUPPLY_PRICE, JSON.toJSON (productPricingMessageDTO), dealTime.isAfter (day39) ? day39 : dealTime.plusSeconds (1));
                } else {
                    log.info ("OrderSelfSupplyPriceListener - dealTime 和 today，之间的时间间隔>=2天，丢弃不处理");
                }
            } else {
                log.info ("OrderSelfSupplyPriceListener - 正式环境，不处理");
            }
        }
    }
}