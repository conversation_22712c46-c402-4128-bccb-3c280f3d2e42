package com.cosfo.item.web.provider.converter;

import cn.hutool.core.util.ObjectUtil;
import com.cofso.item.client.req.CostPriceQueryRangeReq;
import com.cofso.item.client.req.CostPriceQueryReq;
import com.cofso.item.client.resp.CostPriceRangeResultResp;
import com.cofso.item.client.resp.CostPriceResultResp;
import com.cosfo.item.common.enums.CostPriceEnum;
import com.cosfo.item.infrastructure.price.dto.CostPriceQueryDTO;
import com.cosfo.item.infrastructure.price.dto.CostPriceQueryRangeDTO;
import com.cosfo.item.web.domain.vo.CostPriceRangeVO;
import com.cosfo.item.web.domain.vo.CostPriceVO;

public class CostPricePrividerConverter {

    public static CostPriceQueryDTO costPriceQueryReq2DTO(CostPriceQueryReq req) {
        CostPriceQueryDTO dto = new CostPriceQueryDTO();
        dto.setTenantId(req.getTenantId());
        dto.setSkuIds(req.getSkuIds());
        dto.setArea(req.getArea());
        dto.setCity(req.getCity());
        dto.setValidFlag(true);
        dto.setSysType(CostPriceEnum.SysType.SUMMERFARM.getCode());
        return dto;
    }

    @Deprecated
    public static CostPriceResultResp costPriceVO2Resp(CostPriceVO vo) {
        CostPriceResultResp resp = new CostPriceResultResp();
        resp.setSkuId(vo.getSkuId());
        resp.setPrice(vo.getPrice());
        return resp;
    }

    public static CostPriceQueryRangeDTO costPriceQueryRangeReq2DTO(CostPriceQueryRangeReq req) {
        CostPriceQueryRangeDTO dto = new CostPriceQueryRangeDTO ();
        dto.setSkuId(req.getSkuId ());
        dto.setCitys(req.getCitys());
        return dto;
    }

    public static CostPriceRangeResultResp costPriceRangeVO2Resp(CostPriceRangeVO vo) {
        if(ObjectUtil.isEmpty (vo)){
            return null;
        }
        CostPriceRangeResultResp resp = new CostPriceRangeResultResp ();
        resp.setSkuId(vo.getSkuId ());
        resp.setMinPrice(vo.getMinPrice ());
        resp.setMaxPrice(vo.getMaxPrice ());
        return resp;
    }
}
