package com.cosfo.item.web.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: monna.chen
 * @Date: 2023/5/17 09:35
 * @Description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarketItemOnSaleInputDTO {
    /**
     * 主键Id
     */
    private Long id;
    /**
     * 批量主键
     */
    private List<Long> itemIds;
    /**
     * 上下架 0上架1下架
     */
    private Integer onSale;
    /**
     * 租户ID
     */
    private Long tenantId;
}
