package com.cosfo.item.web.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.item.infrastructure.price.model.CostPrice;
import com.cosfo.item.web.domain.service.CostPriceDomianService;
import com.cosfo.item.web.domain.vo.*;
import com.cosfo.item.web.facade.SfMallManageFacade;
import com.cosfo.item.web.facade.SummerfarmMallFacade;
import com.cosfo.item.web.facade.TenantFacade;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.*;

/**
 * 每三分钟执行一次，过期补偿
 */
@Component
@Slf4j
public class InvalidCostPriceUpdateProcessor extends XianMuJavaProcessorV2 {

    @Autowired
    private CostPriceDomianService costPriceDomianService;
    @Autowired
    private SummerfarmMallFacade summerfarmMallFacade;
    @Autowired
    private TenantFacade tenantFacade;
    @Value("${tenant.xmTenantId}")
    private Long xmTenantId;
    @Autowired
    private SfMallManageFacade sfMallManageFacade;


    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("开始进行InvalidCostPriceUpdateProcessor任务");
        List<CostPrice> list = costPriceDomianService.listInvalidCostPrice();

        if (CollectionUtil.isNotEmpty(list)) {
            for (CostPrice costPrice : list) {
                String skuCode = costPrice.getSkuCode();
                Long tenantId = costPrice.getTenantId();
                String province = costPrice.getProvince();
                String city = costPrice.getCity();
                String area = costPrice.getArea();
                SummerFarmCostPriceVO summerFarmCostPriceVO = null;
                try {
                    TenantVO tenantVO = tenantFacade.getTenantByTenantId(tenantId);
                    Integer areaNo = summerfarmMallFacade.getAreaNoByAddress(province, city, area);
                    if (ObjectUtil.isNotNull(tenantVO) && ObjectUtil.isNotNull(areaNo)) {
                        summerFarmCostPriceVO = sfMallManageFacade.queryMallPriceInfo4Saas(areaNo, skuCode, tenantVO.getAdminId());
                        costPriceDomianService.synchronize4Summerfarm(skuCode, tenantId, area, city, province, summerFarmCostPriceVO);
                    } else {
                        log.error("过期任务变更 cost_price更新跳过，tenant空，或者areano空，tenantId={},province={},city={},area={}", tenantId, province, city, area);
                    }
                } catch (Exception e) {
                    costPriceDomianService.synchronize4Summerfarm(skuCode, tenantId, area, city, province, summerFarmCostPriceVO);
                    log.error("过期任务变更 cost_price更新失败，sku={},tenantId={},area={},city={},province={},summerFarmCostPriceVO={}", skuCode, tenantId, area, city, province, JSON.toJSONString(summerFarmCostPriceVO), e);
                }
            }
        }

        return new ProcessResult(true);
    }
}