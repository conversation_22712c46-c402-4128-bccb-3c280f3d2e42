package com.cosfo.item.web.facade;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cosfo.item.web.domain.vo.TenantVO;
import com.cosfo.manage.client.enums.ItemPriceRuleEnum;
import com.cosfo.manage.client.tenant.TenantProvider;
import com.cosfo.manage.client.tenant.req.TenantQueryReq;
import com.cosfo.manage.client.tenant.resp.TenantResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import com.cosfo.item.web.facade.converter.TenantConverter;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Deprecated
@Slf4j
@Component
public class TenantOldFacade {
    @DubboReference
    private TenantProvider tenantProvider;

    public TenantVO getTenantByAdminId(Long adminId) {
        if(ObjectUtil.isNull(adminId)){
            throw new ParamsException("adminId 不能为空");
        }

        TenantQueryReq req = new TenantQueryReq();
        req.setAdminId(adminId);
        DubboResponse<List<TenantResp>> response = tenantProvider.list(req);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<TenantResp> list = response.getData();
        if(!CollectionUtil.isEmpty(list)){
            return TenantConverter.tenantResp2VO(list.stream().findFirst().get());
        }
        return null;
    }
    public List<TenantVO> listAllTenant() {
        TenantQueryReq req = new TenantQueryReq();
        DubboResponse<List<TenantResp>> response = tenantProvider.list(req);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<TenantResp> list = response.getData();
        if(!CollectionUtil.isEmpty(list)){
            return list.stream().map(e-> TenantConverter.tenantResp2VO(e)).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    public List<String> listAddress(Long tenantId) {
        if(ObjectUtil.isEmpty (tenantId)){
            return Collections.emptyList ();
        }
        DubboResponse<List<String>> response = tenantProvider.listAddress(tenantId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        return response.getData().stream().filter (ObjectUtil::isNotNull).collect(Collectors.toList());
    }
    public TenantVO getTenantByTenantId(Long tenantId) {
        if(ObjectUtil.isNull(tenantId)){
            throw new ParamsException("tenantId 不能为空");
        }

        TenantQueryReq req = new TenantQueryReq();
        req.setTenantId(tenantId);
        DubboResponse<List<TenantResp>> response = tenantProvider.list(req);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        List<TenantResp> list = response.getData();
        if(!CollectionUtil.isEmpty(list)){
            return TenantConverter.tenantResp2VO(list.stream().findFirst().get());
        }
        return null;
    }
    public ItemPriceRuleEnum getItemPriceRule(Long tenantId){
        if(ObjectUtil.isNull(tenantId)){
            throw new ParamsException("tenantId 不能为空");
        }

        DubboResponse<ItemPriceRuleEnum> response = tenantProvider.getItemPriceRule (tenantId);
        if (!response.isSuccess()){
            throw new BizException(response.getMsg());
        }
        return response.getData();
    }
}
